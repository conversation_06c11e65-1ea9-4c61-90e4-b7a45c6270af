import { CompanyModel } from '../models/Company';
import { logger } from '../../../utils/logger';
import {
  Company,
  CreateCompanyRequest,
  UpdateCompanyRequest,
  CompanyQueryParams,
  CompanyStatistics
} from '../types/company';
import { ApiResponse, ApiMeta } from '@aperion/shared';
import { createSuccessResponse, createErrorResponse } from '../utils/apiResponse';

export class CompanyService {
  private companyModel: CompanyModel;

  constructor() {
    this.companyModel = new CompanyModel();
  }

  /**
   * Get all companies with filtering and pagination
   */
  async getCompanies(params: CompanyQueryParams): Promise<ApiResponse<{ companies: Company[]; meta: ApiMeta }>> {
    try {
      logger.info('Getting companies with params:', params);
      
      const result = await this.companyModel.getCompanies(params);
      
      logger.info('Successfully retrieved companies', {
        count: result.companies.length,
        total: result.meta.total
      });

      return createSuccessResponse(result);
    } catch (error) {
      logger.error('Error getting companies:', error);
      return createErrorResponse(
        'COMPANIES_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get companies'
      );
    }
  }

  /**
   * Get company by ID
   */
  async getCompanyById(id: string): Promise<ApiResponse<Company>> {
    try {
      logger.info('Getting company by ID:', { id });
      
      const company = await this.companyModel.getCompanyById(id);
      
      if (!company) {
        return createErrorResponse('COMPANY_NOT_FOUND', 'Company not found');
      }

      logger.info('Successfully retrieved company:', { id, name: company.name });

      return createSuccessResponse(company);
    } catch (error) {
      logger.error('Error getting company by ID:', error);
      return createErrorResponse(
        'COMPANY_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get company'
      );
    }
  }

  /**
   * Create a new company
   */
  async createCompany(companyData: CreateCompanyRequest): Promise<ApiResponse<Company>> {
    try {
      logger.info('Creating new company:', { name: companyData.name });
      
      const company = await this.companyModel.createCompany(companyData);
      
      logger.info('Successfully created company:', { id: company.id, name: company.name });

      return createSuccessResponse(company);
    } catch (error) {
      logger.error('Error creating company:', error);
      return createErrorResponse(
        'COMPANY_CREATE_ERROR',
        error instanceof Error ? error.message : 'Failed to create company'
      );
    }
  }

  /**
   * Update a company
   */
  async updateCompany(id: string, companyData: UpdateCompanyRequest): Promise<ApiResponse<Company>> {
    try {
      logger.info('Updating company:', { id, data: companyData });
      
      const company = await this.companyModel.updateCompany(id, companyData);
      
      if (!company) {
        return createErrorResponse('COMPANY_NOT_FOUND', 'Company not found');
      }

      logger.info('Successfully updated company:', { id, name: company.name });

      return createSuccessResponse(company);
    } catch (error) {
      logger.error('Error updating company:', error);
      return createErrorResponse(
        'COMPANY_UPDATE_ERROR',
        error instanceof Error ? error.message : 'Failed to update company'
      );
    }
  }

  /**
   * Delete a company
   */
  async deleteCompany(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    try {
      logger.info('Deleting company:', { id });
      
      const deleted = await this.companyModel.deleteCompany(id);
      
      if (!deleted) {
        return createErrorResponse('COMPANY_NOT_FOUND', 'Company not found');
      }

      logger.info('Successfully deleted company:', { id });

      return createSuccessResponse({ deleted: true });
    } catch (error) {
      logger.error('Error deleting company:', error);
      return createErrorResponse(
        'COMPANY_DELETE_ERROR',
        error instanceof Error ? error.message : 'Failed to delete company'
      );
    }
  }

  /**
   * Get company statistics
   */
  async getCompanyStatistics(): Promise<ApiResponse<CompanyStatistics>> {
    try {
      logger.info('Getting company statistics');
      
      const statistics = await this.companyModel.getCompanyStatistics();
      
      logger.info('Successfully retrieved company statistics:', {
        totalCompanies: statistics.totalCompanies
      });

      return createSuccessResponse(statistics);
    } catch (error) {
      logger.error('Error getting company statistics:', error);
      return createErrorResponse(
        'COMPANY_STATISTICS_ERROR',
        error instanceof Error ? error.message : 'Failed to get company statistics'
      );
    }
  }





  /**
   * Search companies by name or code
   */
  async searchCompanies(searchTerm: string, limit: number = 10): Promise<ApiResponse<Company[]>> {
    try {
      logger.info('Searching companies:', { searchTerm, limit });
      
      const params: CompanyQueryParams = {
        page: 1,
        limit,
        search: searchTerm,
        sortBy: 'name',
        sortOrder: 'asc'
      };

      const result = await this.companyModel.getCompanies(params);
      
      logger.info('Successfully searched companies:', {
        searchTerm,
        count: result.companies.length
      });

      return createSuccessResponse(result.companies);
    } catch (error) {
      logger.error('Error searching companies:', error);
      return createErrorResponse(
        'COMPANY_SEARCH_ERROR',
        error instanceof Error ? error.message : 'Failed to search companies'
      );
    }
  }
}
