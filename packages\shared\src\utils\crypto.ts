
import jwt from 'jsonwebtoken';
import { randomBytes, createHash } from 'crypto';
import { JWTPayload, ServiceToken } from '../types/user';



/**
 * JWT token utilities
 */
export class TokenUtils {
  /**
   * Generate a JWT token
   */
  static generateToken(
    payload: Omit<JWTPayload, 'iat' | 'exp'>,
    secret: string,
    expiresIn: string = '15m'
  ): string {
    const options = {
      expiresIn,
      issuer: payload.iss,
      audience: payload.aud,
    };
    return (jwt.sign as any)(payload, secret, options);
  }

  /**
   * Verify and decode a JWT token
   */
  static verifyToken(token: string, secret: string): JWTPayload {
    return jwt.verify(token, secret) as JWTPayload;
  }

  /**
   * Generate a service-to-service token
   */
  static generateServiceToken(
    sourceService: string,
    targetService: string,
    secret: string,
    expiresIn: string = '15m'
  ): string {
    const payload: Omit<ServiceToken, 'iat' | 'exp'> = {
      iss: 'aperion-api-gateway',
      aud: targetService,
      sub: sourceService,
      scope: 'internal-api',
    };

    return (jwt.sign as any)(payload, secret, { expiresIn });
  }

  /**
   * Verify a service token
   */
  static verifyServiceToken(token: string, secret: string): ServiceToken {
    return jwt.verify(token, secret) as ServiceToken;
  }

  /**
   * Decode token without verification (for debugging)
   */
  static decodeToken(token: string): any {
    return jwt.decode(token);
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as any;
      if (!decoded || !decoded.exp) {
        return true;
      }
      return Date.now() >= decoded.exp * 1000;
    } catch {
      return true;
    }
  }
}

/**
 * Activation code utilities
 */
export class ActivationCodeUtils {
  /**
   * Generate a secure activation code
   */
  static generateActivationCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';

    for (let i = 0; i < 8; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return code;
  }

  /**
   * Generate a signup link token
   */
  static generateSignupToken(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Hash an activation code for storage
   */
  static hashActivationCode(code: string): string {
    return createHash('sha256').update(code).digest('hex');
  }

  /**
   * Verify an activation code against a hash
   */
  static verifyActivationCode(code: string, hash: string): boolean {
    const codeHash = this.hashActivationCode(code);
    return codeHash === hash;
  }
}

/**
 * Encryption utilities for sensitive data
 */
export class EncryptionUtils {
  /**
   * Generate a random salt
   */
  static generateSalt(length: number = 32): string {
    return randomBytes(length).toString('hex');
  }

  /**
   * Generate a secure random string
   */
  static generateRandomString(length: number = 32): string {
    return randomBytes(length).toString('hex');
  }

  /**
   * Create a hash of data
   */
  static createHash(data: string, algorithm: string = 'sha256'): string {
    return createHash(algorithm).update(data).digest('hex');
  }

  /**
   * Create HMAC
   */
  static createHMAC(data: string, secret: string, algorithm: string = 'sha256'): string {
    const crypto = require('crypto');
    return crypto.createHmac(algorithm, secret).update(data).digest('hex');
  }

  /**
   * Generate a correlation ID for request tracking
   */
  static generateCorrelationId(): string {
    return randomBytes(16).toString('hex');
  }

  /**
   * Generate a request ID
   */
  static generateRequestId(): string {
    const timestamp = Date.now().toString(36);
    const random = randomBytes(8).toString('hex');
    return `req_${timestamp}_${random}`;
  }
}

/**
 * Session utilities
 */
export class SessionUtils {
  /**
   * Generate a session ID
   */
  static generateSessionId(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Generate a CSRF token
   */
  static generateCSRFToken(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Create a secure cookie value
   */
  static createSecureCookieValue(data: any, secret: string): string {
    const payload = JSON.stringify(data);
    const signature = EncryptionUtils.createHMAC(payload, secret);
    return `${Buffer.from(payload).toString('base64')}.${signature}`;
  }

  /**
   * Verify and parse a secure cookie value
   */
  static parseSecureCookieValue(cookieValue: string, secret: string): any | null {
    try {
      const [payloadBase64, signature] = cookieValue.split('.');
      const payload = Buffer.from(payloadBase64, 'base64').toString();
      const expectedSignature = EncryptionUtils.createHMAC(payload, secret);

      if (signature !== expectedSignature) {
        return null;
      }

      return JSON.parse(payload);
    } catch {
      return null;
    }
  }
}

/**
 * API key utilities
 */
export class ApiKeyUtils {
  /**
   * Generate an API key
   */
  static generateApiKey(): string {
    const prefix = 'ak_';
    const key = randomBytes(32).toString('hex');
    return `${prefix}${key}`;
  }

  /**
   * Generate an API secret
   */
  static generateApiSecret(): string {
    return randomBytes(64).toString('hex');
  }

  /**
   * Validate API key format
   */
  static validateApiKeyFormat(apiKey: string): boolean {
    return /^ak_[a-f0-9]{64}$/.test(apiKey);
  }
}

/**
 * Time-based utilities
 */
export class TimeUtils {
  /**
   * Generate a time-based token that expires
   */
  static generateTimedToken(expirationMinutes: number = 30): {
    token: string;
    expiresAt: Date;
  } {
    const token = randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000);

    return { token, expiresAt };
  }

  /**
   * Check if a timestamp is expired
   */
  static isExpired(expiresAt: Date): boolean {
    return new Date() > expiresAt;
  }

  /**
   * Get time until expiration in seconds
   */
  static getTimeUntilExpiration(expiresAt: Date): number {
    const now = new Date();
    const diff = expiresAt.getTime() - now.getTime();
    return Math.max(0, Math.floor(diff / 1000));
  }
}
