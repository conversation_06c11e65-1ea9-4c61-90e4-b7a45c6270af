import { Request, Response } from 'express';
import { SubscriptionService } from '../services/SubscriptionService';
import { logger } from '../../../utils/logger';
import { 
  createSubscriptionPlanSchema, 
  updateSubscriptionPlanSchema, 
  subscriptionPlanQuerySchema,
  SubscriptionPlanQueryParams 
} from '../types/subscription';
import { HttpStatus } from '@aperion/shared';

export class SubscriptionController {
  private subscriptionService: SubscriptionService;

  constructor() {
    this.subscriptionService = new SubscriptionService();
  }

  /**
   * Get all subscription plans with filtering and pagination
   */
  getSubscriptionPlans = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate and parse query parameters
      const validationResult = subscriptionPlanQuerySchema.safeParse(req.query);
      
      if (!validationResult.success) {
        logger.warn('Invalid query parameters for getSubscriptionPlans:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const params: SubscriptionPlanQueryParams = validationResult.data;
      const result = await this.subscriptionService.getSubscriptionPlans(params);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getSubscriptionPlans controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get subscription plan by ID
   */
  getSubscriptionPlanById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Subscription plan ID is required'
          }
        });
        return;
      }

      const result = await this.subscriptionService.getSubscriptionPlanById(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'SUBSCRIPTION_PLAN_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getSubscriptionPlanById controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Create a new subscription plan
   */
  createSubscriptionPlan = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request body
      const validationResult = createSubscriptionPlanSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for createSubscriptionPlan:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const planData = validationResult.data;
      const result = await this.subscriptionService.createSubscriptionPlan(planData);

      if (result.success) {
        res.status(HttpStatus.CREATED).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in createSubscriptionPlan controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Update a subscription plan
   */
  updateSubscriptionPlan = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Subscription plan ID is required'
          }
        });
        return;
      }

      // Validate request body
      const validationResult = updateSubscriptionPlanSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for updateSubscriptionPlan:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const planData = validationResult.data;
      const result = await this.subscriptionService.updateSubscriptionPlan(id, planData);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'SUBSCRIPTION_PLAN_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in updateSubscriptionPlan controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Delete a subscription plan
   */
  deleteSubscriptionPlan = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Subscription plan ID is required'
          }
        });
        return;
      }

      const result = await this.subscriptionService.deleteSubscriptionPlan(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'SUBSCRIPTION_PLAN_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in deleteSubscriptionPlan controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get subscription plan statistics
   */
  getSubscriptionPlanStatistics = async (_req: Request, res: Response): Promise<void> => {
    try {
      const result = await this.subscriptionService.getSubscriptionPlanStatistics();

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getSubscriptionPlanStatistics controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get active subscription plans
   */
  getActiveSubscriptionPlans = async (_req: Request, res: Response): Promise<void> => {
    try {
      const result = await this.subscriptionService.getActiveSubscriptionPlans();

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getActiveSubscriptionPlans controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Search subscription plans
   */
  searchSubscriptionPlans = async (req: Request, res: Response): Promise<void> => {
    try {
      const { q: searchTerm, limit } = req.query;

      if (!searchTerm || typeof searchTerm !== 'string') {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Search term is required'
          }
        });
        return;
      }

      const searchLimit = limit ? parseInt(limit as string, 10) : 10;
      const result = await this.subscriptionService.searchSubscriptionPlans(searchTerm, searchLimit);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in searchSubscriptionPlans controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };
}
