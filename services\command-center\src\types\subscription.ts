import { z } from 'zod';

// Subscription plan type enum
export type PlanType = 'basic' | 'premium' | 'enterprise' | 'custom';

// Subscription plan interface
export interface SubscriptionPlan {
  id: string;
  name: string;
  description?: string;
  planType: PlanType;
  priceMonthly?: number;
  priceYearly?: number;
  maxUsers?: number;
  features: string[]; // JSONB array
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Create subscription plan request schema
export const createSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Plan name is required').max(100, 'Plan name too long'),
  description: z.string().optional(),
  planType: z.enum(['basic', 'premium', 'enterprise', 'custom']),
  priceMonthly: z.number().min(0, 'Price must be positive').optional(),
  priceYearly: z.number().min(0, 'Price must be positive').optional(),
  maxUsers: z.number().min(1, 'Max users must be at least 1').optional(),
  features: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
});

// Update subscription plan request schema
export const updateSubscriptionPlanSchema = createSubscriptionPlanSchema.partial();

// Subscription plan query parameters schema
export const subscriptionPlanQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('10'),
  search: z.string().optional(),
  planType: z.enum(['basic', 'premium', 'enterprise', 'custom']).optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  sortBy: z.enum(['name', 'planType', 'priceMonthly', 'priceYearly', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Type definitions from schemas
export type CreateSubscriptionPlanRequest = z.infer<typeof createSubscriptionPlanSchema>;
export type UpdateSubscriptionPlanRequest = z.infer<typeof updateSubscriptionPlanSchema>;
export type SubscriptionPlanQueryParams = z.infer<typeof subscriptionPlanQuerySchema>;

// Subscription plan statistics interface
export interface SubscriptionPlanStatistics {
  totalPlans: number;
  activePlans: number;
  inactivePlans: number;
  plansByType: Array<{
    type: PlanType;
    count: number;
  }>;
  averagePriceMonthly: number;
  averagePriceYearly: number;
}

// User subscription assignment interface
export interface UserSubscriptionAssignment {
  id: string;
  userId: number;
  subscriptionPlanId: string;
  assignedAt: Date;
  expiresAt?: Date;
  assignedBy?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // Joined data
  subscriptionPlan?: SubscriptionPlan;
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
}

// Create user subscription assignment schema
export const createUserSubscriptionAssignmentSchema = z.object({
  userId: z.number().min(1, 'User ID is required'),
  subscriptionPlanId: z.string().uuid('Invalid subscription plan ID'),
  expiresAt: z.string().datetime().optional(),
  assignedBy: z.number().min(1).optional(),
});

// Update user subscription assignment schema
export const updateUserSubscriptionAssignmentSchema = z.object({
  expiresAt: z.string().datetime().optional(),
  isActive: z.boolean().optional(),
});

// Type definitions for assignment schemas
export type CreateUserSubscriptionAssignmentRequest = z.infer<typeof createUserSubscriptionAssignmentSchema>;
export type UpdateUserSubscriptionAssignmentRequest = z.infer<typeof updateUserSubscriptionAssignmentSchema>;
