import { Request, Response } from 'express';
import { AssignmentService } from '../services/AssignmentService';
import { logger } from '../../../utils/logger';
import { 
  createUserCompanyAssignmentSchema, 
  updateUserCompanyAssignmentSchema, 
  userCompanyAssignmentQuerySchema,
  UserCompanyAssignmentQueryParams 
} from '../types/assignment';
import {
  createUserSubscriptionAssignmentSchema,
  updateUserSubscriptionAssignmentSchema
} from '../types/subscription';
import { HttpStatus } from '@aperion/shared';

export class AssignmentController {
  private assignmentService: AssignmentService;

  constructor() {
    this.assignmentService = new AssignmentService();
  }

  // =====================================================
  // COMPANY ASSIGNMENTS
  // =====================================================

  /**
   * Get all user company assignments with filtering and pagination
   */
  getUserCompanyAssignments = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate and parse query parameters
      const validationResult = userCompanyAssignmentQuerySchema.safeParse(req.query);
      
      if (!validationResult.success) {
        logger.warn('Invalid query parameters for getUserCompanyAssignments:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const params: UserCompanyAssignmentQueryParams = validationResult.data;
      const result = await this.assignmentService.getUserCompanyAssignments(params);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getUserCompanyAssignments controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get user company assignment by ID
   */
  getUserCompanyAssignmentById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Assignment ID is required'
          }
        });
        return;
      }

      const result = await this.assignmentService.getUserCompanyAssignmentById(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_COMPANY_ASSIGNMENT_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getUserCompanyAssignmentById controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Create a new user company assignment
   */
  createUserCompanyAssignment = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request body
      const validationResult = createUserCompanyAssignmentSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for createUserCompanyAssignment:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const assignmentData = validationResult.data;
      const result = await this.assignmentService.createUserCompanyAssignment(assignmentData);

      if (result.success) {
        res.status(HttpStatus.CREATED).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in createUserCompanyAssignment controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Update a user company assignment
   */
  updateUserCompanyAssignment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Assignment ID is required'
          }
        });
        return;
      }

      // Validate request body
      const validationResult = updateUserCompanyAssignmentSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for updateUserCompanyAssignment:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const assignmentData = validationResult.data;
      const result = await this.assignmentService.updateUserCompanyAssignment(id, assignmentData);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_COMPANY_ASSIGNMENT_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in updateUserCompanyAssignment controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Delete a user company assignment
   */
  deleteUserCompanyAssignment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Assignment ID is required'
          }
        });
        return;
      }

      const result = await this.assignmentService.deleteUserCompanyAssignment(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_COMPANY_ASSIGNMENT_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in deleteUserCompanyAssignment controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  // =====================================================
  // SUBSCRIPTION ASSIGNMENTS
  // =====================================================

  /**
   * Get all user subscription assignments
   */
  getUserSubscriptionAssignments = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.query;
      let userIdNum: number | undefined;

      if (userId) {
        userIdNum = parseInt(userId as string, 10);
        if (isNaN(userIdNum)) {
          res.status(HttpStatus.BAD_REQUEST).json({
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: 'Invalid User ID format'
            }
          });
          return;
        }
      }

      const result = await this.assignmentService.getUserSubscriptionAssignments(userIdNum);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getUserSubscriptionAssignments controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Create a new user subscription assignment
   */
  createUserSubscriptionAssignment = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request body
      const validationResult = createUserSubscriptionAssignmentSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for createUserSubscriptionAssignment:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const assignmentData = validationResult.data;
      const result = await this.assignmentService.createUserSubscriptionAssignment(assignmentData);

      if (result.success) {
        res.status(HttpStatus.CREATED).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in createUserSubscriptionAssignment controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Update a user subscription assignment
   */
  updateUserSubscriptionAssignment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Assignment ID is required'
          }
        });
        return;
      }

      // Validate request body
      const validationResult = updateUserSubscriptionAssignmentSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for updateUserSubscriptionAssignment:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const assignmentData = validationResult.data;
      const result = await this.assignmentService.updateUserSubscriptionAssignment(id, assignmentData);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_SUBSCRIPTION_ASSIGNMENT_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in updateUserSubscriptionAssignment controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Delete a user subscription assignment
   */
  deleteUserSubscriptionAssignment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Assignment ID is required'
          }
        });
        return;
      }

      const result = await this.assignmentService.deleteUserSubscriptionAssignment(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_SUBSCRIPTION_ASSIGNMENT_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in deleteUserSubscriptionAssignment controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  // =====================================================
  // STATISTICS
  // =====================================================

  /**
   * Get assignment statistics
   */
  getAssignmentStatistics = async (_req: Request, res: Response): Promise<void> => {
    try {
      const result = await this.assignmentService.getAssignmentStatistics();

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getAssignmentStatistics controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };
}
