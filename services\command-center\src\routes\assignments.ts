import { Router } from 'express';
import { AssignmentController } from '../controllers/AssignmentController';
import { authorize } from '../middleware/auth';
import { UserRole } from '@aperion/shared';

/**
 * Assignment Management Router
 *
 * Handles user assignment operations including company assignments,
 * subscription assignments, and assignment statistics.
 *
 * Authentication: Required for all routes
 * Authorization: System Admin, Employer
 *
 * @routes
 * GET    /test                    - API health check
 * GET    /dev/companies           - Get company assignments (development)
 * GET    /dev/companies/:id       - Get company assignment by ID (development)
 * POST   /dev/companies           - Create company assignment (development)
 * PUT    /dev/companies/:id       - Update company assignment (development)
 * DELETE /dev/companies/:id       - Delete company assignment (development)
 * GET    /dev/subscriptions       - Get subscription assignments (development)
 * POST   /dev/subscriptions       - Create subscription assignment (development)
 * PUT    /dev/subscriptions/:id   - Update subscription assignment (development)
 * DELETE /dev/subscriptions/:id   - Delete subscription assignment (development)
 * GET    /dev/statistics          - Get assignment statistics (development)
 * GET    /companies               - Get company assignments (production)
 * GET    /companies/:id           - Get company assignment by ID (production)
 * POST   /companies               - Create company assignment (production)
 * PUT    /companies/:id           - Update company assignment (production)
 * DELETE /companies/:id           - Delete company assignment (production)
 * GET    /subscriptions           - Get subscription assignments (production)
 * POST   /subscriptions           - Create subscription assignment (production)
 * PUT    /subscriptions/:id       - Update subscription assignment (production)
 * DELETE /subscriptions/:id       - Delete subscription assignment (production)
 * GET    /statistics              - Get assignment statistics (production)
 */

const router = Router();
const assignmentController = new AssignmentController();

// =============================================================================
// PROTECTED ROUTES (Authentication & Authorization Required)
// =============================================================================

// Apply authorization middleware to all routes
// System admins and employers can manage assignments
router.use(authorize([UserRole.SYSTEM_ADMIN, UserRole.EMPLOYER]));

// =============================================================================
// PUBLIC ROUTES (Within Protected Context)
// =============================================================================

/**
 * @route   GET /test
 * @desc    API health check endpoint
 * @access  Private (System Admin, Employer)
 * @returns {Object} Health status and timestamp
 */
router.get('/test', (_req, res) => {
  res.json({
    success: true,
    message: 'Assignment management API is working!',
    timestamp: new Date().toISOString(),
    service: 'command-center',
    module: 'assignments',
  });
});

// =============================================================================
// DEVELOPMENT ENDPOINTS
// =============================================================================

// Company Assignment Operations (Development)

/**
 * @route   GET /dev/companies
 * @desc    Get user company assignments with filtering and pagination
 * @access  Private (System Admin, Employer)
 * @query   {number} page - Page number (default: 1)
 * @query   {number} limit - Items per page (default: 20)
 * @query   {string} userId - Filter by user ID
 * @query   {string} companyId - Filter by company ID
 */
router.get('/dev/companies', assignmentController.getUserCompanyAssignments);

/**
 * @route   GET /dev/companies/:id
 * @desc    Get company assignment by ID
 * @access  Private (System Admin, Employer)
 * @param   {string} id - Assignment ID
 * @returns {Object} Company assignment details
 */
router.get('/dev/companies/:id', assignmentController.getUserCompanyAssignmentById);

/**
 * @route   POST /dev/companies
 * @desc    Create new company assignment
 * @access  Private (System Admin, Employer)
 * @body    {Object} Assignment data
 * @returns {Object} Created assignment details
 */
router.post('/dev/companies', assignmentController.createUserCompanyAssignment);

/**
 * @route   PUT /dev/companies/:id
 * @desc    Update company assignment by ID
 * @access  Private (System Admin, Employer)
 * @param   {string} id - Assignment ID
 * @body    {Object} Updated assignment data
 * @returns {Object} Updated assignment details
 */
router.put('/dev/companies/:id', assignmentController.updateUserCompanyAssignment);

/**
 * @route   DELETE /dev/companies/:id
 * @desc    Delete company assignment by ID
 * @access  Private (System Admin, Employer)
 * @param   {string} id - Assignment ID
 * @returns {Object} Deletion confirmation
 */
router.delete('/dev/companies/:id', assignmentController.deleteUserCompanyAssignment);

// Subscription Assignment Operations (Development)

/**
 * @route   GET /dev/subscriptions
 * @desc    Get user subscription assignments with filtering and pagination
 * @access  Private (System Admin, Employer)
 * @query   {number} page - Page number (default: 1)
 * @query   {number} limit - Items per page (default: 20)
 * @query   {string} userId - Filter by user ID
 * @query   {string} subscriptionId - Filter by subscription ID
 */
router.get('/dev/subscriptions', assignmentController.getUserSubscriptionAssignments);

/**
 * @route   POST /dev/subscriptions
 * @desc    Create new subscription assignment
 * @access  Private (System Admin, Employer)
 * @body    {Object} Assignment data
 * @returns {Object} Created assignment details
 */
router.post('/dev/subscriptions', assignmentController.createUserSubscriptionAssignment);

/**
 * @route   PUT /dev/subscriptions/:id
 * @desc    Update subscription assignment by ID
 * @access  Private (System Admin, Employer)
 * @param   {string} id - Assignment ID
 * @body    {Object} Updated assignment data
 * @returns {Object} Updated assignment details
 */
router.put('/dev/subscriptions/:id', assignmentController.updateUserSubscriptionAssignment);

/**
 * @route   DELETE /dev/subscriptions/:id
 * @desc    Delete subscription assignment by ID
 * @access  Private (System Admin, Employer)
 * @param   {string} id - Assignment ID
 * @returns {Object} Deletion confirmation
 */
router.delete('/dev/subscriptions/:id', assignmentController.deleteUserSubscriptionAssignment);

// Statistics (Development)

/**
 * @route   GET /dev/statistics
 * @desc    Get assignment statistics and metrics
 * @access  Private (System Admin, Employer)
 * @returns {Object} Assignment statistics including counts and trends
 */
router.get('/dev/statistics', assignmentController.getAssignmentStatistics);

// =============================================================================
// PRODUCTION ENDPOINTS
// =============================================================================

// Company Assignment Operations (Production)

/**
 * @route   GET /companies
 * @desc    Get user company assignments with filtering and pagination
 * @access  Private (System Admin, Employer)
 */
router.get('/companies', assignmentController.getUserCompanyAssignments);

/**
 * @route   GET /companies/:id
 * @desc    Get company assignment by ID
 * @access  Private (System Admin, Employer)
 */
router.get('/companies/:id', assignmentController.getUserCompanyAssignmentById);

/**
 * @route   POST /companies
 * @desc    Create new company assignment
 * @access  Private (System Admin, Employer)
 */
router.post('/companies', assignmentController.createUserCompanyAssignment);

/**
 * @route   PUT /companies/:id
 * @desc    Update company assignment by ID
 * @access  Private (System Admin, Employer)
 */
router.put('/companies/:id', assignmentController.updateUserCompanyAssignment);

/**
 * @route   DELETE /companies/:id
 * @desc    Delete company assignment by ID
 * @access  Private (System Admin, Employer)
 */
router.delete('/companies/:id', assignmentController.deleteUserCompanyAssignment);

// Subscription Assignment Operations (Production)

/**
 * @route   GET /subscriptions
 * @desc    Get user subscription assignments with filtering and pagination
 * @access  Private (System Admin, Employer)
 */
router.get('/subscriptions', assignmentController.getUserSubscriptionAssignments);

/**
 * @route   POST /subscriptions
 * @desc    Create new subscription assignment
 * @access  Private (System Admin, Employer)
 */
router.post('/subscriptions', assignmentController.createUserSubscriptionAssignment);

/**
 * @route   PUT /subscriptions/:id
 * @desc    Update subscription assignment by ID
 * @access  Private (System Admin, Employer)
 */
router.put('/subscriptions/:id', assignmentController.updateUserSubscriptionAssignment);

/**
 * @route   DELETE /subscriptions/:id
 * @desc    Delete subscription assignment by ID
 * @access  Private (System Admin, Employer)
 */
router.delete('/subscriptions/:id', assignmentController.deleteUserSubscriptionAssignment);

// Statistics (Production)

/**
 * @route   GET /statistics
 * @desc    Get assignment statistics and metrics
 * @access  Private (System Admin only)
 */
router.get('/statistics', authorize([UserRole.SYSTEM_ADMIN]), assignmentController.getAssignmentStatistics);

export { router as assignmentsRouter };
