import pino from 'pino';
import path from 'path';
import fs from 'fs';
import { createStream } from 'rotating-file-stream';

// Define log levels
export const LOG_LEVELS = {
  fatal: 60,
  error: 50,
  warn: 40,
  info: 30,
  debug: 20,
  trace: 10,
} as const;

// Define service names for log file organization
export type ServiceName = 'api-gateway' | 'member' | 'employer' | 'wellness-central' | 'zenx-lms' | 'command-center';

// Logger configuration interface
export interface LoggerConfig {
  serviceName: ServiceName;
  logLevel?: keyof typeof LOG_LEVELS;
  enableConsole?: boolean;
  enableFileLogging?: boolean;
  logDirectory?: string;
  enablePrettyPrint?: boolean;
  enableLogRotation?: boolean;
  maxFileSize?: string;
  maxFiles?: number;
  enableDebugFile?: boolean;
}

// Enhanced context interface for comprehensive logging
export interface LogContext {
  requestId?: string | undefined;
  correlationId?: string | undefined;
  userId?: string | undefined;
  sessionId?: string | undefined;
  traceId?: string | undefined;
  operationId?: string | undefined;
  component?: string | undefined;
  operation?: string | undefined;
  step?: string | undefined;
  transactionId?: string | undefined;
  businessRuleId?: string | undefined;
  userJourneyStep?: string | undefined;
  [key: string]: any;
}

// Enhanced request context for comprehensive HTTP logging
export interface RequestContext {
  // Request identification
  requestId: string;
  correlationId: string;
  traceId?: string | undefined;

  // HTTP details
  method: string;
  url: string;
  path?: string | undefined;
  query?: any;
  params?: any;
  headers?: any;

  // Request body metadata
  bodySize?: number | undefined;
  contentType?: string | undefined;
  contentEncoding?: string | undefined;

  // Network information
  clientIp: string;
  userAgent: string;
  referer?: string | undefined;
  origin?: string | undefined;
  protocol: string;
  httpVersion?: string | undefined;

  // Security context
  authenticated?: boolean | undefined;
  authMethod?: string | undefined;
  permissions?: string[] | undefined;
  rateLimitRemaining?: number | undefined;
  suspiciousActivity?: boolean | undefined;

  // User context
  userId?: string | undefined;
  userRole?: string | undefined;
  userEmail?: string | undefined;
  sessionId?: string | undefined;

  // Performance context
  startTime: number;
  memoryUsageBefore?: number | undefined;
  cpuUsageBefore?: number | undefined;
}

// Enhanced response context for comprehensive HTTP logging
export interface ResponseContext {
  // Response details
  statusCode: number;
  statusMessage?: string;
  headers?: any;
  contentLength?: number;
  contentType?: string;
  cacheStatus?: string;
  compressionUsed?: boolean;

  // Performance metrics
  responseTime: number;
  memoryUsageAfter?: number;
  cpuUsageAfter?: number;
  memoryDelta?: number;

  // Database metrics
  dbQueryCount?: number;
  dbQueryTime?: number;
  dbConnectionsUsed?: number;

  // External API metrics
  externalApiCalls?: number;
  externalApiTime?: number;

  // Business metrics
  businessRulesExecuted?: string[];
  validationResults?: any;
  operationSuccess?: boolean;
  errorDetails?: any;
}

// Database operation context
export interface DatabaseContext {
  operation: string;
  table?: string | undefined;
  schema?: string | undefined;
  queryType: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'TRANSACTION';
  executionTime: number;
  rowsAffected?: number | undefined;
  connectionPoolStatus?: {
    total: number;
    idle: number;
    waiting: number;
  } | undefined;
  transactionId?: string | undefined;
  pgErrorCode?: string | undefined;
  queryHash?: string | undefined;
  retryAttempt?: number | undefined;
}

// Inter-service communication context
export interface ServiceCallContext {
  targetService: string;
  endpoint: string;
  method: string;
  requestId: string;
  correlationId: string;
  responseTime: number;
  statusCode: number;
  retryAttempt?: number;
  circuitBreakerState?: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  dependencyHealth?: 'HEALTHY' | 'DEGRADED' | 'UNHEALTHY';
}

// Enhanced logger interface with context support
export interface ContextualLogger extends pino.Logger {
  withContext(context: LogContext): ContextualLogger;
  setDefaultContext(context: LogContext): void;
  clearContext(): void;
}

// Default configuration with service-specific log directory
const DEFAULT_CONFIG: Required<Omit<LoggerConfig, 'serviceName'>> = {
  logLevel: 'info',
  enableConsole: true,
  enableFileLogging: true,
  logDirectory: '', // Will be set dynamically based on service name
  enablePrettyPrint: process.env.NODE_ENV !== 'production',
  enableLogRotation: true,
  maxFileSize: '10M',
  maxFiles: 7,
  enableDebugFile: process.env.NODE_ENV !== 'production',
};

/**
 * Create centralized service-specific log directory path
 * Creates: logs/{serviceName}/ under the project root for centralized logging
 * This follows microservices best practices for centralized log aggregation
 */
function createServiceLogDirectory(serviceName: ServiceName): string {
  // Always use centralized logging directory structure
  const projectRoot = process.cwd();

  // Handle case where service is running from its own directory
  // Navigate up to project root if we're in a service subdirectory
  let logRoot = projectRoot;
  if (projectRoot.includes('/services/')) {
    // We're running from within a service directory, go up to project root
    const pathParts = projectRoot.split('/');
    const servicesIndex = pathParts.lastIndexOf('services');
    if (servicesIndex > 0) {
      logRoot = pathParts.slice(0, servicesIndex).join('/');
    }
  }

  const centralizedLogDir = path.join(logRoot, 'logs', serviceName);
  return centralizedLogDir;
}

/**
 * Create logs directory if it doesn't exist
 */
function ensureLogDirectory(logDirectory: string): void {
  if (!fs.existsSync(logDirectory)) {
    fs.mkdirSync(logDirectory, { recursive: true });
  }
}

/**
 * Create rotating file stream for log files
 */
function createRotatingStream(filename: string, logDirectory: string, config: Required<Omit<LoggerConfig, 'serviceName'>>) {
  if (config.enableLogRotation) {
    return createStream(filename, {
      size: config.maxFileSize,
      interval: '1d',
      maxFiles: config.maxFiles,
      path: logDirectory,
      compress: 'gzip',
    });
  } else {
    return pino.destination({
      dest: path.join(logDirectory, filename),
      sync: false,
      mkdir: true,
    });
  }
}

/**
 * Get comprehensive system metrics for enhanced logging
 */
function getSystemMetrics(): any {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();

  return {
    memory: {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      heapUsedMB: Math.round(memUsage.heapUsed / 1024 / 1024 * 100) / 100,
      heapTotalMB: Math.round(memUsage.heapTotal / 1024 / 1024 * 100) / 100,
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system,
      userMs: Math.round(cpuUsage.user / 1000),
      systemMs: Math.round(cpuUsage.system / 1000),
    },
    process: {
      uptime: process.uptime(),
      pid: process.pid,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
    },
    timestamp: {
      iso: new Date().toISOString(),
      unix: Date.now(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    }
  };
}

/**
 * Create enhanced file formatter for comprehensive logging
 */
function createFileFormatter(serviceName: ServiceName) {
  return {
    level: (label: string) => ({ level: label }),
    bindings: (bindings: any) => ({
      pid: bindings.pid,
      hostname: bindings.hostname,
      service: serviceName,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.SERVICE_VERSION || '1.0.0',
      deployment: {
        region: process.env.AWS_REGION || process.env.REGION || 'local',
        stage: process.env.STAGE || 'development',
        buildId: process.env.BUILD_ID || 'local',
        commitHash: process.env.COMMIT_HASH || 'unknown',
      }
    }),
    log: (obj: any) => {
      // Enhance log object with comprehensive metadata for file logging
      const enhanced = {
        ...obj,

        // System metrics (for file logging only)
        systemMetrics: getSystemMetrics(),

        // Enhanced error context
        ...(obj.err && {
          errorContext: {
            name: obj.err.name,
            message: obj.err.message,
            stack: obj.err.stack,
            code: obj.err.code,
            errno: obj.err.errno,
            syscall: obj.err.syscall,
            path: obj.err.path,
            // PostgreSQL specific error details
            pgErrorCode: obj.err.code,
            pgSeverity: obj.err.severity,
            pgDetail: obj.err.detail,
            pgHint: obj.err.hint,
            pgPosition: obj.err.position,
            pgInternalPosition: obj.err.internalPosition,
            pgInternalQuery: obj.err.internalQuery,
            pgWhere: obj.err.where,
            pgSchemaName: obj.err.schema,
            pgTableName: obj.err.table,
            pgColumnName: obj.err.column,
            pgDataTypeName: obj.err.dataType,
            pgConstraintName: obj.err.constraint,
          }
        }),

        // Enhanced request context for HTTP logs
        ...(obj.req && {
          requestContext: {
            method: obj.req.method,
            url: obj.req.url,
            path: obj.req.path,
            query: obj.req.query,
            params: obj.req.params,
            headers: obj.req.headers,
            httpVersion: obj.req.httpVersion,
            protocol: obj.req.protocol,
            secure: obj.req.secure,
            ip: obj.req.ip,
            ips: obj.req.ips,
            subdomains: obj.req.subdomains,
            xhr: obj.req.xhr,
            fresh: obj.req.fresh,
            stale: obj.req.stale,
            bodySize: obj.req.headers?.['content-length'] ? parseInt(obj.req.headers['content-length']) : undefined,
            contentType: obj.req.headers?.['content-type'],
            contentEncoding: obj.req.headers?.['content-encoding'],
            userAgent: obj.req.headers?.['user-agent'],
            referer: obj.req.headers?.referer,
            origin: obj.req.headers?.origin,
            acceptLanguage: obj.req.headers?.['accept-language'],
            acceptEncoding: obj.req.headers?.['accept-encoding'],
            cacheControl: obj.req.headers?.['cache-control'],
            ifModifiedSince: obj.req.headers?.['if-modified-since'],
            ifNoneMatch: obj.req.headers?.['if-none-match'],
          }
        }),

        // Enhanced response context
        ...(obj.res && {
          responseContext: {
            statusCode: obj.res.statusCode,
            statusMessage: obj.res.statusMessage,
            headers: obj.res.headers || obj.res._headers,
            headersSent: obj.res.headersSent,
            finished: obj.res.finished,
            contentLength: obj.res.headers?.['content-length'] || obj.res._headers?.['content-length'],
            contentType: obj.res.headers?.['content-type'] || obj.res._headers?.['content-type'],
            cacheControl: obj.res.headers?.['cache-control'] || obj.res._headers?.['cache-control'],
            etag: obj.res.headers?.etag || obj.res._headers?.etag,
            lastModified: obj.res.headers?.['last-modified'] || obj.res._headers?.['last-modified'],
            location: obj.res.headers?.location || obj.res._headers?.location,
            setCookie: obj.res.headers?.['set-cookie'] || obj.res._headers?.['set-cookie'],
          }
        }),

        // Business context enhancement
        businessContext: {
          operation: obj.operation,
          step: obj.step,
          component: obj.component,
          transactionId: obj.transactionId,
          businessRuleId: obj.businessRuleId,
          userJourneyStep: obj.userJourneyStep,
          validationResults: obj.validationResults,
          businessRulesExecuted: obj.businessRulesExecuted,
          operationSuccess: obj.operationSuccess,
          retryAttempt: obj.retryAttempt,
          maxRetries: obj.maxRetries,
        },

        // Security context enhancement
        securityContext: {
          authenticated: obj.authenticated,
          authMethod: obj.authMethod,
          permissions: obj.permissions,
          rateLimitRemaining: obj.rateLimitRemaining,
          rateLimitHit: obj.rateLimitHit,
          suspiciousActivity: obj.suspiciousActivity,
          securityFlags: obj.securityFlags,
          ipWhitelisted: obj.ipWhitelisted,
          geoLocation: obj.geoLocation,
          deviceFingerprint: obj.deviceFingerprint,
        },

        // Performance metrics enhancement
        performanceMetrics: {
          responseTime: obj.responseTime,
          responseTimeMs: obj.responseTimeMs,
          dbQueryCount: obj.dbQueryCount,
          dbQueryTime: obj.dbQueryTime,
          dbConnectionsUsed: obj.dbConnectionsUsed,
          externalApiCalls: obj.externalApiCalls,
          externalApiTime: obj.externalApiTime,
          cacheHits: obj.cacheHits,
          cacheMisses: obj.cacheMisses,
          queueTime: obj.queueTime,
          processingTime: obj.processingTime,
        },

        // Database operation context
        ...(obj.dbOperation && {
          databaseContext: {
            operation: obj.dbOperation,
            table: obj.dbTable,
            schema: obj.dbSchema,
            queryType: obj.dbQueryType,
            executionTime: obj.dbExecutionTime,
            rowsAffected: obj.dbRowsAffected,
            connectionPoolStatus: obj.dbConnectionPoolStatus,
            transactionId: obj.dbTransactionId,
            queryHash: obj.dbQueryHash,
            indexesUsed: obj.dbIndexesUsed,
            planCost: obj.dbPlanCost,
          }
        }),

        // Inter-service communication context
        ...(obj.serviceCall && {
          serviceCallContext: {
            targetService: obj.targetService,
            endpoint: obj.serviceEndpoint,
            method: obj.serviceMethod,
            responseTime: obj.serviceResponseTime,
            statusCode: obj.serviceStatusCode,
            retryAttempt: obj.serviceRetryAttempt,
            circuitBreakerState: obj.circuitBreakerState,
            dependencyHealth: obj.dependencyHealth,
            loadBalancerNode: obj.loadBalancerNode,
          }
        }),

        // Audit trail enhancement
        auditTrail: {
          action: obj.auditAction,
          resource: obj.auditResource,
          resourceId: obj.auditResourceId,
          previousValue: obj.auditPreviousValue,
          newValue: obj.auditNewValue,
          reason: obj.auditReason,
          ipAddress: obj.auditIpAddress,
          userAgent: obj.auditUserAgent,
          sessionId: obj.auditSessionId,
          complianceFlags: obj.complianceFlags,
        },

        // Correlation and tracing
        tracing: {
          requestId: obj.requestId,
          correlationId: obj.correlationId,
          traceId: obj.traceId,
          spanId: obj.spanId,
          parentSpanId: obj.parentSpanId,
          operationId: obj.operationId,
          sessionId: obj.sessionId,
          userId: obj.userId,
          userRole: obj.userRole,
          userEmail: obj.userEmail,
        },

        // Metadata for log processing
        logMetadata: {
          logType: obj.logType || 'application',
          logVersion: '2.0',
          logFormat: 'enhanced-json',
          loggedAt: new Date().toISOString(),
          loggerVersion: '1.0.0',
          schemaVersion: '1.0',
        }
      };

      return enhanced;
    }
  };
}

/**
 * Create a contextual logger wrapper that ensures consistent fields
 */
function createContextualLogger(baseLogger: pino.Logger, serviceName: ServiceName): ContextualLogger {
  let defaultContext: LogContext = {
    service: serviceName,
    environment: process.env.NODE_ENV || 'development',
    version: process.env.SERVICE_VERSION || '1.0.0',
    nodeVersion: process.version,
    timestamp: () => new Date().toISOString(),
  };

  const contextualLogger = Object.create(baseLogger);

  // Override logging methods to include context
  const originalMethods = ['trace', 'debug', 'info', 'warn', 'error', 'fatal'];

  originalMethods.forEach(method => {
    contextualLogger[method] = function(msgOrObj: any, ...args: any[]) {
      let logObj: any = {};
      let message: string = '';

      // Handle different call patterns: logger.info(obj, msg) or logger.info(msg, obj)
      if (typeof msgOrObj === 'string') {
        message = msgOrObj;
        logObj = args[0] || {};
      } else if (typeof msgOrObj === 'object' && msgOrObj !== null) {
        logObj = msgOrObj;
        message = args[0] || logObj.msg || '';
      }

      // Merge default context with provided data
      const enrichedLog = {
        ...defaultContext,
        ...logObj,
        msg: message,
        loggedAt: new Date().toISOString(),
        // Add automatic fields (basic for console, enhanced for file via formatter)
        memoryUsage: process.memoryUsage().heapUsed,
        uptime: process.uptime(),
      };

      // Call original logger method with proper typing
      return (baseLogger as any)[method](enrichedLog);
    };
  });

  // Add context management methods
  contextualLogger.withContext = function(context: LogContext): ContextualLogger {
    const newLogger = createContextualLogger(baseLogger, serviceName);
    newLogger.setDefaultContext({ ...defaultContext, ...context });
    return newLogger;
  };

  contextualLogger.setDefaultContext = function(context: LogContext): void {
    defaultContext = { ...defaultContext, ...context };
  };

  contextualLogger.clearContext = function(): void {
    defaultContext = {
      service: serviceName,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.SERVICE_VERSION || '1.0.0',
      nodeVersion: process.version,
    };
  };

  return contextualLogger as ContextualLogger;
}

/**
 * Create Pino logger with enhanced file and console streams
 */
export function createLogger(config: LoggerConfig): ContextualLogger {
  // Set service-specific log directory if not explicitly provided
  const serviceLogDirectory = config.logDirectory || createServiceLogDirectory(config.serviceName);
  const finalConfig = { ...DEFAULT_CONFIG, ...config, logDirectory: serviceLogDirectory };

  // Ensure centralized service-specific log directory exists
  if (finalConfig.enableFileLogging) {
    ensureLogDirectory(finalConfig.logDirectory);
    console.log(`📁 Enhanced Pino logging initialized for ${config.serviceName} service`);
    console.log(`   🏢 Centralized log directory: ${finalConfig.logDirectory}`);
    console.log(`   📄 Log files: application.log, access.log, error.log${finalConfig.enableDebugFile ? ', debug.log' : ''}`);
    console.log(`   🎯 Strategy: Centralized microservices logging`);
  }

  const streams: any[] = [];

  // Console stream (maintain current pretty printing for development readability)
  if (finalConfig.enableConsole) {
    if (finalConfig.enablePrettyPrint) {
      const prettyStream = require('pino-pretty')({
        colorize: true,
        translateTime: 'HH:MM:ss.l',
        ignore: 'pid,hostname,service,environment,version,nodeVersion,loggedAt,memoryUsage,uptime,systemMetrics,requestContext,responseContext,businessContext,securityContext,performanceMetrics,databaseContext,serviceCallContext,auditTrail,tracing,logMetadata,errorContext',
        messageFormat: (log: any, messageKey: string) => {
          const msg = log[messageKey];

          // Create a concise console message with key context (maintain current format)
          let consoleMsg = msg;

          // Add key contextual information for console readability
          const contextParts: string[] = [];

          if (log.operation) contextParts.push(`op:${log.operation}`);
          if (log.component) contextParts.push(`${log.component}`);
          if (log.step) contextParts.push(`step:${log.step}`);
          if (log.requestId) contextParts.push(`req:${log.requestId.slice(-6)}`);
          if (log.method && log.url) contextParts.push(`${log.method} ${log.url}`);
          if (log.statusCode) contextParts.push(`${log.statusCode}`);
          if (log.responseTime) contextParts.push(`${log.responseTime}`);
          if (log.errorName) contextParts.push(`${log.errorName}`);
          if (log.pgErrorCode) contextParts.push(`PG:${log.pgErrorCode}`);

          if (contextParts.length > 0) {
            consoleMsg = `${msg} [${contextParts.join(' | ')}]`;
          }

          return consoleMsg;
        },
        customPrettifiers: {
          time: (timestamp: string) => `🕐 ${timestamp}`,
          level: (logLevel: string) => {
            const levelEmojis: Record<string, string> = {
              10: '🔍 TRACE',
              20: '🐛 DEBUG',
              30: 'ℹ️  INFO',
              40: '⚠️  WARN',
              50: '❌ ERROR',
              60: '💀 FATAL',
            };
            return levelEmojis[logLevel] || `📝 ${logLevel}`;
          },
          service: (serviceName: string) => `[${serviceName.toUpperCase()}]`,
        },
        // Custom colors for different log levels
        customColors: 'info:blue,warn:yellow,error:red,debug:green,trace:gray,fatal:magenta',
      });

      streams.push({
        level: finalConfig.logLevel,
        stream: prettyStream,
      });
    } else {
      // Simple console output for production
      streams.push({
        level: finalConfig.logLevel,
        stream: process.stdout,
      });
    }
  }

  // Enhanced file streams with comprehensive logging
  if (finalConfig.enableFileLogging) {
    // Use the service's local logs directory
    ensureLogDirectory(finalConfig.logDirectory);

    // Application log file with enhanced formatter for comprehensive data
    streams.push({
      level: finalConfig.logLevel,
      stream: createRotatingStream('application.log', finalConfig.logDirectory, finalConfig),
    });

    // Error-only log file with enhanced error context
    streams.push({
      level: 'error',
      stream: createRotatingStream('error.log', finalConfig.logDirectory, finalConfig),
    });

    // Debug log file with full system context
    if (finalConfig.enableDebugFile && (finalConfig.logLevel === 'debug' || finalConfig.logLevel === 'trace')) {
      streams.push({
        level: 'debug',
        stream: createRotatingStream('debug.log', finalConfig.logDirectory, finalConfig),
      });
    }
  }

  // Create base logger with enhanced formatters for file logging
  const baseLogger = pino(
    {
      level: finalConfig.logLevel,
      timestamp: pino.stdTimeFunctions.isoTime,
      formatters: createFileFormatter(finalConfig.serviceName),
      serializers: {
        req: pino.stdSerializers.req,
        res: pino.stdSerializers.res,
        err: pino.stdSerializers.err,
      },
      redact: {
        paths: [
          // Enhanced sensitive field redaction
          'password',
          'token',
          'apiKey',
          'ssn',
          'creditCard',
          'socialSecurityNumber',
          'bankAccount',
          'routingNumber',
          'pin',
          'secret',
          'privateKey',
          'accessToken',
          'refreshToken',
          'sessionToken',
          'authToken',
          'bearerToken',
          'apiSecret',
          'clientSecret',
          'webhookSecret',
          'encryptionKey',
          'signingKey',
          'masterKey',
          'databasePassword',
          'connectionString',
          'req.headers.authorization',
          'req.headers.cookie',
          'req.headers["x-api-key"]',
          'req.headers["x-auth-token"]',
          'req.headers["x-access-token"]',
          'req.headers["x-refresh-token"]',
          'req.headers["x-session-token"]',
          'requestContext.headers.authorization',
          'requestContext.headers.cookie',
          'requestContext.headers["x-api-key"]',
          'responseContext.headers["set-cookie"]',
          'auditTrail.previousValue.password',
          'auditTrail.newValue.password',
          'businessContext.sensitiveData',
          'securityContext.credentials',
        ],
        censor: '[REDACTED]',
      },
    },
    streams.length > 1 ? pino.multistream(streams) : streams[0]?.stream || process.stdout
  );

  // Return contextual logger with consistent fields
  return createContextualLogger(baseLogger, finalConfig.serviceName);
}

/**
 * Create a dedicated access logger for HTTP requests
 */
export function createAccessLogger(config: LoggerConfig): pino.Logger {
  // Set service-specific log directory if not explicitly provided
  const serviceLogDirectory = config.logDirectory || createServiceLogDirectory(config.serviceName);
  const finalConfig = { ...DEFAULT_CONFIG, ...config, logDirectory: serviceLogDirectory };

  if (!finalConfig.enableFileLogging) {
    // If file logging is disabled, return the main logger
    return createLogger(config);
  }

  // Ensure log directory exists
  ensureLogDirectory(finalConfig.logDirectory);

  const streams: any[] = [];

  // Console stream for development (access logs)
  if (finalConfig.enableConsole && finalConfig.enablePrettyPrint) {
    const accessPrettyStream = require('pino-pretty')({
      colorize: true,
      translateTime: 'HH:MM:ss.l',
      ignore: 'pid,hostname,service,environment,version,nodeVersion,loggedAt,memoryUsage,uptime,logType',
      messageFormat: (log: any, messageKey: string) => {
        const msg = log[messageKey];

        // Create concise HTTP access log for console
        if (log.method && log.url) {
          // Status color for potential future use
          // const statusColor = log.statusCode >= 500 ? 'red' :
          //                    log.statusCode >= 400 ? 'yellow' :
          //                    log.statusCode >= 300 ? 'cyan' : 'green';

          return `${log.method} ${log.url} → ${log.statusCode} ${log.responseTime || ''}`;
        }

        return msg;
      },
      customPrettifiers: {
        time: (timestamp: string) => `🌐 ${timestamp}`,
        level: (logLevel: string) => {
          const levelEmojis: Record<string, string> = {
            30: '📡 HTTP',
            40: '⚠️  HTTP',
            50: '❌ HTTP',
          };
          return levelEmojis[logLevel] || `🌐 HTTP`;
        },
      },
      customColors: 'info:cyan,warn:yellow,error:red',
    });

    streams.push({
      level: 'info',
      stream: accessPrettyStream,
    });
  }

  // Access log file (HTTP requests only)
  streams.push({
    level: 'info',
    stream: createRotatingStream('access.log', finalConfig.logDirectory, finalConfig),
  });

  // Error log file (for HTTP errors)
  streams.push({
    level: 'error',
    stream: createRotatingStream('error.log', finalConfig.logDirectory, finalConfig),
  });

  // Create dedicated access logger
  const accessLogger = pino(
    {
      level: 'info',
      timestamp: pino.stdTimeFunctions.isoTime,
      formatters: {
        level: (label: string) => ({ level: label }),
        bindings: (bindings: any) => ({
          pid: bindings.pid,
          hostname: bindings.hostname,
          service: finalConfig.serviceName,
          logType: 'access', // Mark as access log
        }),
      },
      serializers: {
        req: pino.stdSerializers.req,
        // Remove response serializer to avoid conflicts with pino-http
        // pino-http will handle response serialization with its own custom serializers
        err: pino.stdSerializers.err,
      },
      redact: {
        paths: [
          // Basic sensitive fields that are commonly present
          'password',
          'token',
          'apiKey',
          'ssn',
          'creditCard',
        ],
        censor: '[REDACTED]',
      },
    },
    streams.length > 1 ? pino.multistream(streams) : streams[0]?.stream || process.stdout
  );

  return accessLogger;
}









/**
 * Create enhanced HTTP logger middleware with comprehensive access logging
 */
export function createHttpLogger(_applicationLogger: pino.Logger, config: LoggerConfig) {
  // Set service-specific log directory if not explicitly provided
  const serviceLogDirectory = config.logDirectory || createServiceLogDirectory(config.serviceName);
  const finalConfig = { ...DEFAULT_CONFIG, ...config, logDirectory: serviceLogDirectory };

  // Ensure service-specific log directory exists
  ensureLogDirectory(finalConfig.logDirectory);

  const streams: any[] = [];

  // Console output for development (maintain current simple format)
  if (finalConfig.enableConsole && finalConfig.enablePrettyPrint) {
    const accessPrettyStream = require('pino-pretty')({
      colorize: true,
      translateTime: 'HH:MM:ss.l',
      ignore: 'pid,hostname,service,environment,version,logType,systemMetrics,requestContext,responseContext,businessContext,securityContext,performanceMetrics,auditTrail,tracing,logMetadata',
      messageFormat: (log: any, messageKey: string) => {
        const msg = log[messageKey];

        // Create concise HTTP access log for console (maintain current format)
        if (log.method && log.url) {
          return `${log.method} ${log.url} → ${log.statusCode} ${log.responseTime || ''}`;
        }

        return msg;
      },
      customPrettifiers: {
        time: (timestamp: string) => `🌐 ${timestamp}`,
        level: (logLevel: string) => {
          const levelEmojis: Record<string, string> = {
            30: '📡 HTTP',
            40: '⚠️  HTTP',
            50: '❌ HTTP',
          };
          return levelEmojis[logLevel] || `🌐 HTTP`;
        },
      },
      customColors: 'info:cyan,warn:yellow,error:red',
    });

    streams.push({
      level: 'info',
      stream: accessPrettyStream,
    });
  }

  // Enhanced access log file with comprehensive HTTP data
  streams.push({
    level: 'info',
    stream: createRotatingStream('access.log', finalConfig.logDirectory, finalConfig),
  });

  // Create enhanced HTTP logger with comprehensive file formatting
  const httpLogger = pino(
    {
      level: 'info',
      timestamp: pino.stdTimeFunctions.isoTime,
      formatters: createFileFormatter(finalConfig.serviceName),
      serializers: {
        req: pino.stdSerializers.req,
        res: pino.stdSerializers.res,
        err: pino.stdSerializers.err,
      },
      redact: {
        paths: [
          'password', 'token', 'apiKey', 'ssn', 'creditCard',
          'req.headers.authorization', 'req.headers.cookie',
          'req.headers["x-api-key"]', 'req.headers["x-auth-token"]',
          'requestContext.headers.authorization', 'requestContext.headers.cookie',
          'responseContext.headers["set-cookie"]', 'securityContext.credentials',
        ],
        censor: '[REDACTED]',
      },
    },
    streams.length > 1 ? pino.multistream(streams) : streams[0]?.stream || process.stdout
  );

  // Return enhanced middleware with comprehensive HTTP logging
  return (req: any, res: any, next: any) => {
    const startTime = Date.now();
    const memoryBefore = process.memoryUsage();
    const cpuBefore = process.cpuUsage();

    // Extract comprehensive request context
    const clientIp = req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
                    req.headers['x-real-ip'] ||
                    req.connection?.remoteAddress ||
                    req.socket?.remoteAddress ||
                    req.ip ||
                    'unknown';

    // Override res.end to capture comprehensive response data
    const originalEnd = res.end;
    res.end = function(chunk?: any, encoding?: any, cb?: any) {
      const responseTime = Date.now() - startTime;
      const memoryAfter = process.memoryUsage();
      const cpuAfter = process.cpuUsage(cpuBefore);

      // Determine log level based on status code
      const logLevel = res.statusCode >= 500 ? 'error' :
                      res.statusCode >= 400 ? 'warn' : 'info';

      // Extract response headers safely
      const responseHeaders: any = {};
      try {
        if (res.getHeaders && typeof res.getHeaders === 'function') {
          Object.assign(responseHeaders, res.getHeaders());
        } else if (res._headers) {
          Object.assign(responseHeaders, res._headers);
        }
      } catch (e) {
        // Ignore header extraction errors
      }

      // Calculate content length
      const contentLength = responseHeaders['content-length'] ||
                           (chunk ? Buffer.byteLength(chunk.toString()) : 0);

      // Log comprehensive HTTP request/response data
      httpLogger[logLevel](`${req.method} ${req.originalUrl || req.url} ${res.statusCode} ${responseTime}ms`, {
        // Core HTTP data
        method: req.method,
        url: req.originalUrl || req.url,
        path: req.path || req.route?.path,
        query: req.query,
        params: req.params,
        statusCode: res.statusCode,
        statusMessage: res.statusMessage,

        // Performance metrics
        responseTime: `${responseTime}ms`,
        responseTimeMs: responseTime,

        // Request tracking
        requestId: req.requestId || req.id,
        correlationId: req.correlationId,
        traceId: req.traceId,

        // Network and client information
        ip: clientIp,
        userAgent: req.headers['user-agent'] || 'unknown',
        protocol: req.protocol,
        httpVersion: req.httpVersion,
        secure: req.secure,

        // User context
        userId: req.user?.id || req.user?.sub,
        userRole: req.user?.role || req.user?.['custom:role'],
        userEmail: req.user?.email,
        sessionId: req.sessionId,

        // Request metadata for file logging (enhanced via formatter)
        req: req,
        res: res,

        // Enhanced context for comprehensive file logging
        logType: 'access',

        // Request body metadata
        bodySize: req.headers?.['content-length'] ? parseInt(req.headers['content-length']) : undefined,
        contentType: req.headers?.['content-type'],
        contentEncoding: req.headers?.['content-encoding'],

        // Response metadata
        responseContentLength: contentLength,
        responseContentType: responseHeaders['content-type'],
        cacheControl: responseHeaders['cache-control'],
        etag: responseHeaders.etag,

        // Security context
        authenticated: !!req.user,
        authMethod: req.authMethod,
        rateLimitRemaining: req.rateLimitRemaining,
        rateLimitHit: req.rateLimitHit,
        suspiciousActivity: req.suspiciousActivity,

        // Performance metrics (enhanced)
        memoryUsageBefore: memoryBefore.heapUsed,
        memoryUsageAfter: memoryAfter.heapUsed,
        memoryDelta: memoryAfter.heapUsed - memoryBefore.heapUsed,
        cpuUserTime: Math.round(cpuAfter.user / 1000),
        cpuSystemTime: Math.round(cpuAfter.system / 1000),

        // Database metrics (if available)
        dbQueryCount: req.dbQueryCount,
        dbQueryTime: req.dbQueryTime,
        dbConnectionsUsed: req.dbConnectionsUsed,

        // External API metrics (if available)
        externalApiCalls: req.externalApiCalls,
        externalApiTime: req.externalApiTime,

        // Business context
        operation: req.operation,
        component: req.component,
        businessRuleId: req.businessRuleId,

        // Audit trail
        auditAction: req.auditAction,
        auditResource: req.auditResource,
        auditResourceId: req.auditResourceId,

        // Timing details
        startTime: startTime,
        endTime: Date.now(),
        timestamp: new Date().toISOString(),
        loggedAt: new Date().toISOString(),
      });

      // Call original end method
      return originalEnd.call(this, chunk, encoding, cb);
    };

    next();
  };
}

/**
 * Utility function to create request ID
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Utility function to create correlation ID
 */
export function generateCorrelationId(): string {
  return `corr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Log rotation utility (for production use)
 */
export function setupLogRotation(logDirectory: string, serviceName: ServiceName) {
  // Log rotation is now handled automatically by rotating-file-stream
  // in the createRotatingStream function
  console.log(`Log rotation enabled for ${serviceName} in ${logDirectory} (10MB files, 7 files kept, gzip compression)`);
}

/**
 * Enhanced logging utilities for comprehensive context
 */
export const LoggingUtils = {
  /**
   * Create database operation context
   */
  createDatabaseContext(operation: string, table?: string | undefined, schema?: string | undefined): Partial<DatabaseContext> {
    return {
      operation,
      table: table || undefined,
      schema: schema || undefined,
      queryType: operation.toUpperCase().split(' ')[0] as any,
      executionTime: 0, // To be filled by caller
      transactionId: `txn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    };
  },

  /**
   * Create service call context
   */
  createServiceCallContext(targetService: string, endpoint: string, method: string): Partial<ServiceCallContext> {
    return {
      targetService,
      endpoint,
      method,
      requestId: generateRequestId(),
      correlationId: generateCorrelationId(),
      responseTime: 0, // To be filled by caller
      statusCode: 0, // To be filled by caller
    };
  },

  /**
   * Create business operation context
   */
  createBusinessContext(operation: string, step?: string | undefined, component?: string | undefined): LogContext {
    return {
      operation,
      step: step || undefined,
      component: component || undefined,
      operationId: `op_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      transactionId: `txn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      userJourneyStep: step || undefined,
    };
  },

  /**
   * Create security audit context
   */
  createSecurityContext(action: string, resource?: string, resourceId?: string): LogContext {
    return {
      auditAction: action,
      auditResource: resource,
      auditResourceId: resourceId,
      securityFlags: [],
      authenticated: false,
      suspiciousActivity: false,
    };
  },

  /**
   * Create performance metrics context
   */
  createPerformanceContext(): any {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      memoryUsageBefore: memUsage.heapUsed,
      cpuUsageBefore: cpuUsage,
      startTime: Date.now(),
    };
  },

  /**
   * Calculate performance delta
   */
  calculatePerformanceDelta(beforeContext: any): any {
    const memUsageAfter = process.memoryUsage();
    const cpuUsageAfter = process.cpuUsage(beforeContext.cpuUsageBefore);
    const endTime = Date.now();

    return {
      memoryUsageAfter: memUsageAfter.heapUsed,
      memoryDelta: memUsageAfter.heapUsed - beforeContext.memoryUsageBefore,
      cpuUserTime: Math.round(cpuUsageAfter.user / 1000),
      cpuSystemTime: Math.round(cpuUsageAfter.system / 1000),
      executionTime: endTime - beforeContext.startTime,
      endTime,
    };
  },

  /**
   * Enhance error with comprehensive context
   */
  enhanceError(error: Error, context: LogContext = {}): any {
    return {
      ...context,
      err: error,
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      errorCode: (error as any).code,
      pgErrorCode: (error as any).code,
      pgSeverity: (error as any).severity,
      pgDetail: (error as any).detail,
      pgHint: (error as any).hint,
      pgPosition: (error as any).position,
      pgWhere: (error as any).where,
      pgSchemaName: (error as any).schema,
      pgTableName: (error as any).table,
      pgColumnName: (error as any).column,
      pgConstraintName: (error as any).constraint,
      retryAttempt: context.retryAttempt || 0,
      maxRetries: context.maxRetries || 3,
    };
  },

  /**
   * Create comprehensive request context
   */
  createRequestContext(req: any): RequestContext {
    const clientIp = req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
                    req.headers['x-real-ip'] ||
                    req.connection?.remoteAddress ||
                    req.socket?.remoteAddress ||
                    req.ip ||
                    'unknown';

    return {
      requestId: req.requestId || generateRequestId(),
      correlationId: req.correlationId || generateCorrelationId(),
      traceId: req.traceId,
      method: req.method,
      url: req.originalUrl || req.url,
      path: req.path || req.route?.path,
      query: req.query,
      params: req.params,
      headers: req.headers,
      bodySize: req.headers?.['content-length'] ? parseInt(req.headers['content-length']) : undefined as number | undefined,
      contentType: req.headers?.['content-type'] || undefined,
      contentEncoding: req.headers?.['content-encoding'] || undefined,
      clientIp,
      userAgent: req.headers?.['user-agent'] || 'unknown',
      referer: req.headers?.referer,
      origin: req.headers?.origin,
      protocol: req.protocol,
      httpVersion: req.httpVersion,
      authenticated: !!req.user,
      authMethod: req.authMethod,
      permissions: req.permissions,
      rateLimitRemaining: req.rateLimitRemaining,
      suspiciousActivity: req.suspiciousActivity,
      userId: req.user?.id || req.user?.sub,
      userRole: req.user?.role || req.user?.['custom:role'],
      userEmail: req.user?.email,
      sessionId: req.sessionId,
      startTime: Date.now(),
      memoryUsageBefore: process.memoryUsage().heapUsed,
      cpuUsageBefore: process.cpuUsage().user,
    };
  },

  /**
   * Create comprehensive response context
   */
  createResponseContext(res: any, startTime: number, memoryBefore: number): ResponseContext {
    const responseHeaders: any = {};
    try {
      if (res.getHeaders && typeof res.getHeaders === 'function') {
        Object.assign(responseHeaders, res.getHeaders());
      } else if (res._headers) {
        Object.assign(responseHeaders, res._headers);
      }
    } catch (e) {
      // Ignore header extraction errors
    }

    const memoryAfter = process.memoryUsage().heapUsed;
    const responseTime = Date.now() - startTime;

    return {
      statusCode: res.statusCode,
      statusMessage: res.statusMessage,
      headers: responseHeaders,
      contentLength: responseHeaders['content-length'],
      contentType: responseHeaders['content-type'],
      cacheStatus: responseHeaders['cache-control'],
      compressionUsed: !!responseHeaders['content-encoding'],
      responseTime,
      memoryUsageAfter: memoryAfter,
      memoryDelta: memoryAfter - memoryBefore,
      dbQueryCount: res.dbQueryCount,
      dbQueryTime: res.dbQueryTime,
      dbConnectionsUsed: res.dbConnectionsUsed,
      externalApiCalls: res.externalApiCalls,
      externalApiTime: res.externalApiTime,
      businessRulesExecuted: res.businessRulesExecuted,
      validationResults: res.validationResults,
      operationSuccess: res.statusCode < 400,
      errorDetails: res.statusCode >= 400 ? {
        category: res.statusCode >= 500 ? 'server_error' : 'client_error',
        type: res.statusCode === 401 ? 'unauthorized' :
              res.statusCode === 403 ? 'forbidden' :
              res.statusCode === 404 ? 'not_found' :
              res.statusCode === 429 ? 'rate_limited' :
              res.statusCode >= 500 ? 'internal_error' : 'client_error',
      } : undefined,
    };
  },
};

/**
 * Export enhanced logger factory with comprehensive utilities
 */
export const LoggerFactory = {
  createLogger,
  createAccessLogger,
  createHttpLogger,
  generateRequestId,
  generateCorrelationId,
  setupLogRotation,
  utils: LoggingUtils,
};

export default LoggerFactory;
