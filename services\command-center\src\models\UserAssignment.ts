import { Pool } from 'pg';
import { config } from '../config';
import { logger } from '../../../utils/logger';
import {
  UserCompanyAssignment,
  CreateUserCompanyAssignmentRequest,
  UpdateUserCompanyAssignmentRequest,
  UserCompanyAssignmentQueryParams,
  AssignmentStatistics
} from '../types/assignment';
import {
  UserSubscriptionAssignment,
  CreateUserSubscriptionAssignmentRequest,
  UpdateUserSubscriptionAssignmentRequest
} from '../types/subscription';
import { ApiMeta } from '@aperion/shared';

export class UserAssignmentModel {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.user,
      password: config.database.password,
      ssl: config.database.ssl,
      max: config.database.maxConnections,
      idleTimeoutMillis: config.database.idleTimeoutMillis,
      connectionTimeoutMillis: config.database.connectionTimeoutMillis,
    });

    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
  }

  /**
   * Execute a database query
   */
  private async query(text: string, params?: any[]) {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      logger.debug('Executed query', { text, duration, rows: result.rowCount });
      return result;
    } catch (error) {
      logger.error('Query error', { text, params, error });
      throw error;
    }
  }

  // =====================================================
  // COMPANY ASSIGNMENTS
  // =====================================================

  /**
   * Get all user company assignments with filtering and pagination
   */
  async getUserCompanyAssignments(params: UserCompanyAssignmentQueryParams): Promise<{ assignments: UserCompanyAssignment[]; meta: ApiMeta }> {
    const { page, limit, userId, companyId, roleInCompany, isActive, assignedBy, dateFrom, dateTo, sortBy, sortOrder } = params;
    const offset = (page - 1) * limit;

    // Build WHERE clause
    const conditions: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (userId) {
      conditions.push(`uca.user_id = $${paramIndex}`);
      queryParams.push(userId);
      paramIndex++;
    }

    if (companyId) {
      conditions.push(`uca.company_id = $${paramIndex}`);
      queryParams.push(companyId);
      paramIndex++;
    }

    if (roleInCompany) {
      conditions.push(`uca.role_in_company ILIKE $${paramIndex}`);
      queryParams.push(`%${roleInCompany}%`);
      paramIndex++;
    }

    if (isActive !== undefined) {
      conditions.push(`uca.is_active = $${paramIndex}`);
      queryParams.push(isActive);
      paramIndex++;
    }

    if (assignedBy) {
      conditions.push(`uca.assigned_by = $${paramIndex}`);
      queryParams.push(assignedBy);
      paramIndex++;
    }

    if (dateFrom) {
      conditions.push(`uca.assigned_at >= $${paramIndex}`);
      queryParams.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      conditions.push(`uca.assigned_at <= $${paramIndex}`);
      queryParams.push(dateTo);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    const orderByClause = `ORDER BY uca.${sortBy === 'assignedAt' ? 'assigned_at' :
                                          sortBy === 'createdAt' ? 'created_at' :
                                          sortBy === 'updatedAt' ? 'updated_at' : sortBy} ${sortOrder}`;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM command_center.user_company_assignments uca
      ${whereClause}
    `;
    const countResult = await this.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get assignments with joined data
    const assignmentsQuery = `
      SELECT
        uca.id,
        uca.user_id as "userId",
        uca.company_id as "companyId",
        uca.role_in_company as "roleInCompany",
        uca.assigned_at as "assignedAt",
        uca.assigned_by as "assignedBy",
        uca.is_active as "isActive",
        uca.created_at as "createdAt",
        uca.updated_at as "updatedAt",
        json_build_object(
          'id', c.id,
          'name', c.name,
          'industry', c.industry,
          'status', c.status
        ) as company,
        json_build_object(
          'id', ur.id,
          'firstName', ur.first_name,
          'lastName', ur.last_name,
          'email', ur.email,
          'role', CASE
            WHEN ur.user_type = 'member' THEN 'member'
            WHEN ur.user_type = 'employer' THEN 'employer'
            WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
            WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
            WHEN ur.user_type = 'system_admin' THEN 'system-admin'
            ELSE ur.user_type
          END
        ) as user,
        json_build_object(
          'id', abu.id,
          'firstName', abu.first_name,
          'lastName', abu.last_name,
          'email', abu.email
        ) as "assignedByUser"
      FROM command_center.user_company_assignments uca
      LEFT JOIN command_center.companies c ON uca.company_id = c.id
      LEFT JOIN shared_data.user_references ur ON uca.user_id = ur.id
      LEFT JOIN shared_data.user_references abu ON uca.assigned_by = abu.id
      ${whereClause}
      ${orderByClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const result = await this.query(assignmentsQuery, queryParams);

    const meta: ApiMeta = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    };

    return {
      assignments: result.rows,
      meta,
    };
  }

  /**
   * Get user company assignment by ID
   */
  async getUserCompanyAssignmentById(id: string): Promise<UserCompanyAssignment | null> {
    const query = `
      SELECT
        uca.id,
        uca.user_id as "userId",
        uca.company_id as "companyId",
        uca.role_in_company as "roleInCompany",
        uca.assigned_at as "assignedAt",
        uca.assigned_by as "assignedBy",
        uca.is_active as "isActive",
        uca.created_at as "createdAt",
        uca.updated_at as "updatedAt",
        json_build_object(
          'id', c.id,
          'name', c.name,
          'industry', c.industry,
          'status', c.status
        ) as company,
        json_build_object(
          'id', ur.id,
          'firstName', ur.first_name,
          'lastName', ur.last_name,
          'email', ur.email,
          'role', CASE
            WHEN ur.user_type = 'member' THEN 'member'
            WHEN ur.user_type = 'employer' THEN 'employer'
            WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
            WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
            WHEN ur.user_type = 'system_admin' THEN 'system-admin'
            ELSE ur.user_type
          END
        ) as user,
        json_build_object(
          'id', abu.id,
          'firstName', abu.first_name,
          'lastName', abu.last_name,
          'email', abu.email
        ) as "assignedByUser"
      FROM command_center.user_company_assignments uca
      LEFT JOIN command_center.companies c ON uca.company_id = c.id
      LEFT JOIN shared_data.user_references ur ON uca.user_id = ur.id
      LEFT JOIN shared_data.user_references abu ON uca.assigned_by = abu.id
      WHERE uca.id = $1
    `;

    try {
      const result = await this.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error getting user company assignment by ID:', error);
      throw new Error('Failed to get user company assignment');
    }
  }

  /**
   * Create a new user company assignment
   */
  async createUserCompanyAssignment(assignmentData: CreateUserCompanyAssignmentRequest): Promise<UserCompanyAssignment> {
    const query = `
      INSERT INTO command_center.user_company_assignments (
        user_id,
        company_id,
        role_in_company,
        assigned_by
      ) VALUES ($1, $2, $3, $4)
      RETURNING
        id,
        user_id as "userId",
        company_id as "companyId",
        role_in_company as "roleInCompany",
        assigned_at as "assignedAt",
        assigned_by as "assignedBy",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, [
        assignmentData.userId,
        assignmentData.companyId,
        assignmentData.roleInCompany || null,
        assignmentData.assignedBy || null
      ]);

      return result.rows[0];
    } catch (error) {
      logger.error('Error creating user company assignment:', error);
      if ((error as any).code === '23505') {
        throw new Error('User is already assigned to this company');
      }
      throw new Error('Failed to create user company assignment');
    }
  }

  /**
   * Update a user company assignment
   */
  async updateUserCompanyAssignment(id: string, assignmentData: UpdateUserCompanyAssignmentRequest): Promise<UserCompanyAssignment | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(assignmentData).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = key === 'roleInCompany' ? 'role_in_company' :
                       key === 'isActive' ? 'is_active' : key;
        
        fields.push(`${dbField} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    // Add updated_at
    fields.push(`updated_at = NOW()`);
    values.push(id);

    const query = `
      UPDATE command_center.user_company_assignments
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING
        id,
        user_id as "userId",
        company_id as "companyId",
        role_in_company as "roleInCompany",
        assigned_at as "assignedAt",
        assigned_by as "assignedBy",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error updating user company assignment:', error);
      throw new Error('Failed to update user company assignment');
    }
  }

  /**
   * Delete a user company assignment
   */
  async deleteUserCompanyAssignment(id: string): Promise<boolean> {
    const query = 'DELETE FROM command_center.user_company_assignments WHERE id = $1';

    try {
      const result = await this.query(query, [id]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      logger.error('Error deleting user company assignment:', error);
      throw new Error('Failed to delete user company assignment');
    }
  }

  // =====================================================
  // SUBSCRIPTION ASSIGNMENTS
  // =====================================================

  /**
   * Get all user subscription assignments
   */
  async getUserSubscriptionAssignments(userId?: number): Promise<UserSubscriptionAssignment[]> {
    const whereClause = userId ? 'WHERE usa.user_id = $1' : '';
    const params = userId ? [userId] : [];

    const query = `
      SELECT
        usa.id,
        usa.user_id as "userId",
        usa.subscription_plan_id as "subscriptionPlanId",
        usa.assigned_at as "assignedAt",
        usa.expires_at as "expiresAt",
        usa.assigned_by as "assignedBy",
        usa.is_active as "isActive",
        usa.created_at as "createdAt",
        usa.updated_at as "updatedAt",
        json_build_object(
          'id', sp.id,
          'name', sp.name,
          'description', sp.description,
          'planType', sp.plan_type,
          'priceMonthly', sp.price_monthly,
          'priceYearly', sp.price_yearly,
          'maxUsers', sp.max_users,
          'features', sp.features,
          'isActive', sp.is_active
        ) as "subscriptionPlan",
        json_build_object(
          'id', ur.id,
          'firstName', ur.first_name,
          'lastName', ur.last_name,
          'email', ur.email
        ) as user
      FROM command_center.user_subscription_assignments usa
      LEFT JOIN command_center.subscription_plans sp ON usa.subscription_plan_id = sp.id
      LEFT JOIN shared_data.user_references ur ON usa.user_id = ur.id
      ${whereClause}
      ORDER BY usa.assigned_at DESC
    `;

    try {
      const result = await this.query(query, params);
      return result.rows;
    } catch (error) {
      logger.error('Error getting user subscription assignments:', error);
      throw new Error('Failed to get user subscription assignments');
    }
  }

  /**
   * Create a new user subscription assignment
   */
  async createUserSubscriptionAssignment(assignmentData: CreateUserSubscriptionAssignmentRequest): Promise<UserSubscriptionAssignment> {
    // First, deactivate any existing active subscription for this user
    await this.query(
      'UPDATE command_center.user_subscription_assignments SET is_active = false WHERE user_id = $1 AND is_active = true',
      [assignmentData.userId]
    );

    const query = `
      INSERT INTO command_center.user_subscription_assignments (
        user_id,
        subscription_plan_id,
        expires_at,
        assigned_by
      ) VALUES ($1, $2, $3, $4)
      RETURNING
        id,
        user_id as "userId",
        subscription_plan_id as "subscriptionPlanId",
        assigned_at as "assignedAt",
        expires_at as "expiresAt",
        assigned_by as "assignedBy",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, [
        assignmentData.userId,
        assignmentData.subscriptionPlanId,
        assignmentData.expiresAt || null,
        assignmentData.assignedBy || null
      ]);

      return result.rows[0];
    } catch (error) {
      logger.error('Error creating user subscription assignment:', error);
      throw new Error('Failed to create user subscription assignment');
    }
  }

  /**
   * Update a user subscription assignment
   */
  async updateUserSubscriptionAssignment(id: string, assignmentData: UpdateUserSubscriptionAssignmentRequest): Promise<UserSubscriptionAssignment | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(assignmentData).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = key === 'expiresAt' ? 'expires_at' :
                       key === 'isActive' ? 'is_active' : key;

        fields.push(`${dbField} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    // Add updated_at
    fields.push(`updated_at = NOW()`);
    values.push(id);

    const query = `
      UPDATE command_center.user_subscription_assignments
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING
        id,
        user_id as "userId",
        subscription_plan_id as "subscriptionPlanId",
        assigned_at as "assignedAt",
        expires_at as "expiresAt",
        assigned_by as "assignedBy",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error updating user subscription assignment:', error);
      throw new Error('Failed to update user subscription assignment');
    }
  }

  /**
   * Delete a user subscription assignment
   */
  async deleteUserSubscriptionAssignment(id: string): Promise<boolean> {
    const query = 'DELETE FROM command_center.user_subscription_assignments WHERE id = $1';

    try {
      const result = await this.query(query, [id]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      logger.error('Error deleting user subscription assignment:', error);
      throw new Error('Failed to delete user subscription assignment');
    }
  }

  // =====================================================
  // STATISTICS
  // =====================================================

  /**
   * Get assignment statistics
   */
  async getAssignmentStatistics(): Promise<AssignmentStatistics> {
    const queries = [
      // Company assignment counts
      `SELECT
         COUNT(*) as total,
         COUNT(*) FILTER (WHERE is_active = true) as active,
         COUNT(*) FILTER (WHERE is_active = false) as inactive
       FROM command_center.user_company_assignments`,

      // Assignments by company
      `SELECT
         uca.company_id as "companyId",
         c.name as "companyName",
         COUNT(*) as "userCount"
       FROM command_center.user_company_assignments uca
       LEFT JOIN command_center.companies c ON uca.company_id = c.id
       WHERE uca.is_active = true
       GROUP BY uca.company_id, c.name
       ORDER BY "userCount" DESC
       LIMIT 10`,

      // Assignments by role
      `SELECT
         role_in_company as role,
         COUNT(*) as count
       FROM command_center.user_company_assignments
       WHERE is_active = true AND role_in_company IS NOT NULL
       GROUP BY role_in_company
       ORDER BY count DESC`,

      // Recent assignments
      `SELECT
         uca.id,
         uca.user_id as "userId",
         ur.first_name || ' ' || ur.last_name as "userName",
         c.name as "companyName",
         uca.role_in_company as "roleInCompany",
         uca.assigned_at as "assignedAt"
       FROM command_center.user_company_assignments uca
       LEFT JOIN shared_data.user_references ur ON uca.user_id = ur.id
       LEFT JOIN command_center.companies c ON uca.company_id = c.id
       WHERE uca.is_active = true
       ORDER BY uca.assigned_at DESC
       LIMIT 10`
    ];

    try {
      const [statusResult, companyResult, roleResult, recentResult] = await Promise.all(
        queries.map(query => this.query(query))
      );

      const statusData = statusResult.rows[0];

      return {
        totalAssignments: parseInt(statusData.total),
        activeAssignments: parseInt(statusData.active),
        inactiveAssignments: parseInt(statusData.inactive),
        assignmentsByCompany: companyResult.rows,
        assignmentsByRole: roleResult.rows,
        recentAssignments: recentResult.rows,
      };
    } catch (error) {
      logger.error('Error getting assignment statistics:', error);
      throw new Error('Failed to get assignment statistics');
    }
  }
}
