-- =====================================================
-- LMS SERVICE SCHEMA
-- =====================================================

-- Learning Modules Table
CREATE TABLE lms_service.learning_modules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL,
  subcategory VARCHAR(100),
  difficulty_level VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
  estimated_duration_minutes INTEGER NOT NULL,
  learning_objectives TEXT[] DEFAULT ARRAY[]::TEXT[],
  prerequisites TEXT[] DEFAULT ARRAY[]::TEXT[],
  tags TEXT[] DEFAULT ARRAY[]::TEXT[],
  content_type VARCHAR(50) NOT NULL CHECK (content_type IN ('video', 'interactive', 'document', 'quiz', 'simulation', 'mixed')),
  content_url VARCHAR(500),
  thumbnail_url VARCHAR(500),
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived')),
  version INTEGER DEFAULT 1,
  created_by UUID NOT NULL, -- References LMS creator
  published_at TIMESTAMP,
  featured BOOLEAN DEFAULT false,
  rating DECIMAL(3,2) DEFAULT 0,
  total_ratings INTEGER DEFAULT 0,
  total_enrollments INTEGER DEFAULT 0,
  completion_rate DECIMAL(5,2) DEFAULT 0,
  content_metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Learning Paths Table
CREATE TABLE lms_service.learning_paths (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL,
  difficulty_level VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
  estimated_duration_hours INTEGER,
  modules JSONB NOT NULL DEFAULT '[]', -- Array of module IDs with order
  prerequisites TEXT[] DEFAULT ARRAY[]::TEXT[],
  learning_outcomes TEXT[] DEFAULT ARRAY[]::TEXT[],
  certificate_template_url VARCHAR(500),
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  created_by UUID NOT NULL,
  featured BOOLEAN DEFAULT false,
  total_enrollments INTEGER DEFAULT 0,
  completion_rate DECIMAL(5,2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Learner Enrollments Table
CREATE TABLE lms_service.learner_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  learner_id UUID NOT NULL, -- References member or coach
  learner_type VARCHAR(50) NOT NULL CHECK (learner_type IN ('member', 'wellness_coach', 'employer', 'admin')),
  module_id UUID REFERENCES lms_service.learning_modules(id) ON DELETE CASCADE,
  learning_path_id UUID REFERENCES lms_service.learning_paths(id) ON DELETE CASCADE,
  enrolled_at TIMESTAMP DEFAULT NOW(),
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'enrolled' CHECK (status IN ('enrolled', 'in_progress', 'completed', 'dropped', 'paused')),
  last_accessed_at TIMESTAMP,
  total_time_spent_minutes INTEGER DEFAULT 0,
  completion_certificate_url VARCHAR(500),
  final_score DECIMAL(5,2),
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Learning Progress Tracking Table
CREATE TABLE lms_service.learning_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  enrollment_id UUID REFERENCES lms_service.learner_enrollments(id) ON DELETE CASCADE,
  learner_id UUID NOT NULL,
  module_id UUID REFERENCES lms_service.learning_modules(id) ON DELETE CASCADE,
  section_id VARCHAR(100), -- For tracking progress within modules
  progress_type VARCHAR(50) NOT NULL CHECK (progress_type IN ('started', 'checkpoint', 'quiz_attempt', 'completed')),
  progress_data JSONB DEFAULT '{}',
  time_spent_minutes INTEGER DEFAULT 0,
  score DECIMAL(5,2),
  max_score DECIMAL(5,2),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Assessments Table
CREATE TABLE lms_service.assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  module_id UUID REFERENCES lms_service.learning_modules(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  assessment_type VARCHAR(50) NOT NULL CHECK (assessment_type IN ('quiz', 'assignment', 'practical', 'survey')),
  questions JSONB NOT NULL DEFAULT '[]',
  passing_score DECIMAL(5,2) DEFAULT 70,
  max_attempts INTEGER DEFAULT 3,
  time_limit_minutes INTEGER,
  randomize_questions BOOLEAN DEFAULT false,
  show_correct_answers BOOLEAN DEFAULT true,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
  created_by UUID NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Assessment Attempts Table
CREATE TABLE lms_service.assessment_attempts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assessment_id UUID REFERENCES lms_service.assessments(id) ON DELETE CASCADE,
  enrollment_id UUID REFERENCES lms_service.learner_enrollments(id) ON DELETE CASCADE,
  learner_id UUID NOT NULL,
  attempt_number INTEGER NOT NULL,
  started_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP,
  answers JSONB DEFAULT '{}',
  score DECIMAL(5,2),
  max_score DECIMAL(5,2),
  passed BOOLEAN DEFAULT false,
  time_spent_minutes INTEGER,
  status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'abandoned')),
  created_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR LMS SERVICE
-- =====================================================

CREATE INDEX idx_modules_category ON lms_service.learning_modules(category);
CREATE INDEX idx_modules_status ON lms_service.learning_modules(status);
CREATE INDEX idx_modules_featured ON lms_service.learning_modules(featured);
CREATE INDEX idx_modules_tags ON lms_service.learning_modules USING GIN(tags);
CREATE INDEX idx_modules_difficulty ON lms_service.learning_modules(difficulty_level);
CREATE INDEX idx_modules_rating ON lms_service.learning_modules(rating);

CREATE INDEX idx_paths_category ON lms_service.learning_paths(category);
CREATE INDEX idx_paths_status ON lms_service.learning_paths(status);
CREATE INDEX idx_paths_featured ON lms_service.learning_paths(featured);

CREATE INDEX idx_enrollments_learner ON lms_service.learner_enrollments(learner_id, learner_type);
CREATE INDEX idx_enrollments_module ON lms_service.learner_enrollments(module_id);
CREATE INDEX idx_enrollments_path ON lms_service.learner_enrollments(learning_path_id);
CREATE INDEX idx_enrollments_status ON lms_service.learner_enrollments(status);
CREATE INDEX idx_enrollments_enrolled_at ON lms_service.learner_enrollments(enrolled_at);

CREATE INDEX idx_progress_enrollment ON lms_service.learning_progress(enrollment_id);
CREATE INDEX idx_progress_learner ON lms_service.learning_progress(learner_id);
CREATE INDEX idx_progress_module ON lms_service.learning_progress(module_id);
CREATE INDEX idx_progress_type ON lms_service.learning_progress(progress_type);

CREATE INDEX idx_assessments_module ON lms_service.assessments(module_id);
CREATE INDEX idx_assessments_type ON lms_service.assessments(assessment_type);
CREATE INDEX idx_assessments_status ON lms_service.assessments(status);

CREATE INDEX idx_attempts_assessment ON lms_service.assessment_attempts(assessment_id);
CREATE INDEX idx_attempts_enrollment ON lms_service.assessment_attempts(enrollment_id);
CREATE INDEX idx_attempts_learner ON lms_service.assessment_attempts(learner_id);
CREATE INDEX idx_attempts_status ON lms_service.assessment_attempts(status);

-- =====================================================
-- TRIGGERS FOR LMS SERVICE
-- =====================================================

CREATE TRIGGER update_modules_updated_at 
  BEFORE UPDATE ON lms_service.learning_modules 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_paths_updated_at 
  BEFORE UPDATE ON lms_service.learning_paths 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessments_updated_at 
  BEFORE UPDATE ON lms_service.assessments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
