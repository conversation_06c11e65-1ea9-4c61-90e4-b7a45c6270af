import { Router, Request, Response } from 'express';
import { HealthCheckResponse, asyncHandler } from '@aperion/shared';
import { checkAllServicesHealth } from '../middleware/proxy';
import { logger } from '../utils/logger';

const router = Router();

// Gateway health check
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();

  try {
    // Check all downstream services
    const serviceHealthChecks = await checkAllServicesHealth();
    const responseTime = Date.now() - startTime;

    // Determine overall health
    const allHealthy = serviceHealthChecks.every(service => service.status === 'healthy');

    const healthCheck: HealthCheckResponse = {
      status: allHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'api-gateway',
      version: '1.0.0',
      uptime: process.uptime(),
      dependencies: serviceHealthChecks,
    };

    // Log health check
    logger.info('Health check performed', {
      requestId: req.requestId,
      overallStatus: healthCheck.status,
      responseTime: `${responseTime}ms`,
      serviceCount: serviceHealthChecks.length,
      healthyServices: serviceHealthChecks.filter(s => s.status === 'healthy').length,
    });

    // Return appropriate status code
    const statusCode = allHealthy ? 200 : 503;
    res.status(statusCode).json(healthCheck);

  } catch (error) {
    logger.error('Health check failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const healthCheck: HealthCheckResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'api-gateway',
      version: '1.0.0',
      uptime: process.uptime(),
    };

    res.status(503).json(healthCheck);
  }
}));

// Detailed health check with service breakdown
router.get('/detailed', asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();

  try {
    const serviceHealthChecks = await checkAllServicesHealth();
    const responseTime = Date.now() - startTime;

    const healthyServices = serviceHealthChecks.filter(s => s.status === 'healthy');
    const unhealthyServices = serviceHealthChecks.filter(s => s.status === 'unhealthy');

    const detailedHealth = {
      status: unhealthyServices.length === 0 ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'api-gateway',
      version: '1.0.0',
      uptime: process.uptime(),
      responseTime: `${responseTime}ms`,
      summary: {
        total: serviceHealthChecks.length,
        healthy: healthyServices.length,
        unhealthy: unhealthyServices.length,
      },
      services: {
        healthy: healthyServices,
        unhealthy: unhealthyServices,
      },
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        },
        cpu: process.cpuUsage(),
      },
    };

    const statusCode = unhealthyServices.length === 0 ? 200 : 503;
    res.status(statusCode).json(detailedHealth);

  } catch (error) {
    logger.error('Detailed health check failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'api-gateway',
      error: 'Health check failed',
    });
  }
}));

// Readiness probe (for Kubernetes)
router.get('/ready', asyncHandler(async (_req: Request, res: Response) => {
  try {
    // Check if gateway is ready to serve traffic
    const serviceHealthChecks = await checkAllServicesHealth();
    const criticalServicesHealthy = serviceHealthChecks
      .filter(service => ['member-service', 'employer-service'].includes(service.name))
      .every(service => service.status === 'healthy');

    if (criticalServicesHealthy) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        reason: 'Critical services unavailable',
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}));

// Liveness probe (for Kubernetes)
router.get('/live', (_req: Request, res: Response) => {
  // Simple liveness check - if this endpoint responds, the service is alive
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

export { router as healthRouter };
