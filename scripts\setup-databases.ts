#!/usr/bin/env tsx

import { Client, ClientConfig } from 'pg';
import * as fs from 'fs';
import * as path from 'path';

// Single Database Configuration
interface DatabaseConfig extends ClientConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
}

const databaseConfig: DatabaseConfig = {
  host: process.env.DB_HOST || '**************',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'AperionHealth-Dev',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'Cloud@2025',
  ssl: process.env.DB_SSL === 'true'
};

// Schemas to create
const schemas: string[] = [
  'shared_data',
  'member_service',
  'employer_service',
  'wellness_service',
  'lms_service',
  'command_center'
];

async function setupDatabase(): Promise<void> {
  console.log('🚀 Setting up Aperion Health single database...\n');
  console.log(`📊 Connecting to database: ${databaseConfig.database}`);

  const client = new Client(databaseConfig);

  try {
    await client.connect();
    console.log(`✅ Connected to ${databaseConfig.database}`);

    // Enable required extensions
    await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
    console.log(`   📦 Enabled uuid-ossp extension`);

    await client.query('CREATE EXTENSION IF NOT EXISTS "btree_gin"');
    console.log(`   📦 Enabled btree_gin extension`);

    await client.query('CREATE EXTENSION IF NOT EXISTS "pgcrypto"');
    console.log(`   📦 Enabled pgcrypto extension`);

    // Create schemas
    for (const schema of schemas) {
      await client.query(`CREATE SCHEMA IF NOT EXISTS ${schema}`);
      console.log(`   📁 Created schema: ${schema}`);
    }

    // Create update function for timestamps
    await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);
    console.log(`   ⚙️ Created update_updated_at_column function`);

    console.log(`✅ Database ${databaseConfig.database} setup completed\n`);

  } catch (error: any) {
    console.error(`❌ Error setting up ${databaseConfig.database}:`, error.message);

    if (error.code === 'ECONNREFUSED') {
      console.error('   💡 Make sure PostgreSQL is running and accessible');
    } else if (error.code === '3D000') {
      console.error(`   💡 Database ${databaseConfig.database} doesn't exist. Please create it first.`);
    } else if (error.code === '28P01') {
      console.error(`   💡 Authentication failed for user ${databaseConfig.user}. Check credentials.`);
    }
    throw error;

  } finally {
    await client.end();
  }

  console.log('🎉 Database setup completed!');
  console.log('\n📋 Next steps:');
  console.log('   1. Run: npm run db:migrate (to create tables)');
  console.log('   2. Run: npm run db:test (to verify connection)');
  console.log('   3. Run: npm run dev (to start services)');
}

// Create SQL script for manual database creation
function createDatabaseScript(): void {
  const sqlScript = `-- Aperion Health Single Database Setup Script
-- Run this script as PostgreSQL superuser (postgres)

-- Create database
CREATE DATABASE "${databaseConfig.database}";

-- Connect to the database
\\c "${databaseConfig.database}";

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schemas
${schemas.map(schema => `CREATE SCHEMA IF NOT EXISTS ${schema};`).join('\n')}

-- Create update function for timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Database setup completed
-- Next steps:
-- 1. Run the migration script: cd database && npm run migrate
-- 2. Test connection: cd database && npm run test:connection
`;

  const scriptPath = path.join(__dirname, 'create-database.sql');
  fs.writeFileSync(scriptPath, sqlScript);
  console.log(`📄 SQL script created: ${scriptPath}`);
}

// Check if PostgreSQL is accessible
async function checkPostgreSQL(): Promise<boolean> {
  // First try to connect to the target database
  try {
    const client = new Client(databaseConfig);
    await client.connect();
    console.log('✅ Target database connection successful');
    await client.end();
    return true;
  } catch (error: any) {
    if (error.code === '3D000') {
      console.log('⚠️  Target database does not exist, checking PostgreSQL server...');

      // Try connecting to postgres database to check server
      const postgresClient = new Client({
        ...databaseConfig,
        database: 'postgres'
      });

      try {
        await postgresClient.connect();
        console.log('✅ PostgreSQL server connection successful');
        await postgresClient.end();
        console.log(`⚠️  Database '${databaseConfig.database}' needs to be created`);
        return false;
      } catch (serverError: any) {
        console.error('❌ PostgreSQL server connection failed:', serverError.message);
        console.error('\n💡 Troubleshooting:');
        console.error('   1. Make sure PostgreSQL is installed and running');
        console.error('   2. Check your connection settings in .env file');
        console.error('   3. Verify PostgreSQL is listening on the correct port');
        console.error('   4. Check database credentials');
        return false;
      }
    } else {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
  }
}

// Main execution
async function main(): Promise<void> {
  console.log('🔍 Checking database connection...');
  console.log(`📍 Target: ${databaseConfig.host}:${databaseConfig.port}/${databaseConfig.database}`);

  const isConnected = await checkPostgreSQL();
  if (!isConnected) {
    console.log('\n📄 Creating SQL script for manual setup...');
    createDatabaseScript();
    console.log('\n⚠️  Please run the generated SQL script manually and then retry this setup.');
    console.log('\n💡 Alternative: Use the database migration system:');
    console.log('   cd database && npm install && npm run migrate');
    process.exit(1);
  }

  await setupDatabase();
}

// Handle command line arguments
function handleCommandLine(): void {
  const args = process.argv.slice(2);
  
  if (args.includes('--create-script')) {
    createDatabaseScript();
    console.log('✅ SQL script created successfully');
  } else if (args.includes('--help')) {
    console.log('Aperion Health Database Setup');
    console.log('');
    console.log('Usage:');
    console.log('  tsx scripts/setup-databases.ts              # Setup database with schemas');
    console.log('  tsx scripts/setup-databases.ts --create-script  # Create SQL script for manual setup');
    console.log('  tsx scripts/setup-databases.ts --help           # Show this help');
    console.log('');
    console.log('Environment Variables:');
    console.log('  DB_HOST      Database host (default: **************)');
    console.log('  DB_PORT      Database port (default: 5432)');
    console.log('  DB_NAME      Database name (default: AperionHealth-Dev)');
    console.log('  DB_USER      Database user (default: postgres)');
    console.log('  DB_PASSWORD  Database password (default: Cloud@2025)');
    console.log('  DB_SSL       Use SSL connection (default: false)');
  } else {
    main().catch(error => {
      console.error('❌ Setup failed:', error);
      process.exit(1);
    });
  }
}

if (require.main === module) {
  handleCommandLine();
}

export { setupDatabase, createDatabaseScript, checkPostgreSQL };
