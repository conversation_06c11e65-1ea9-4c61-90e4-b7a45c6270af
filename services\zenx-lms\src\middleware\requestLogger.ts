import { Request, Response, NextFunction } from 'express';
import { generateRequestId, generateCorrelationId, logger } from '../utils/logger';

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      correlationId?: string;
      startTime?: number;
    }
  }
}

export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  // Generate request ID and correlation ID using Pino utilities
  req.requestId = generateRequestId();
  req.correlationId = (req.headers['x-correlation-id'] as string) || generateCorrelationId();
  req.startTime = Date.now();

  // Add correlation ID to response headers (only if headers haven't been sent)
  if (req.correlationId && !res.headersSent) {
    res.setHeader('X-Correlation-ID', req.correlationId);
  }
  if (req.requestId && !res.headersSent) {
    res.setHeader('X-Request-ID', req.requestId);
  }

  // Add request ID and correlation ID to request for Pino HTTP logger
  (req as any).id = req.requestId;
  (req as any).correlationId = req.correlationId;

  // Log incoming request with comprehensive data
  logger.info('Incoming request', {
    requestId: req.requestId,
    correlationId: req.correlationId,
    method: req.method,
    url: req.originalUrl || req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.socket?.remoteAddress,
    headers: {
      authorization: req.headers.authorization ? '[REDACTED]' : undefined,
      'content-type': req.headers['content-type'],
      'content-length': req.headers['content-length'],
      'accept': req.headers.accept,
      'accept-encoding': req.headers['accept-encoding'],
      'cache-control': req.headers['cache-control'],
    },
    query: req.query,
    params: req.params,
    timestamp: new Date().toISOString(),
    service: 'zenx-lms',
  });

  // Override res.end to log response with comprehensive data
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any, cb?: any): any {
    const responseTime = Date.now() - (req.startTime || Date.now());

    logger.info('Outgoing response', {
      requestId: req.requestId,
      correlationId: req.correlationId,
      method: req.method,
      url: req.originalUrl || req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      responseTimeMs: responseTime,
      contentLength: res.get('Content-Length'),
      responseHeaders: {
        'content-type': res.get('Content-Type'),
        'content-length': res.get('Content-Length'),
        'cache-control': res.get('Cache-Control'),
        'x-request-id': res.get('X-Request-ID'),
        'x-correlation-id': res.get('X-Correlation-ID'),
      },
      timestamp: new Date().toISOString(),
      service: 'zenx-lms',
    });

    // Call original end method
    return originalEnd.call(this, chunk, encoding, cb);
  };

  next();
};
