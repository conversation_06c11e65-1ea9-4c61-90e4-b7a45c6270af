import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import {
  getMemberProfile,
  updateMemberProfile,
  getMemberDependents,
  createDependent,
  updateDependent,
  deleteDependent
} from '../controllers/profile';
import { asyncHandler } from '@aperion/shared';

const router = Router();

// Validation error handler
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    // Log validation errors for debugging
    const logger = require('../utils/logger').logger;
    logger.warn('Validation failed', {
      requestId: req.requestId || 'unknown',
      endpoint: req.originalUrl,
      method: req.method,
      validationErrors: errors.array(),
      requestBody: req.body,
      userId: req.user?.sub,
    });

    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        details: errors.array(),
        timestamp: new Date().toISOString(),
        requestId: req.requestId || 'unknown',
        path: req.originalUrl,
        method: req.method,
      },
    });
  }
  next();
};

// Profile update validation
const profileUpdateValidation = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),

  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),

  body('dateOfBirth')
    .optional()
    .custom((value) => {
      if (!value) return true; // Optional field

      // Accept both yyyy-MM-dd and ISO 8601 formats
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;

      if (dateRegex.test(value) || isoRegex.test(value)) {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          throw new Error('Date of birth must be a valid date');
        }
        return true;
      }

      throw new Error('Date of birth must be in yyyy-MM-dd or ISO 8601 format');
    }),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other', 'prefer-not-to-say'])
    .withMessage('Gender must be one of: male, female, other, prefer-not-to-say'),

  body('phone')
    .optional()
    .custom((value) => {
      if (!value) return true; // Optional field

      // Allow empty string or valid phone patterns
      if (value === '') return true;

      // Basic phone validation - allow digits, spaces, dashes, parentheses, plus
      const phoneRegex = /^[\+]?[\d\s\-\(\)]+$/;
      if (phoneRegex.test(value) && value.length >= 10 && value.length <= 20) {
        return true;
      }

      throw new Error('Please provide a valid phone number (10-20 characters, digits, spaces, dashes, parentheses, and + allowed)');
    }),

  body('address')
    .optional()
    .isObject()
    .withMessage('Address must be an object'),

  body('address.street')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Street address must be less than 200 characters'),

  body('address.city')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('City must be less than 100 characters'),

  body('address.state')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('State must be less than 100 characters'),

  body('address.zipCode')
    .optional()
    .custom((value) => {
      if (!value || value === '') return true; // Allow empty

      const trimmed = value.trim();
      if (trimmed.length < 3 || trimmed.length > 20) {
        throw new Error('Postal code must be between 3 and 20 characters');
      }

      if (!/^[A-Za-z0-9\s\-]+$/.test(trimmed)) {
        throw new Error('Postal code can only contain letters, numbers, spaces, and hyphens');
      }

      return true;
    }),
  
  body('address.country')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Country must be less than 100 characters'),
  
  body('emergencyContacts')
    .optional()
    .isArray()
    .withMessage('Emergency contacts must be an array'),
  
  body('emergencyContacts.*.name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Emergency contact name is required and must be less than 100 characters'),
  
  body('emergencyContacts.*.relationship')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Emergency contact relationship is required and must be less than 50 characters'),
  
  body('emergencyContacts.*.phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Emergency contact phone must be a valid phone number'),
  
  body('emergencyContacts.*.email')
    .optional()
    .isEmail()
    .withMessage('Emergency contact email must be a valid email address'),
  
  body('healthPreferences')
    .optional()
    .isObject()
    .withMessage('Health preferences must be an object'),
  
  body('privacySettings')
    .optional()
    .isObject()
    .withMessage('Privacy settings must be an object'),
  
  body('profilePictureUrl')
    .optional()
    .isURL()
    .withMessage('Profile picture URL must be a valid URL'),
];

/**
 * @route   GET /api/member/profile
 * @desc    Get complete member profile with all data
 * @access  Private (Member)
 */
router.get('/', asyncHandler(getMemberProfile));

/**
 * @route   PUT /api/member/profile
 * @desc    Update member profile
 * @access  Private (Member)
 */
router.put(
  '/',
  profileUpdateValidation,
  handleValidationErrors,
  asyncHandler(updateMemberProfile)
);

/**
 * @route   GET /api/member/profile/dependents
 * @desc    Get member dependents only
 * @access  Private (Member)
 */
router.get('/dependents', asyncHandler(getMemberDependents));

// Dependent validation schemas
const dependentValidation = [
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('First name is required and must be between 1-100 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Last name is required and must be between 1-100 characters'),
  body('relationship')
    .isIn(['spouse', 'child', 'parent', 'sibling', 'other'])
    .withMessage('Relationship must be one of: spouse, child, parent, sibling, other'),
  body('dateOfBirth')
    .isISO8601()
    .withMessage('Date of birth must be a valid date in ISO format'),
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other', 'prefer_not_to_say'])
    .withMessage('Gender must be one of: male, female, other, prefer_not_to_say'),
];

const dependentUpdateValidation = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('First name must be between 1-100 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Last name must be between 1-100 characters'),
  body('relationship')
    .optional()
    .isIn(['spouse', 'child', 'parent', 'sibling', 'other'])
    .withMessage('Relationship must be one of: spouse, child, parent, sibling, other'),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date in ISO format'),
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other', 'prefer_not_to_say'])
    .withMessage('Gender must be one of: male, female, other, prefer_not_to_say'),
];

/**
 * @route   POST /api/member/profile/dependents
 * @desc    Create a new dependent
 * @access  Private (Member)
 */
router.post(
  '/dependents',
  dependentValidation,
  handleValidationErrors,
  asyncHandler(createDependent)
);

/**
 * @route   PUT /api/member/profile/dependents/:dependentId
 * @desc    Update an existing dependent
 * @access  Private (Member)
 */
router.put(
  '/dependents/:dependentId',
  dependentUpdateValidation,
  handleValidationErrors,
  asyncHandler(updateDependent)
);

/**
 * @route   DELETE /api/member/profile/dependents/:dependentId
 * @desc    Delete a dependent
 * @access  Private (Member)
 */
router.delete('/dependents/:dependentId', asyncHandler(deleteDependent));

export { router as profileRouter };
