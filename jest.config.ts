import type { Config } from 'jest';

const config: Config = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/packages', '<rootDir>/services', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  collectCoverageFrom: [
    'packages/*/src/**/*.ts',
    'services/*/src/**/*.ts',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/dist/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  moduleNameMapping: {
    '^@aperion/shared/(.*)$': '<rootDir>/packages/shared/src/$1',
    '^@aperion/auth/(.*)$': '<rootDir>/packages/auth/src/$1',
    '^@aperion/database/(.*)$': '<rootDir>/packages/database/src/$1'
  },
  testTimeout: 30000,
  verbose: true,
  projects: [
    {
      displayName: 'unit',
      testMatch: ['<rootDir>/**/*.test.ts'],
      testPathIgnorePatterns: ['/integration/', '/e2e/']
    },
    {
      displayName: 'integration',
      testMatch: ['<rootDir>/**/integration/**/*.test.ts']
    }
  ]
};

export default config;
