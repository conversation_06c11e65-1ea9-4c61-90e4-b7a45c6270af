import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Search, Download, Upload, RefreshCw, MoreHorizontal, Edit, Trash2, UserPlus, Mail, Phone, Loader2, Users, Clock } from "lucide-react";
import { employeeService, Employee, Department, HealthPlan, CreateEmployeeRequest } from '@/services/employeeService';
import { LoadingContainer } from "@/components/ui/loaders";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";

// Departments for filtering
const departments = ["All Departments", "Engineering", "Marketing", "Finance", "HR", "Sales", "Customer Success", "Product"];

// Health plans for filtering
const healthPlans = ["All Plans", "Basic", "Standard", "Premium"];

const EmployeeManagement = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("All Departments");
  const [selectedHealthPlan, setSelectedHealthPlan] = useState("All Plans");
  const [selectedStatus, setSelectedStatus] = useState("All Statuses");
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState("current");
  const [isAddEmployeeOpen, setIsAddEmployeeOpen] = useState(false);

  // API data state
  const [apiEmployees, setApiEmployees] = useState<Employee[]>([]);
  const [apiCurrentEmployees, setApiCurrentEmployees] = useState<Employee[]>([]);
  const [apiPendingEmployees, setApiPendingEmployees] = useState<Employee[]>([]);
  const [apiDepartments, setApiDepartments] = useState<Department[]>([]);
  const [apiHealthPlans, setApiHealthPlans] = useState<HealthPlan[]>([]);

  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingCurrent, setIsLoadingCurrent] = useState(false);
  const [isLoadingPending, setIsLoadingPending] = useState(false);
  const [isCreatingEmployee, setIsCreatingEmployee] = useState(false);
  const [loadingDepartments, setLoadingDepartments] = useState(false);
  const [loadingHealthPlans, setLoadingHealthPlans] = useState(false);
  const [deletingEmployeeId, setDeletingEmployeeId] = useState<string | null>(null);

  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [employeeToDelete, setEmployeeToDelete] = useState<{ id: string; name: string } | null>(null);

  // Form state for Add Employee
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    department: "",
    role: "",
    healthPlan: ""
  });

  // Form validation errors
  const [formErrors, setFormErrors] = useState({
    firstName: "",
    email: "",
    phoneNumber: "",
    department: "",
    role: "",
    healthPlan: ""
  });

  // Load departments, health plans, and employees on component mount
  useEffect(() => {
    loadDepartments();
    loadHealthPlans();
    loadCurrentEmployees();
    loadPendingEmployees();
    loadEmployees(); // Keep for backward compatibility
  }, []);

  // Load departments from API
  const loadDepartments = async () => {
    setLoadingDepartments(true);
    try {
      const response = await employeeService.getDepartments();
      if (response.success && response.data) {
        setApiDepartments(response.data);
      }
    } catch (error) {
      console.error('Failed to load departments:', error);
    } finally {
      setLoadingDepartments(false);
    }
  };

  // Load health plans from API
  const loadHealthPlans = async () => {
    setLoadingHealthPlans(true);
    try {
      const response = await employeeService.getHealthPlans();
      if (response.success && response.data) {
        setApiHealthPlans(response.data);
      }
    } catch (error) {
      console.error('Failed to load health plans:', error);
    } finally {
      setLoadingHealthPlans(false);
    }
  };

  // Load employees from API (for pending employees)
  const loadEmployees = async () => {
    setIsLoading(true);
    try {
      const response = await employeeService.getEmployees();
      if (response.success && response.data) {
        setApiEmployees(response.data);
      }
    } catch (error) {
      console.error('Failed to load employees:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load current employees from API
  const loadCurrentEmployees = async () => {
    setIsLoadingCurrent(true);
    try {
      const response = await employeeService.getCurrentEmployees();
      if (response.success && response.data) {
        setApiCurrentEmployees(response.data);
      }
    } catch (error) {
      console.error('Failed to load current employees:', error);
    } finally {
      setIsLoadingCurrent(false);
    }
  };

  // Load pending employees from API
  const loadPendingEmployees = async () => {
    setIsLoadingPending(true);
    try {
      const response = await employeeService.getPendingEmployees();
      if (response.success && response.data) {
        setApiPendingEmployees(response.data);
      }
    } catch (error) {
      console.error('Failed to load pending employees:', error);
    } finally {
      setIsLoadingPending(false);
    }
  };
  
  // Filter current employees based on search and filters
  const filteredCurrentEmployees = apiCurrentEmployees.filter(employee => {
    const matchesSearch =
      employee.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.jobTitle?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.departmentName?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesDepartment = selectedDepartment === "All Departments" || employee.departmentName === selectedDepartment;
    // Note: Health plan filtering is not available in the current API, so we'll skip it for now
    const matchesHealthPlan = selectedHealthPlan === "All Plans"; // Always true for now
    const matchesStatus = selectedStatus === "All Statuses" || employee.employmentStatus === selectedStatus.toLowerCase();

    return matchesSearch && matchesDepartment && matchesHealthPlan && matchesStatus;
  });

  // Filter pending employees based on search
  const filteredPendingEmployees = apiPendingEmployees.filter(employee => {
    const matchesSearch =
      employee.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.jobTitle?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.departmentName?.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });
  
  // Handle employee selection (checkbox)
  const toggleEmployeeSelection = (employeeId: number) => {
    if (selectedEmployees.includes(employeeId)) {
      setSelectedEmployees(selectedEmployees.filter(id => id !== employeeId));
    } else {
      setSelectedEmployees([...selectedEmployees, employeeId]);
    }
  };
  
  // Handle bulk selection
  const toggleSelectAll = () => {
    if (selectedEmployees.length === filteredCurrentEmployees.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(filteredCurrentEmployees.map(e => e.id));
    }
  };

  // Validate form fields
  const validateForm = () => {
    const errors = {
      firstName: "",
      email: "",
      phoneNumber: "",
      department: "",
      role: "",
      healthPlan: ""
    };

    let isValid = true;

    // First Name validation
    if (!formData.firstName.trim()) {
      errors.firstName = "First Name is required";
      isValid = false;
    }

    // Email validation
    if (!formData.email.trim()) {
      errors.email = "Email is required";
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
      isValid = false;
    }

    // Phone Number validation
    if (!formData.phoneNumber.trim()) {
      errors.phoneNumber = "Phone Number is required";
      isValid = false;
    } else if (!/^[\+]?[1-9][\d]{0,15}$/.test(formData.phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
      errors.phoneNumber = "Please enter a valid phone number";
      isValid = false;
    }

    // Department validation
    if (!formData.department) {
      errors.department = "Department is required";
      isValid = false;
    }

    // Role validation
    if (!formData.role.trim()) {
      errors.role = "Role is required";
      isValid = false;
    }

    // Health Plan validation
    if (!formData.healthPlan) {
      errors.healthPlan = "Health Plan is required";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Handle form submission
  const handleAddEmployee = async () => {
    if (validateForm()) {
      setIsCreatingEmployee(true);

      try {
        const employeeData: CreateEmployeeRequest = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phoneNumber: formData.phoneNumber || undefined,
          department: formData.department,
          role: formData.role,
          healthPlan: formData.healthPlan
        };

        console.log("Registering employee:", employeeData);
        const response = await employeeService.registerEmployee(employeeData);

        if (response.success) {
          console.log("Employee registered successfully:", response.data);

          // Reset form
          setFormData({
            firstName: "",
            lastName: "",
            email: "",
            phoneNumber: "",
            department: "",
            role: "",
            healthPlan: ""
          });

          // Clear errors
          setFormErrors({
            firstName: "",
            email: "",
            phoneNumber: "",
            department: "",
            role: "",
            healthPlan: ""
          });

          // Close dialog
          setIsAddEmployeeOpen(false);

          // Reload employees to show the new one
          loadCurrentEmployees();
          loadPendingEmployees();
          loadEmployees(); // Keep for backward compatibility

          // Show success toast with member_id information
          const memberInfo = response.data?.member_id ? ` (Member ID: ${response.data.member_id})` : '';
          toast({
            title: "Employee Added Successfully",
            description: `${employeeData.firstName} ${employeeData.lastName} has been registered successfully${memberInfo}!`,
            variant: "default",
          });
        } else {
          // Handle API error
          console.error("Failed to register employee:", response.error);
          toast({
            title: "Registration Failed",
            description: response.error?.message || 'Failed to register employee. Please try again.',
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error registering employee:", error);
        toast({
          title: "Error",
          description: "An unexpected error occurred while registering the employee.",
          variant: "destructive",
        });
      } finally {
        setIsCreatingEmployee(false);
      }
    }
  };

  // Handle form field changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Handle delete invitation - show confirmation dialog
  const handleDeleteInvitation = (employeeId: string, employeeName: string) => {
    setEmployeeToDelete({ id: employeeId, name: employeeName });
    setDeleteDialogOpen(true);
  };

  // Confirm delete invitation
  const confirmDeleteInvitation = async () => {
    if (!employeeToDelete) return;

    setDeletingEmployeeId(employeeToDelete.id);

    try {
      console.log("Deleting employee invitation:", employeeToDelete.id);
      const response = await employeeService.deleteInvitation(employeeToDelete.id);

      if (response.success) {
        console.log("Employee invitation deleted successfully:", response.data);

        // Reload pending employees to reflect the deletion
        loadPendingEmployees();

        // Show success toast
        toast({
          title: "Invitation Deleted",
          description: `Invitation for ${employeeToDelete.name} has been deleted successfully.`,
          variant: "default",
        });
      } else {
        // Handle API error
        console.error("Failed to delete employee invitation:", response.error);
        toast({
          title: "Delete Failed",
          description: response.error?.message || 'Failed to delete invitation. Please try again.',
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting employee invitation:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred while deleting the invitation.",
        variant: "destructive",
      });
    } finally {
      setDeletingEmployeeId(null);
      setDeleteDialogOpen(false);
      setEmployeeToDelete(null);
    }
  };

  // Empty state component for current employees
  const CurrentEmployeesEmptyState = () => (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="text-center">
        <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Current Employees</h3>
        <p className="text-gray-500 text-sm mb-6 max-w-sm">
          You haven't added any employees yet. Start building your team by adding your first employee.
        </p>
        <Button
          onClick={() => setIsAddEmployeeOpen(true)}
          className="bg-primary text-white hover:bg-primary/90"
        >
          <UserPlus className="mr-2 h-4 w-4" />
          Add First Employee
        </Button>
      </div>
    </div>
  );

  // Empty state component for pending employees
  const PendingEmployeesEmptyState = () => (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="text-center">
        <Clock className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Pending Enrollments</h3>
        <p className="text-gray-500 text-sm mb-6 max-w-sm">
          All invited employees have completed their enrollment. New invitations will appear here.
        </p>
        <Button
          onClick={() => setIsAddEmployeeOpen(true)}
          variant="outline"
        >
          <UserPlus className="mr-2 h-4 w-4" />
          Invite New Employee
        </Button>
      </div>
    </div>
  );
  
  return (
    <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Employee Management</h1>
            <p className="text-gray-500 mt-1">Manage employees and their wellness benefits</p>
          </div>
          <div className="flex gap-4">
            <Dialog open={isAddEmployeeOpen} onOpenChange={setIsAddEmployeeOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Employee
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New Employee</DialogTitle>
                  <DialogDescription>
                    Enter the details of the new employee below.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      placeholder="First Name"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange("firstName", e.target.value)}
                      className={formErrors.firstName ? "border-red-500" : ""}
                    />
                    {formErrors.firstName && (
                      <p className="text-sm text-red-500">{formErrors.firstName}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      placeholder="Last Name"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange("lastName", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className={formErrors.email ? "border-red-500" : ""}
                    />
                    {formErrors.email && (
                      <p className="text-sm text-red-500">{formErrors.email}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber" className="flex items-center space-x-1">
                      <span>Phone Number *</span>
                    </Label>
                    <Input
                      id="phoneNumber"
                      type="tel"
                      placeholder="+****************"
                      value={formData.phoneNumber}
                      onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                      className={formErrors.phoneNumber ? "border-red-500" : ""}
                    />
                    {formErrors.phoneNumber && (
                      <p className="text-sm text-red-500">{formErrors.phoneNumber}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="department">Department *</Label>
                    <Select
                      value={formData.department}
                      onValueChange={(value) => handleInputChange("department", value)}
                    >
                      <SelectTrigger className={formErrors.department ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        {loadingDepartments ? (
                          <SelectItem value="" disabled>Loading departments...</SelectItem>
                        ) : (
                          apiDepartments.length > 0 ? (
                            apiDepartments.map((dept) => (
                              <SelectItem key={dept.id} value={dept.name}>{dept.name}</SelectItem>
                            ))
                          ) : (
                            departments.filter(d => d !== "All Departments").map((dept) => (
                              <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                            ))
                          )
                        )}
                      </SelectContent>
                    </Select>
                    {formErrors.department && (
                      <p className="text-sm text-red-500">{formErrors.department}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role *</Label>
                    <Input
                      id="role"
                      placeholder="Role"
                      value={formData.role}
                      onChange={(e) => handleInputChange("role", e.target.value)}
                      className={formErrors.role ? "border-red-500" : ""}
                    />
                    {formErrors.role && (
                      <p className="text-sm text-red-500">{formErrors.role}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="healthPlan">Health Plan *</Label>
                    <Select
                      value={formData.healthPlan}
                      onValueChange={(value) => handleInputChange("healthPlan", value)}
                    >
                      <SelectTrigger className={formErrors.healthPlan ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select health plan" />
                      </SelectTrigger>
                      <SelectContent>
                        {loadingHealthPlans ? (
                          <SelectItem value="" disabled>Loading health plans...</SelectItem>
                        ) : (
                          apiHealthPlans.length > 0 ? (
                            apiHealthPlans.map((plan) => (
                              <SelectItem key={plan.id} value={plan.name}>{plan.name}</SelectItem>
                            ))
                          ) : (
                            healthPlans.filter(p => p !== "All Plans").map((plan) => (
                              <SelectItem key={plan} value={plan}>{plan}</SelectItem>
                            ))
                          )
                        )}
                      </SelectContent>
                    </Select>
                    {formErrors.healthPlan && (
                      <p className="text-sm text-red-500">{formErrors.healthPlan}</p>
                    )}
                  </div>

                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsAddEmployeeOpen(false)}
                    disabled={isCreatingEmployee}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    onClick={handleAddEmployee}
                    disabled={isCreatingEmployee}
                  >
                    {isCreatingEmployee ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      'Add Employee'
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        {/* Employee Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList>
            <TabsTrigger value="current">Current Employees</TabsTrigger>
            <TabsTrigger value="pending">Pending Enrollments</TabsTrigger>
          </TabsList>
          
          {/* Current Employees Tab */}
          <TabsContent value="current">
            <Card>
              <CardHeader className="p-4 pb-0">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div className="relative w-full md:w-auto md:min-w-[320px]">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input 
                      placeholder="Search employees..." 
                      className="pl-10"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div className="flex flex-wrap gap-2 items-center">
                    <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Department" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((dept) => (
                          <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Select value={selectedHealthPlan} onValueChange={setSelectedHealthPlan}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Health Plan" />
                      </SelectTrigger>
                      <SelectContent>
                        {healthPlans.map((plan) => (
                          <SelectItem key={plan} value={plan}>{plan}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="All Statuses">All Statuses</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="on_leave">On Leave</SelectItem>
                        <SelectItem value="terminated">Terminated</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Button variant="outline" size="icon" onClick={() => { loadCurrentEmployees(); loadPendingEmployees(); loadEmployees(); }} disabled={isLoadingCurrent || isLoadingPending || isLoading}>
                      <RefreshCw className={`h-4 w-4 ${(isLoadingCurrent || isLoadingPending || isLoading) ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0 overflow-auto">
                <LoadingContainer isLoading={isLoadingCurrent} text="Loading current employees...">
                  {filteredCurrentEmployees.length === 0 ? (
                    <CurrentEmployeesEmptyState />
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-12">
                            <Checkbox
                              checked={selectedEmployees.length > 0 && selectedEmployees.length === filteredCurrentEmployees.length}
                              onCheckedChange={toggleSelectAll}
                            />
                          </TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Department</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>Health Plan</TableHead>
                          <TableHead>Wellness Score</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredCurrentEmployees.map((employee) => (
                          <TableRow key={employee.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedEmployees.includes(employee.id)}
                                onCheckedChange={() => toggleEmployeeSelection(employee.id)}
                              />
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="font-medium">{employee.firstName} {employee.lastName}</p>
                                <p className="text-sm text-gray-500">{employee.email}</p>
                              </div>
                            </TableCell>
                            <TableCell>{employee.departmentName || 'N/A'}</TableCell>
                            <TableCell>{employee.jobTitle || 'N/A'}</TableCell>
                            <TableCell>
                              <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                                N/A
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className="h-2 w-16 bg-gray-200 rounded-full overflow-hidden">
                                  <div
                                    className="h-full bg-gray-400"
                                    style={{ width: '0%' }}
                                  />
                                </div>
                                <span className="text-sm">N/A</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={
                                employee.employmentStatus === "active" ? "bg-green-100 text-green-800 hover:bg-green-100" :
                                employee.employmentStatus === "inactive" ? "bg-red-100 text-red-800 hover:bg-red-100" :
                                employee.employmentStatus === "on_leave" ? "bg-amber-100 text-amber-800 hover:bg-amber-100" :
                                "bg-gray-100 text-gray-800 hover:bg-gray-100"
                              }>
                                {employee.employmentStatus === "active" ? "Active" :
                                 employee.employmentStatus === "inactive" ? "Inactive" :
                                 employee.employmentStatus === "on_leave" ? "On Leave" :
                                 employee.employmentStatus === "terminated" ? "Terminated" :
                                 employee.employmentStatus}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button variant="ghost" size="icon">
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="icon">
                                  <Mail className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </LoadingContainer>
              </CardContent>
              <CardFooter className="flex justify-between p-4 border-t">
                <div className="text-sm text-gray-500">
                  Showing {filteredCurrentEmployees.length} of {apiCurrentEmployees.length} employees
                </div>
                <div className="flex gap-2">
                  {selectedEmployees.length > 0 ? (
                    <>
                      <Button variant="outline" size="sm">
                        Bulk Edit
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Remove
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button variant="outline" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Export
                      </Button>
                      <Button variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        Import
                      </Button>
                    </>
                  )}
                </div>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Pending Enrollments Tab */}
          <TabsContent value="pending">
            <Card>
              <CardHeader>
                <CardTitle>Pending Enrollments</CardTitle>
                <CardDescription>Employees who have been invited but haven't completed enrollment</CardDescription>
              </CardHeader>
              <CardContent className="p-0 overflow-auto">
                <LoadingContainer isLoading={isLoadingPending} text="Loading pending enrollments...">
                  {filteredPendingEmployees.length === 0 ? (
                    <PendingEmployeesEmptyState />
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Department</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>Invite Sent</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredPendingEmployees.map((employee) => (
                          <TableRow key={employee.id}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{employee.firstName} {employee.lastName}</p>
                              </div>
                            </TableCell>
                            <TableCell>{employee.email}</TableCell>
                            <TableCell>{employee.departmentName || 'N/A'}</TableCell>
                            <TableCell>{employee.jobTitle || 'N/A'}</TableCell>
                            <TableCell>{employee.createdAt ? new Date(employee.createdAt).toLocaleDateString() : 'N/A'}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button variant="outline" size="sm">
                                  <Mail className="mr-2 h-4 w-4" />
                                  Resend Invite
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteInvitation(employee.id, `${employee.firstName} ${employee.lastName}`)}
                                  disabled={deletingEmployeeId === employee.id}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  {deletingEmployeeId === employee.id ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </LoadingContainer>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <Trash2 className="h-5 w-5 text-red-500" />
                Delete Employee Invitation
              </AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete the invitation for{" "}
                <span className="font-semibold">{employeeToDelete?.name}</span>?
                <br />
                <br />
                This action cannot be undone. The employee will be removed from Cognito and the database.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel
                onClick={() => {
                  setDeleteDialogOpen(false);
                  setEmployeeToDelete(null);
                }}
                disabled={deletingEmployeeId !== null}
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDeleteInvitation}
                disabled={deletingEmployeeId !== null}
                className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              >
                {deletingEmployeeId === employeeToDelete?.id ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Invitation
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
  );
};

export default EmployeeManagement;