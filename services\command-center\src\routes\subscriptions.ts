import { Router } from 'express';
import { SubscriptionController } from '../controllers/SubscriptionController';
import { authorize } from '../middleware/auth';
import { UserRole } from '@aperion/shared';

/**
 * Subscription Management Router
 *
 * Handles subscription plan operations including CRUD operations,
 * statistics, search functionality, and plan management.
 *
 * Authentication: Required for all routes
 * Authorization: System Admin only
 *
 * @routes
 * GET    /test           - API health check
 * GET    /dev            - Get all subscription plans (development)
 * GET    /dev/statistics - Get subscription statistics (development)
 * GET    /dev/active     - Get active subscription plans (development)
 * GET    /dev/search     - Search subscription plans (development)
 * GET    /dev/:id        - Get subscription plan by ID (development)
 * POST   /dev            - Create subscription plan (development)
 * PUT    /dev/:id        - Update subscription plan (development)
 * DELETE /dev/:id        - Delete subscription plan (development)
 * GET    /               - Get all subscription plans (production)
 * GET    /statistics     - Get subscription statistics (production)
 * GET    /active         - Get active subscription plans (production)
 * GET    /search         - Search subscription plans (production)
 * GET    /:id            - Get subscription plan by ID (production)
 * POST   /               - Create subscription plan (production)
 * PUT    /:id            - Update subscription plan (production)
 * DELETE /:id            - Delete subscription plan (production)
 */

const router = Router();
const subscriptionController = new SubscriptionController();

// =============================================================================
// PROTECTED ROUTES (Authentication & Authorization Required)
// =============================================================================

// Apply authorization middleware to all routes
// Only system admins can manage subscription plans
router.use(authorize([UserRole.SYSTEM_ADMIN]));

// =============================================================================
// PUBLIC ROUTES (Within Protected Context)
// =============================================================================

/**
 * @route   GET /test
 * @desc    API health check endpoint
 * @access  Private (System Admin)
 * @returns {Object} Health status and timestamp
 */
router.get('/test', (_req, res) => {
  res.json({
    success: true,
    message: 'Subscription management API is working!',
    timestamp: new Date().toISOString(),
    service: 'command-center',
    module: 'subscriptions',
  });
});

// =============================================================================
// DEVELOPMENT ENDPOINTS
// =============================================================================

/**
 * @route   GET /dev
 * @desc    Get all subscription plans with pagination and filtering
 * @access  Private (System Admin)
 * @query   {number} page - Page number (default: 1)
 * @query   {number} limit - Items per page (default: 20)
 * @query   {string} search - Search term
 * @query   {string} planType - Filter by plan type
 * @query   {boolean} isActive - Filter by active status
 */
router.get('/dev', subscriptionController.getSubscriptionPlans);

/**
 * @route   GET /dev/statistics
 * @desc    Get subscription plan statistics and metrics
 * @access  Private (System Admin)
 * @returns {Object} Subscription statistics including plan counts and revenue metrics
 */
router.get('/dev/statistics', subscriptionController.getSubscriptionPlanStatistics);

/**
 * @route   GET /dev/active
 * @desc    Get all active subscription plans
 * @access  Private (System Admin)
 * @returns {Array} List of active subscription plans
 */
router.get('/dev/active', subscriptionController.getActiveSubscriptionPlans);

/**
 * @route   GET /dev/search
 * @desc    Search subscription plans by name or description
 * @access  Private (System Admin)
 * @query   {string} q - Search query
 * @query   {number} limit - Maximum results (default: 10)
 */
router.get('/dev/search', subscriptionController.searchSubscriptionPlans);

/**
 * @route   GET /dev/:id
 * @desc    Get subscription plan by ID
 * @access  Private (System Admin)
 * @param   {string} id - Subscription plan ID
 * @returns {Object} Subscription plan details
 */
router.get('/dev/:id', subscriptionController.getSubscriptionPlanById);

/**
 * @route   POST /dev
 * @desc    Create new subscription plan
 * @access  Private (System Admin)
 * @body    {Object} Subscription plan data
 * @returns {Object} Created subscription plan details
 */
router.post('/dev', subscriptionController.createSubscriptionPlan);

/**
 * @route   PUT /dev/:id
 * @desc    Update subscription plan by ID
 * @access  Private (System Admin)
 * @param   {string} id - Subscription plan ID
 * @body    {Object} Updated subscription plan data
 * @returns {Object} Updated subscription plan details
 */
router.put('/dev/:id', subscriptionController.updateSubscriptionPlan);

/**
 * @route   DELETE /dev/:id
 * @desc    Delete subscription plan by ID
 * @access  Private (System Admin)
 * @param   {string} id - Subscription plan ID
 * @returns {Object} Deletion confirmation
 */
router.delete('/dev/:id', subscriptionController.deleteSubscriptionPlan);

// =============================================================================
// PRODUCTION ENDPOINTS
// =============================================================================

/**
 * @route   GET /
 * @desc    Get all subscription plans with pagination and filtering
 * @access  Private (System Admin)
 */
router.get('/', subscriptionController.getSubscriptionPlans);

/**
 * @route   GET /statistics
 * @desc    Get subscription plan statistics and metrics
 * @access  Private (System Admin)
 */
router.get('/statistics', subscriptionController.getSubscriptionPlanStatistics);

/**
 * @route   GET /active
 * @desc    Get all active subscription plans
 * @access  Private (System Admin)
 */
router.get('/active', subscriptionController.getActiveSubscriptionPlans);

/**
 * @route   GET /search
 * @desc    Search subscription plans by name or description
 * @access  Private (System Admin)
 */
router.get('/search', subscriptionController.searchSubscriptionPlans);

/**
 * @route   GET /:id
 * @desc    Get subscription plan by ID
 * @access  Private (System Admin)
 */
router.get('/:id', subscriptionController.getSubscriptionPlanById);

/**
 * @route   POST /
 * @desc    Create new subscription plan
 * @access  Private (System Admin)
 */
router.post('/', subscriptionController.createSubscriptionPlan);

/**
 * @route   PUT /:id
 * @desc    Update subscription plan by ID
 * @access  Private (System Admin)
 */
router.put('/:id', subscriptionController.updateSubscriptionPlan);

/**
 * @route   DELETE /:id
 * @desc    Delete subscription plan by ID
 * @access  Private (System Admin)
 */
router.delete('/:id', subscriptionController.deleteSubscriptionPlan);

export { router as subscriptionsRouter };
