{"name": "@aperion/shared", "version": "1.0.0", "description": "Shared utilities and types for Aperion Health platform", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/types/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js", "types": "./dist/types/index.d.ts"}, "./server": {"import": "./dist/esm/server.js", "require": "./dist/cjs/server.js", "types": "./dist/types/server.d.ts"}}, "scripts": {"build": "npm run clean && npm run build:cjs && npm run build:esm && npm run build:types", "build:cjs": "tsc -p tsconfig.cjs.json", "build:esm": "tsc -p tsconfig.esm.json", "build:types": "tsc -p tsconfig.types.json", "dev": "npm run build -- --watch", "test": "jest", "lint": "eslint src --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pino": "^9.7.0", "pino-http": "^10.5.0", "pino-multi-stream": "^6.0.0", "pino-pretty": "^13.0.0", "rotating-file-stream": "^3.2.3", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/pino-multi-stream": "^5.1.6", "@types/uuid": "^9.0.7", "rimraf": "^5.0.5", "typescript": "^5.1.0"}}