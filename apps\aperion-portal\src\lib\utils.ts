import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Date formatting utilities for consistent date handling
 */

/**
 * Convert any date string to yyyy-MM-dd format for HTML date inputs
 * @param dateString - Date string in any format (ISO, yyyy-MM-dd, etc.)
 * @returns Date string in yyyy-MM-dd format or empty string if invalid
 */
export function formatDateForInput(dateString?: string): string {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    // Extract yyyy-MM-dd part from ISO string
    return date.toISOString().split('T')[0];
  } catch {
    return '';
  }
}

/**
 * Convert yyyy-MM-dd format to ISO string for API requests
 * @param dateString - Date string in yyyy-MM-dd format
 * @returns ISO date string or original string if already in ISO format
 */
export function formatDateForAPI(dateString?: string): string {
  if (!dateString) return '';

  // If already in ISO format, return as is
  if (dateString.includes('T')) return dateString;

  try {
    // For yyyy-MM-dd format, create date and return ISO string
    const date = new Date(dateString + 'T00:00:00.000Z');
    if (isNaN(date.getTime())) return dateString;

    return date.toISOString();
  } catch {
    return dateString;
  }
}

/**
 * Validate if a date string is in valid format
 * @param dateString - Date string to validate
 * @returns true if valid date, false otherwise
 */
export function isValidDate(dateString?: string): boolean {
  if (!dateString) return false;

  try {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  } catch {
    return false;
  }
}

/**
 * Format date for display in user-friendly format
 * @param dateString - Date string in any format
 * @returns Formatted date string (e.g., "May 15, 2010") or original string if invalid
 */
export function formatDateForDisplay(dateString?: string): string {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch {
    return dateString;
  }
}
