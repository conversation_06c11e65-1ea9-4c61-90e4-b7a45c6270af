/**
 * API service utilities for AperionHealth Portal
 * Provides centralized API communication with proper error handling and logging
 */

import { logger } from '@/utils/logger';

// API Configuration
// Use relative URLs since Vite proxy is configured to route /api to localhost:3000
const API_BASE_URL = (import.meta as any).env?.VITE_API_BASE_URL || '';
const API_TIMEOUT = 30000; // 30 seconds

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    path?: string;
    method?: string;
    requestId?: string;
  };
}

export interface MemberProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  profilePictureUrl?: string;
  emergencyContacts?: any[];
  healthPreferences?: any;
  privacySettings?: any;
  department?: string;
  healthPlanStatus?: string;
  status: string;
  employeeId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Dependent {
  id: string;
  firstName: string;
  lastName: string;
  relationship: string;
  dateOfBirth: string;
  memberId: string;
  gender?: string;
}

export interface CreateDependentData {
  firstName: string;
  lastName: string;
  relationship: string;
  dateOfBirth: string;
  gender?: string;
}

export interface UpdateDependentData {
  firstName?: string;
  lastName?: string;
  relationship?: string;
  dateOfBirth?: string;
  gender?: string;
}

export interface ProfileData {
  profile: MemberProfile;
  dependents: Dependent[];
  stats: {
    totalDependents: number;
    profileCompleteness: number;
    profileCompletenessAchieved: boolean;
    profileCompletenessAchievedAt?: string | null;
    lastUpdated: string;
  };
}

// Utility function to get auth token
const getAuthToken = (): string | null => {
  return localStorage.getItem('aperion_token');
};

// Utility function to make API requests
async function apiRequest<T>(
  method: string,
  endpoint: string,
  data?: any,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const startTime = Date.now();
  const url = `${API_BASE_URL}${endpoint}`;
  const token = getAuthToken();

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>),
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const requestOptions: RequestInit = {
    method,
    headers,
    ...options,
  };

  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    requestOptions.body = JSON.stringify(data);
  }

  try {
    logger.info(`API Request: ${method} ${endpoint}`, {
      method,
      endpoint,
      hasData: !!data,
      hasAuth: !!token,
      tokenLength: token ? token.length : 0,
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    const response = await fetch(url, {
      ...requestOptions,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    const duration = Date.now() - startTime;

    // Enhanced response handling with better error details
    let responseData: ApiResponse<T>;
    let responseText: string;

    try {
      responseText = await response.text();

      // Try to parse as JSON first
      if (responseText.trim()) {
        try {
          responseData = JSON.parse(responseText);
        } catch (jsonError) {
          // If JSON parsing fails, create a structured error response
          logger.error('Failed to parse JSON response', {
            method,
            endpoint,
            status: response.status,
            responseText: responseText.substring(0, 500), // Log first 500 chars
            contentType: response.headers.get('content-type'),
          });

          throw new Error(`Failed to parse response: ${response.statusText}. Response: ${responseText.substring(0, 200)}`);
        }
      } else {
        // Empty response
        responseData = {
          success: false,
          error: {
            code: 'EMPTY_RESPONSE',
            message: `Empty response from server: ${response.statusText}`,
            timestamp: new Date().toISOString(),
          }
        };
      }
    } catch (textError) {
      logger.error('Failed to read response text', {
        method,
        endpoint,
        status: response.status,
        error: textError instanceof Error ? textError.message : 'Unknown error',
      });

      throw new Error(`Failed to read response: ${response.statusText}`);
    }

    logger.logApiCall(method, endpoint, response.status, duration, {
      success: response.ok,
      hasData: !!responseData.data,
      responseSize: responseText.length,
    });

    if (!response.ok) {
      const errorMessage = responseData.error?.message ||
                          `HTTP ${response.status}: ${response.statusText}`;

      logger.error('API request failed', {
        method,
        endpoint,
        status: response.status,
        error: responseData.error,
        hasAuth: !!token,
      });

      throw new Error(errorMessage);
    }

    return responseData;
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    logger.error('API request error', {
      method,
      endpoint,
      error: errorMessage,
      duration,
      hasAuth: !!token,
    });

    logger.logApiCall(method, endpoint, 0, duration, {
      error: errorMessage,
      success: false,
    });

    throw error;
  }
}

// Member Profile API Service
export const memberProfileService = {
  /**
   * Get complete member profile with all data
   */
  async getProfile(): Promise<ProfileData> {
    const response = await apiRequest<ProfileData>('GET', '/api/member/profile');
    if (!response.success || !response.data) {
      throw new Error('Failed to fetch profile data');
    }
    return response.data;
  },

  /**
   * Update member profile
   */
  async updateProfile(updateData: Partial<MemberProfile>): Promise<MemberProfile> {
    const response = await apiRequest<{ profile: MemberProfile }>('PUT', '/api/member/profile', updateData);

    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to update profile');
    }
    return response.data.profile;
  },

  /**
   * Get member dependents only
   */
  async getDependents(): Promise<Dependent[]> {
    const response = await apiRequest<{ dependents: Dependent[] }>('GET', '/api/member/profile/dependents');
    if (!response.success || !response.data) {
      throw new Error('Failed to fetch dependents');
    }
    return response.data.dependents;
  },

  /**
   * Create a new dependent
   */
  async createDependent(dependentData: CreateDependentData): Promise<Dependent> {
    const response = await apiRequest<{ dependent: Dependent }>('POST', '/api/member/profile/dependents', dependentData);
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to create dependent');
    }
    return response.data.dependent;
  },

  /**
   * Update an existing dependent
   */
  async updateDependent(dependentId: string, updateData: UpdateDependentData): Promise<Dependent> {
    const response = await apiRequest<{ dependent: Dependent }>('PUT', `/api/member/profile/dependents/${dependentId}`, updateData);
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to update dependent');
    }
    return response.data.dependent;
  },

  /**
   * Delete a dependent
   */
  async deleteDependent(dependentId: string): Promise<void> {
    const response = await apiRequest<{ message: string }>('DELETE', `/api/member/profile/dependents/${dependentId}`);
    if (!response.success) {
      throw new Error(response.error?.message || 'Failed to delete dependent');
    }
  },

  /**
   * Upload profile photo
   */
  async uploadProfilePhoto(file: File): Promise<{
    profilePictureUrl: string;
    uploadInfo: {
      originalSize: number;
      finalSize: number;
      compressionRatio: string;
      s3Key: string;
      dimensions?: {
        width?: number;
        height?: number;
      };
    };
  }> {
    const formData = new FormData();
    formData.append('photo', file);

    const token = getAuthToken();
    const response = await fetch('/api/member/profile/photo', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Failed to upload profile photo');
    }

    const result = await response.json();
    return result.data;
  },

  /**
   * Get profile photo information
   */
  async getProfilePhotoInfo(): Promise<{
    hasProfilePhoto: boolean;
    profilePictureUrl?: string;
    memberInfo: {
      id: string;
      firstName: string;
      lastName: string;
      employerId: string;
    };
  }> {
    const response = await apiRequest<{
      hasProfilePhoto: boolean;
      profilePictureUrl?: string;
      memberInfo: {
        id: string;
        firstName: string;
        lastName: string;
        employerId: string;
      };
    }>('GET', '/api/member/profile/photo');
    if (!response.success || !response.data) {
      throw new Error('Failed to fetch profile photo info');
    }
    return response.data;
  },

  /**
   * Delete profile photo
   */
  async deleteProfilePhoto(): Promise<{
    message: string;
    deletedS3Key: string;
  }> {
    const response = await apiRequest<{
      message: string;
      deletedS3Key: string;
    }>('DELETE', '/api/member/profile/photo');
    if (!response.success || !response.data) {
      throw new Error('Failed to delete profile photo');
    }
    return response.data;
  },
};


// Export the general API request function for other services
export { apiRequest };
