import { Request, Response } from 'express';
import { CompanyService } from '../services/CompanyService';
import { logger } from '../../../utils/logger';
import { 
  createCompanySchema, 
  updateCompanySchema, 
  companyQuerySchema,
  CompanyQueryParams 
} from '../types/company';
import { HttpStatus } from '@aperion/shared';

export class CompanyController {
  private companyService: CompanyService;

  constructor() {
    this.companyService = new CompanyService();
  }

  /**
   * Get all companies with filtering and pagination
   */
  getCompanies = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate and parse query parameters
      const validationResult = companyQuerySchema.safeParse(req.query);
      
      if (!validationResult.success) {
        logger.warn('Invalid query parameters for getCompanies:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const params: CompanyQueryParams = validationResult.data;
      const result = await this.companyService.getCompanies(params);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getCompanies controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get company by ID
   */
  getCompanyById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Company ID is required'
          }
        });
        return;
      }

      const result = await this.companyService.getCompanyById(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'COMPANY_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getCompanyById controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Create a new company
   */
  createCompany = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request body
      const validationResult = createCompanySchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for createCompany:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const companyData = validationResult.data;
      const result = await this.companyService.createCompany(companyData);

      if (result.success) {
        res.status(HttpStatus.CREATED).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in createCompany controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Update a company
   */
  updateCompany = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Company ID is required'
          }
        });
        return;
      }

      // Validate request body
      const validationResult = updateCompanySchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for updateCompany:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const companyData = validationResult.data;
      const result = await this.companyService.updateCompany(id, companyData);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'COMPANY_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in updateCompany controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Delete a company
   */
  deleteCompany = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Company ID is required'
          }
        });
        return;
      }

      const result = await this.companyService.deleteCompany(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'COMPANY_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in deleteCompany controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get company statistics
   */
  getCompanyStatistics = async (_req: Request, res: Response): Promise<void> => {
    try {
      const result = await this.companyService.getCompanyStatistics();

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getCompanyStatistics controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Search companies
   */
  searchCompanies = async (req: Request, res: Response): Promise<void> => {
    try {
      const { q: searchTerm, limit } = req.query;

      if (!searchTerm || typeof searchTerm !== 'string') {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Search term is required'
          }
        });
        return;
      }

      const searchLimit = limit ? parseInt(limit as string, 10) : 10;
      const result = await this.companyService.searchCompanies(searchTerm, searchLimit);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in searchCompanies controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };
}
