import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import WellnessScoreCard from '@/components/dashboard/WellnessScoreCard';
import RewardsCard from '@/components/dashboard/RewardsCard';
import GoalCard from '@/components/dashboard/GoalCard';
import HealthIDCard from '@/components/dashboard/HealthIDCard';
import { RecommendationsSection } from '@/components/recommendations/RecommendationsSection';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { CollapsibleSection } from '@/components/ui/collapsible-section';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext
} from "@/components/ui/carousel";
import {
  TrendingUp,
  Award,
  Flag,
  Activity,
  Heart,
  Zap
} from 'lucide-react';

// Mock data - this would come from API in real implementation
const mockUser = {
  id: '1',
  firstName: 'John',
  lastName: 'Member',
  email: '<EMAIL>'
};

const mockHealthPlan = {
  id: '1',
  planName: 'Horizon Comprehensive Plan',
  planType: 'PPO',
  memberId: 'MEM987654321',
  groupNumber: 'GRP1234',
  coveragePeriodStart: '2023-01-01',
  coveragePeriodEnd: '2023-12-31',
  primaryCareVisitCopay: 25,
  specialistVisitCopay: 50,
  emergencyRoomCopay: 150,
  individualDeductible: 1500,
  familyDeductible: 3000,
  individualOutOfPocketMax: 5000,
  familyOutOfPocketMax: 10000,
  createdAt: '2023-01-01',
  updatedAt: '2023-01-01'
};

const mockWellnessScore = {
  id: '1',
  userId: '1',
  score: 75,
  category: 'Good',
  feedback: 'You\'re doing great! Keep up the good work.',
  breakdown: {
    physical: 80,
    mental: 75,
    nutrition: 82,
    sleep: 76
  },
  lastUpdated: '2023-01-15',
  createdAt: '2023-01-01',
  updatedAt: '2023-01-15'
};

const mockRewards = {
  id: '1',
  userId: '1',
  currentPoints: 750,
  totalEarnedPoints: 2500,
  nextRewardThreshold: 1000,
  availableRewards: [],
  recentActivity: [],
  createdAt: '2023-01-01',
  updatedAt: '2023-01-15'
};

const mockGoals = [
  {
    id: '1',
    userId: '1',
    title: 'Daily Step Count',
    description: 'Reach 10,000 steps daily for cardiovascular health',
    category: 'fitness',
    targetValue: 10000,
    currentValue: 7500,
    unit: 'steps',
    icon: 'directions_walk',
    isCompleted: false,
    lastUpdated: '2023-01-15',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-15'
  },
  {
    id: '2',
    userId: '1',
    title: 'Water Intake',
    description: 'Drink 8 glasses of water daily for proper hydration',
    category: 'nutrition',
    targetValue: 8,
    currentValue: 6,
    unit: 'glasses',
    icon: 'local_drink',
    isCompleted: false,
    lastUpdated: '2023-01-15',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-15'
  },
  {
    id: '3',
    userId: '1',
    title: 'Daily Step Count',
    description: 'Reach 10,000 steps daily for cardiovascular health',
    category: 'fitness',
    targetValue: 10000,
    currentValue: 7500,
    unit: 'steps',
    icon: 'directions_walk',
    isCompleted: false,
    lastUpdated: '2023-01-15',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-15'
  }
];

const Dashboard: React.FC = () => {
  // State for collapsible sections
  const [healthIdOpen, setHealthIdOpen] = useState(true);
  const [wellnessOverviewOpen, setWellnessOverviewOpen] = useState(true);
  const [goalsOpen, setGoalsOpen] = useState(true);
  const [recommendationsOpen, setRecommendationsOpen] = useState(true);

  // State for adding goals
  const [isAddingGoal, setIsAddingGoal] = useState(false);

  // Loading states for conditional skeleton display
  const [isHealthPlanLoading, setIsHealthPlanLoading] = useState(true);
  const [isScoreLoading, setIsScoreLoading] = useState(true);
  const [isRewardsLoading, setIsRewardsLoading] = useState(true);
  const [isGoalsLoading, setIsGoalsLoading] = useState(true);

  // Mock queries to simulate the original structure
  const user = mockUser;
  const healthPlan = mockHealthPlan;
  const wellnessScore = mockWellnessScore;
  const rewards = mockRewards;
  const goals = mockGoals;

  // Determine if wellness score needs attention
  const scoreNeedsAttention = wellnessScore && wellnessScore.score < 60;

  // Simulate data loading - in real app this would be from API calls
  useEffect(() => {
    // Simulate loading delays for different data types
    const loadHealthPlan = setTimeout(() => setIsHealthPlanLoading(false), 800);
    const loadScore = setTimeout(() => setIsScoreLoading(false), 1200);
    const loadRewards = setTimeout(() => setIsRewardsLoading(false), 1000);
    const loadGoals = setTimeout(() => setIsGoalsLoading(false), 1400);

    return () => {
      clearTimeout(loadHealthPlan);
      clearTimeout(loadScore);
      clearTimeout(loadRewards);
      clearTimeout(loadGoals);
    };
  }, []);

  // Handle adding a new goal
  const handleAddGoal = async () => {
    setIsAddingGoal(true);
    // Simulate API call
    setTimeout(() => {
      setIsAddingGoal(false);
    }, 1000);
  };

  // Skeleton loader components - authentic HealthHorizon style
  const HealthIDSkeleton = () => (
    <div className="bg-white rounded-lg shadow-sm p-6 mt-4">
      <div className="space-y-4">
        <Skeleton className="h-6 w-48" />
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-28" />
          </div>
        </div>
        <Skeleton className="h-16 w-full" />
      </div>
    </div>
  );

  const WellnessCardSkeleton = () => (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="space-y-4">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-2 w-full" />
      </div>
    </div>
  );

  const GoalsSkeleton = () => (
    <div className="mt-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm p-6">
            <div className="space-y-4">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-2 w-full" />
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-12" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Health ID Section */}
      <CollapsibleSection
        title="Health ID Card"
        icon={<i className="material-icons text-[hsl(var(--wellness-purple))]">badge</i>}
        iconColor="text-[hsl(var(--wellness-purple))]"
        defaultOpen={healthIdOpen}
        className="mb-6 transition-all duration-300 hover:shadow-md"
        animation="slide"
      >
        <div className="fade-in">
          {isHealthPlanLoading ? (
            <HealthIDSkeleton />
          ) : (
            <div className="card-transition mt-4">
              <HealthIDCard
                healthPlan={healthPlan}
                userName={user ? `${user.firstName} ${user.lastName}` : undefined}
              />
            </div>
          )}
        </div>
      </CollapsibleSection>

      {/* Dashboard Overview Section */}
      <CollapsibleSection
        title="Your Wellness Overview"
        icon={<Activity size={20} />}
        iconColor="text-[hsl(var(--wellness-blue))]"
        defaultOpen={wellnessOverviewOpen}
        className="mb-6 transition-all duration-300 hover:shadow-md"
        animation="slide"
        badge={scoreNeedsAttention ? (
          <Badge variant="destructive" className="ml-2 animate-pulse">
            Needs Attention
          </Badge>
        ) : null}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
          <div className="fade-in-delay-1">
            {isScoreLoading ? (
              <WellnessCardSkeleton />
            ) : (
              <div className="card-transition relative">
                {scoreNeedsAttention && (
                  <div className="absolute -top-1 -right-1 z-10">
                    <Badge variant="destructive" className="animate-pulse">
                      <i className="material-icons text-xs mr-1">priority_high</i>
                      Attention needed
                    </Badge>
                  </div>
                )}
                <WellnessScoreCard
                  wellnessScore={wellnessScore}
                  isLoading={false}
                />
              </div>
            )}
          </div>

          <div className="fade-in-delay-2">
            {isRewardsLoading ? (
              <WellnessCardSkeleton />
            ) : (
              <div className="card-transition">
                <RewardsCard
                  rewards={rewards}
                  isLoading={false}
                />
              </div>
            )}
          </div>
        </div>
      </CollapsibleSection>

      {/* Wellness Goals Section */}
      <CollapsibleSection
        title="Your Wellness Goals"
        icon={<Flag size={20} />}
        iconColor="text-[hsl(var(--wellness-green))]"
        defaultOpen={goalsOpen}
        className="mb-6 transition-all duration-300 hover:shadow-md"
        animation="slide"
        rightContent={
          <Button
            onClick={handleAddGoal}
            className="text-sm bg-[hsl(var(--wellness-green))] text-white px-3 py-1.5 rounded flex items-center hover:opacity-90 transition"
            disabled={isAddingGoal}
          >
            {isAddingGoal ? (
              <>
                <i className="material-icons animate-spin text-sm mr-1">refresh</i>
                Adding...
              </>
            ) : (
              <>
                <i className="material-icons text-sm mr-1">add</i>
                Add Goal
              </>
            )}
          </Button>
        }
      >
        {isGoalsLoading ? (
          <GoalsSkeleton />
        ) : goals.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center card-transition mt-4">
            <div className="flex justify-center">
              <Heart className="h-16 w-16 text-[hsl(var(--wellness-teal))] animate-pulse" />
            </div>
            <h3 className="mt-4 text-lg font-medium">No Wellness Goals</h3>
            <p className="mt-2 text-neutral-500 text-sm">
              Start tracking your wellness journey by adding your first goal.
            </p>
            <Button
              onClick={handleAddGoal}
              className="mt-4 bg-[hsl(var(--wellness-green))] text-white hover:opacity-90 transition"
              disabled={isAddingGoal}
            >
              {isAddingGoal ? (
                <>
                  <i className="material-icons animate-spin text-sm mr-1">refresh</i>
                  Adding...
                </>
              ) : (
                <>
                  <i className="material-icons text-sm mr-1">add</i>
                  Add Your First Goal
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="relative mt-4">
            <Carousel className="w-full">
              <CarouselContent className="-ml-4">
                {goals.map((goal, index) => (
                  <CarouselItem key={goal.id} className="pl-4 md:basis-1/2 lg:basis-1/3">
                    <div className={`fade-in-delay-${index % 3 + 1}`}>
                      <div className="card-transition h-full">
                        <GoalCard goal={goal} />
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <div className="flex justify-end gap-2 mt-4">
                <div>
                  <CarouselPrevious className="relative static left-auto right-auto translate-y-0" />
                </div>
                <div>
                  <CarouselNext className="relative static left-auto right-auto translate-y-0" />
                </div>
              </div>
            </Carousel>
          </div>
        )}
      </CollapsibleSection>

      {/* Recommendations Section */}
      <CollapsibleSection
        title="Personalized Recommendations"
        icon={<Zap size={20} />}
        iconColor="text-[#7c3aed]"
        defaultOpen={recommendationsOpen}
        className="mb-6 transition-all duration-300 hover:shadow-md"
        animation="slide"
      >
        <div className="mt-4">
          <RecommendationsSection maxItems={3} />
        </div>
      </CollapsibleSection>
    </div>
  );


};

export default Dashboard;
