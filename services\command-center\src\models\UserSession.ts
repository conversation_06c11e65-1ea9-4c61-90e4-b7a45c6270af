import { Pool } from 'pg';
import { config } from '../config';
import { logger } from '../../../utils/logger';
import {
  UserSession,
  CreateUserSessionRequest,
  UpdateUserSessionRequest,
  UserSessionQueryParams,
  UserSessionStatistics
} from '../types/session';
import { ApiMeta } from '@aperion/shared';

export class UserSessionModel {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.user,
      password: config.database.password,
      ssl: config.database.ssl,
      max: config.database.maxConnections,
      idleTimeoutMillis: config.database.idleTimeoutMillis,
      connectionTimeoutMillis: config.database.connectionTimeoutMillis,
    });

    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
  }

  /**
   * Execute a database query
   */
  private async query(text: string, params?: any[]) {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      logger.debug('Executed query', { text, duration, rows: result.rowCount });
      return result;
    } catch (error) {
      logger.error('Query error', { text, params, error });
      throw error;
    }
  }

  /**
   * Get all user sessions with filtering and pagination
   */
  async getUserSessions(params: UserSessionQueryParams): Promise<{ sessions: UserSession[]; meta: ApiMeta }> {
    const { page, limit, userId, isActive, dateFrom, dateTo, sortBy, sortOrder } = params;
    const offset = (page - 1) * limit;

    // Build WHERE clause
    const conditions: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (userId) {
      conditions.push(`us.user_id = $${paramIndex}`);
      queryParams.push(userId);
      paramIndex++;
    }

    if (isActive !== undefined) {
      conditions.push(`us.is_active = $${paramIndex}`);
      queryParams.push(isActive);
      paramIndex++;
    }

    if (dateFrom) {
      conditions.push(`us.login_at >= $${paramIndex}`);
      queryParams.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      conditions.push(`us.login_at <= $${paramIndex}`);
      queryParams.push(dateTo);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    const orderByClause = `ORDER BY us.${sortBy === 'loginAt' ? 'login_at' :
                                          sortBy === 'logoutAt' ? 'logout_at' :
                                          sortBy === 'lastActivityAt' ? 'last_activity_at' :
                                          sortBy === 'createdAt' ? 'created_at' : sortBy} ${sortOrder}`;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM command_center.user_sessions us
      ${whereClause}
    `;
    const countResult = await this.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get user sessions with user details
    const sessionsQuery = `
      SELECT
        us.id,
        us.user_id as "userId",
        us.session_id as "sessionId",
        us.login_at as "loginAt",
        us.logout_at as "logoutAt",
        us.last_activity_at as "lastActivityAt",
        us.ip_address as "ipAddress",
        us.user_agent as "userAgent",
        us.is_active as "isActive",
        us.created_at as "createdAt",
        json_build_object(
          'id', ur.id,
          'firstName', ur.first_name,
          'lastName', ur.last_name,
          'email', ur.email,
          'role', CASE
            WHEN ur.user_type = 'member' THEN 'member'
            WHEN ur.user_type = 'employer' THEN 'employer'
            WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
            WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
            WHEN ur.user_type = 'system_admin' THEN 'system-admin'
            ELSE ur.user_type
          END
        ) as user
      FROM command_center.user_sessions us
      LEFT JOIN shared_data.user_references ur ON us.user_id = ur.id
      ${whereClause}
      ${orderByClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const result = await this.query(sessionsQuery, queryParams);

    const meta: ApiMeta = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    };

    return {
      sessions: result.rows,
      meta,
    };
  }

  /**
   * Get user session by ID
   */
  async getUserSessionById(id: string): Promise<UserSession | null> {
    const query = `
      SELECT
        us.id,
        us.user_id as "userId",
        us.session_id as "sessionId",
        us.login_at as "loginAt",
        us.logout_at as "logoutAt",
        us.last_activity_at as "lastActivityAt",
        us.ip_address as "ipAddress",
        us.user_agent as "userAgent",
        us.is_active as "isActive",
        us.created_at as "createdAt",
        json_build_object(
          'id', ur.id,
          'firstName', ur.first_name,
          'lastName', ur.last_name,
          'email', ur.email,
          'role', CASE
            WHEN ur.user_type = 'member' THEN 'member'
            WHEN ur.user_type = 'employer' THEN 'employer'
            WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
            WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
            WHEN ur.user_type = 'system_admin' THEN 'system-admin'
            ELSE ur.user_type
          END
        ) as user
      FROM command_center.user_sessions us
      LEFT JOIN shared_data.user_references ur ON us.user_id = ur.id
      WHERE us.id = $1
    `;

    try {
      const result = await this.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error getting user session by ID:', error);
      throw new Error('Failed to get user session');
    }
  }

  /**
   * Create a new user session
   */
  async createUserSession(sessionData: CreateUserSessionRequest): Promise<UserSession> {
    const query = `
      INSERT INTO command_center.user_sessions (
        user_id,
        session_id,
        ip_address,
        user_agent
      ) VALUES ($1, $2, $3, $4)
      RETURNING
        id,
        user_id as "userId",
        session_id as "sessionId",
        login_at as "loginAt",
        logout_at as "logoutAt",
        last_activity_at as "lastActivityAt",
        ip_address as "ipAddress",
        user_agent as "userAgent",
        is_active as "isActive",
        created_at as "createdAt"
    `;

    try {
      const result = await this.query(query, [
        sessionData.userId,
        sessionData.sessionId,
        sessionData.ipAddress || null,
        sessionData.userAgent || null
      ]);

      return result.rows[0];
    } catch (error) {
      logger.error('Error creating user session:', error);
      if ((error as any).code === '23505') {
        throw new Error('Session ID already exists');
      }
      throw new Error('Failed to create user session');
    }
  }

  /**
   * Update a user session
   */
  async updateUserSession(id: string, sessionData: UpdateUserSessionRequest): Promise<UserSession | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(sessionData).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = key === 'lastActivityAt' ? 'last_activity_at' :
                       key === 'logoutAt' ? 'logout_at' :
                       key === 'isActive' ? 'is_active' : key;
        
        fields.push(`${dbField} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    values.push(id);

    const query = `
      UPDATE command_center.user_sessions
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING
        id,
        user_id as "userId",
        session_id as "sessionId",
        login_at as "loginAt",
        logout_at as "logoutAt",
        last_activity_at as "lastActivityAt",
        ip_address as "ipAddress",
        user_agent as "userAgent",
        is_active as "isActive",
        created_at as "createdAt"
    `;

    try {
      const result = await this.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error updating user session:', error);
      throw new Error('Failed to update user session');
    }
  }

  /**
   * Delete a user session
   */
  async deleteUserSession(id: string): Promise<boolean> {
    const query = 'DELETE FROM command_center.user_sessions WHERE id = $1';

    try {
      const result = await this.query(query, [id]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      logger.error('Error deleting user session:', error);
      throw new Error('Failed to delete user session');
    }
  }

  /**
   * Terminate user session (set logout time and inactive)
   */
  async terminateUserSession(id: string): Promise<UserSession | null> {
    const query = `
      UPDATE command_center.user_sessions
      SET logout_at = NOW(), is_active = false
      WHERE id = $1
      RETURNING
        id,
        user_id as "userId",
        session_id as "sessionId",
        login_at as "loginAt",
        logout_at as "logoutAt",
        last_activity_at as "lastActivityAt",
        ip_address as "ipAddress",
        user_agent as "userAgent",
        is_active as "isActive",
        created_at as "createdAt"
    `;

    try {
      const result = await this.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error terminating user session:', error);
      throw new Error('Failed to terminate user session');
    }
  }

  /**
   * Update session activity (update last_activity_at)
   */
  async updateSessionActivity(sessionId: string): Promise<boolean> {
    const query = `
      UPDATE command_center.user_sessions
      SET last_activity_at = NOW()
      WHERE session_id = $1 AND is_active = true
    `;

    try {
      const result = await this.query(query, [sessionId]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      logger.error('Error updating session activity:', error);
      throw new Error('Failed to update session activity');
    }
  }

  /**
   * Get user session statistics
   */
  async getUserSessionStatistics(): Promise<UserSessionStatistics> {
    const queries = [
      // Total counts by status
      `SELECT
         COUNT(*) as total,
         COUNT(*) FILTER (WHERE is_active = true) as active,
         COUNT(*) FILTER (WHERE is_active = false) as inactive,
         AVG(EXTRACT(EPOCH FROM (COALESCE(logout_at, NOW()) - login_at))/60) as avg_duration
       FROM command_center.user_sessions`,

      // Sessions by time period
      `SELECT
         COUNT(*) FILTER (WHERE login_at >= CURRENT_DATE) as today,
         COUNT(*) FILTER (WHERE login_at >= CURRENT_DATE - INTERVAL '7 days') as this_week,
         COUNT(*) FILTER (WHERE login_at >= CURRENT_DATE - INTERVAL '30 days') as this_month
       FROM command_center.user_sessions`,

      // Top users by session count
      `SELECT
         us.user_id as "userId",
         ur.first_name as "firstName",
         ur.last_name as "lastName",
         COUNT(*) as "sessionCount",
         MAX(us.last_activity_at) as "lastActivity"
       FROM command_center.user_sessions us
       LEFT JOIN shared_data.user_references ur ON us.user_id = ur.id
       GROUP BY us.user_id, ur.first_name, ur.last_name
       ORDER BY "sessionCount" DESC
       LIMIT 10`,

      // Sessions by hour of day
      `SELECT
         EXTRACT(HOUR FROM login_at) as hour,
         COUNT(*) as count
       FROM command_center.user_sessions
       WHERE login_at >= CURRENT_DATE - INTERVAL '30 days'
       GROUP BY EXTRACT(HOUR FROM login_at)
       ORDER BY hour`
    ];

    try {
      const [statusResult, periodResult, topUsersResult, hourlyResult] = await Promise.all(
        queries.map(query => this.query(query))
      );

      const statusData = statusResult.rows[0];
      const periodData = periodResult.rows[0];

      return {
        totalSessions: parseInt(statusData.total),
        activeSessions: parseInt(statusData.active),
        inactiveSessions: parseInt(statusData.inactive),
        averageSessionDuration: parseFloat(statusData.avg_duration) || 0,
        sessionsToday: parseInt(periodData.today),
        sessionsThisWeek: parseInt(periodData.this_week),
        sessionsThisMonth: parseInt(periodData.this_month),
        topUsers: topUsersResult.rows,
        sessionsByHour: hourlyResult.rows,
      };
    } catch (error) {
      logger.error('Error getting user session statistics:', error);
      throw new Error('Failed to get user session statistics');
    }
  }
}
