import { z } from 'zod';

// User session interface
export interface UserSession {
  id: string;
  userId: number;
  sessionId: string;
  loginAt: Date;
  logoutAt?: Date;
  lastActivityAt: Date;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
  createdAt: Date;
  // Joined data
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    role: string;
  };
}

// Create user session request schema
export const createUserSessionSchema = z.object({
  userId: z.number().min(1, 'User ID is required'),
  sessionId: z.string().min(1, 'Session ID is required'),
  ipAddress: z.string().ip().optional(),
  userAgent: z.string().optional(),
});

// Update user session request schema
export const updateUserSessionSchema = z.object({
  lastActivityAt: z.string().datetime().optional(),
  logoutAt: z.string().datetime().optional(),
  isActive: z.boolean().optional(),
});

// User session query parameters schema
export const userSessionQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('10'),
  userId: z.string().transform(Number).pipe(z.number().min(1)).optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  sortBy: z.enum(['loginAt', 'logoutAt', 'lastActivityAt', 'createdAt']).default('lastActivityAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Type definitions from schemas
export type CreateUserSessionRequest = z.infer<typeof createUserSessionSchema>;
export type UpdateUserSessionRequest = z.infer<typeof updateUserSessionSchema>;
export type UserSessionQueryParams = z.infer<typeof userSessionQuerySchema>;

// User session statistics interface
export interface UserSessionStatistics {
  totalSessions: number;
  activeSessions: number;
  inactiveSessions: number;
  averageSessionDuration: number; // in minutes
  sessionsToday: number;
  sessionsThisWeek: number;
  sessionsThisMonth: number;
  topUsers: Array<{
    userId: number;
    firstName: string;
    lastName: string;
    sessionCount: number;
    lastActivity: Date;
  }>;
  sessionsByHour: Array<{
    hour: number;
    count: number;
  }>;
}

// Session activity interface for tracking user activity
export interface SessionActivity {
  sessionId: string;
  userId: number;
  activity: string;
  timestamp: Date;
  metadata?: any; // JSONB field for additional activity data
}

// Bulk session operations
export interface BulkSessionOperation {
  sessionIds: string[];
  operation: 'terminate' | 'extend' | 'update_activity';
  data?: any;
}
