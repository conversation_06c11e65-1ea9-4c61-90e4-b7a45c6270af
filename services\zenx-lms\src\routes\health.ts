import { Router } from 'express';
import { HealthCheckResponse } from '@aperion/shared';

const router = Router();

router.get('/', async (_req, res) => {
  const healthCheck: HealthCheckResponse = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'zenx-lms',
    version: '1.0.0',
    uptime: process.uptime(),
  };

  res.json(healthCheck);
});

export { router as healthRouter };
