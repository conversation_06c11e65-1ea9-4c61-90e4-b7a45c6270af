import { SubscriptionPlanModel } from '../models/SubscriptionPlan';
import { logger } from '../../../utils/logger';
import {
  SubscriptionPlan,
  CreateSubscriptionPlanRequest,
  UpdateSubscriptionPlanRequest,
  SubscriptionPlanQueryParams,
  SubscriptionPlanStatistics
} from '../types/subscription';
import { ApiResponse, ApiMeta } from '@aperion/shared';
import { createSuccessResponse, createErrorResponse } from '../utils/apiResponse';

export class SubscriptionService {
  private subscriptionPlanModel: SubscriptionPlanModel;

  constructor() {
    this.subscriptionPlanModel = new SubscriptionPlanModel();
  }

  /**
   * Get all subscription plans with filtering and pagination
   */
  async getSubscriptionPlans(params: SubscriptionPlanQueryParams): Promise<ApiResponse<{ plans: SubscriptionPlan[]; meta: ApiMeta }>> {
    try {
      logger.info('Getting subscription plans with params:', params);
      
      const result = await this.subscriptionPlanModel.getSubscriptionPlans(params);
      
      logger.info('Successfully retrieved subscription plans', {
        count: result.plans.length,
        total: result.meta.total
      });

      return createSuccessResponse(result);
    } catch (error) {
      logger.error('Error getting subscription plans:', error);
      return createErrorResponse(
        'SUBSCRIPTION_PLANS_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get subscription plans'
      );
    }
  }

  /**
   * Get subscription plan by ID
   */
  async getSubscriptionPlanById(id: string): Promise<ApiResponse<SubscriptionPlan>> {
    try {
      logger.info('Getting subscription plan by ID:', { id });
      
      const plan = await this.subscriptionPlanModel.getSubscriptionPlanById(id);
      
      if (!plan) {
        return createErrorResponse('SUBSCRIPTION_PLAN_NOT_FOUND', 'Subscription plan not found');
      }

      logger.info('Successfully retrieved subscription plan:', { id, name: plan.name });

      return createSuccessResponse(plan);
    } catch (error) {
      logger.error('Error getting subscription plan by ID:', error);
      return createErrorResponse(
        'SUBSCRIPTION_PLAN_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get subscription plan'
      );
    }
  }

  /**
   * Create a new subscription plan
   */
  async createSubscriptionPlan(planData: CreateSubscriptionPlanRequest): Promise<ApiResponse<SubscriptionPlan>> {
    try {
      logger.info('Creating new subscription plan:', { name: planData.name });
      
      // Validate plan data
      const validationErrors = this.validateSubscriptionPlanData(planData);
      if (validationErrors.length > 0) {
        return createErrorResponse('VALIDATION_ERROR', validationErrors.join(', '));
      }

      const plan = await this.subscriptionPlanModel.createSubscriptionPlan(planData);

      logger.info('Successfully created subscription plan:', { id: plan.id, name: plan.name });

      return createSuccessResponse(plan);
    } catch (error) {
      logger.error('Error creating subscription plan:', error);
      return createErrorResponse(
        'SUBSCRIPTION_PLAN_CREATE_ERROR',
        error instanceof Error ? error.message : 'Failed to create subscription plan'
      );
    }
  }

  /**
   * Update a subscription plan
   */
  async updateSubscriptionPlan(id: string, planData: UpdateSubscriptionPlanRequest): Promise<ApiResponse<SubscriptionPlan>> {
    try {
      logger.info('Updating subscription plan:', { id, data: planData });
      
      // Validate plan data
      const validationErrors = this.validateSubscriptionPlanData(planData);
      if (validationErrors.length > 0) {
        return createErrorResponse('VALIDATION_ERROR', validationErrors.join(', '));
      }

      const plan = await this.subscriptionPlanModel.updateSubscriptionPlan(id, planData);

      if (!plan) {
        return createErrorResponse('SUBSCRIPTION_PLAN_NOT_FOUND', 'Subscription plan not found');
      }

      logger.info('Successfully updated subscription plan:', { id, name: plan.name });

      return createSuccessResponse(plan);
    } catch (error) {
      logger.error('Error updating subscription plan:', error);
      return createErrorResponse(
        'SUBSCRIPTION_PLAN_UPDATE_ERROR',
        error instanceof Error ? error.message : 'Failed to update subscription plan'
      );
    }
  }

  /**
   * Delete a subscription plan
   */
  async deleteSubscriptionPlan(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    try {
      logger.info('Deleting subscription plan:', { id });
      
      const deleted = await this.subscriptionPlanModel.deleteSubscriptionPlan(id);
      
      if (!deleted) {
        return createErrorResponse('SUBSCRIPTION_PLAN_NOT_FOUND', 'Subscription plan not found');
      }

      logger.info('Successfully deleted subscription plan:', { id });

      return createSuccessResponse({ deleted: true });
    } catch (error) {
      logger.error('Error deleting subscription plan:', error);
      return createErrorResponse(
        'SUBSCRIPTION_PLAN_DELETE_ERROR',
        error instanceof Error ? error.message : 'Failed to delete subscription plan'
      );
    }
  }

  /**
   * Get subscription plan statistics
   */
  async getSubscriptionPlanStatistics(): Promise<ApiResponse<SubscriptionPlanStatistics>> {
    try {
      logger.info('Getting subscription plan statistics');
      
      const statistics = await this.subscriptionPlanModel.getSubscriptionPlanStatistics();
      
      logger.info('Successfully retrieved subscription plan statistics:', {
        totalPlans: statistics.totalPlans
      });

      return createSuccessResponse(statistics);
    } catch (error) {
      logger.error('Error getting subscription plan statistics:', error);
      return createErrorResponse(
        'SUBSCRIPTION_PLAN_STATISTICS_ERROR',
        error instanceof Error ? error.message : 'Failed to get subscription plan statistics'
      );
    }
  }

  /**
   * Get active subscription plans only
   */
  async getActiveSubscriptionPlans(): Promise<ApiResponse<SubscriptionPlan[]>> {
    try {
      logger.info('Getting active subscription plans');
      
      const params: SubscriptionPlanQueryParams = {
        page: 1,
        limit: 100,
        isActive: true,
        sortBy: 'name',
        sortOrder: 'asc'
      };

      const result = await this.subscriptionPlanModel.getSubscriptionPlans(params);
      
      logger.info('Successfully retrieved active subscription plans:', {
        count: result.plans.length
      });

      return createSuccessResponse(result.plans);
    } catch (error) {
      logger.error('Error getting active subscription plans:', error);
      return createErrorResponse(
        'ACTIVE_SUBSCRIPTION_PLANS_ERROR',
        error instanceof Error ? error.message : 'Failed to get active subscription plans'
      );
    }
  }

  /**
   * Validate subscription plan data
   */
  private validateSubscriptionPlanData(data: CreateSubscriptionPlanRequest | UpdateSubscriptionPlanRequest): string[] {
    const errors: string[] = [];

    if ('name' in data && data.name && data.name.length > 100) {
      errors.push('Plan name must be 100 characters or less');
    }

    if ('priceMonthly' in data && data.priceMonthly !== undefined && data.priceMonthly < 0) {
      errors.push('Monthly price must be positive');
    }

    if ('priceYearly' in data && data.priceYearly !== undefined && data.priceYearly < 0) {
      errors.push('Yearly price must be positive');
    }

    if ('maxUsers' in data && data.maxUsers !== undefined && data.maxUsers < 1) {
      errors.push('Max users must be at least 1');
    }

    // Validate that custom plans have appropriate pricing
    if ('planType' in data && data.planType === 'custom') {
      if ('priceMonthly' in data && data.priceMonthly !== undefined && data.priceMonthly !== null) {
        logger.warn('Custom plans typically should not have fixed pricing');
      }
    }

    return errors;
  }

  /**
   * Search subscription plans by name or description
   */
  async searchSubscriptionPlans(searchTerm: string, limit: number = 10): Promise<ApiResponse<SubscriptionPlan[]>> {
    try {
      logger.info('Searching subscription plans:', { searchTerm, limit });
      
      const params: SubscriptionPlanQueryParams = {
        page: 1,
        limit,
        search: searchTerm,
        sortBy: 'name',
        sortOrder: 'asc'
      };

      const result = await this.subscriptionPlanModel.getSubscriptionPlans(params);
      
      logger.info('Successfully searched subscription plans:', {
        searchTerm,
        count: result.plans.length
      });

      return createSuccessResponse(result.plans);
    } catch (error) {
      logger.error('Error searching subscription plans:', error);
      return createErrorResponse(
        'SUBSCRIPTION_PLAN_SEARCH_ERROR',
        error instanceof Error ? error.message : 'Failed to search subscription plans'
      );
    }
  }
}
