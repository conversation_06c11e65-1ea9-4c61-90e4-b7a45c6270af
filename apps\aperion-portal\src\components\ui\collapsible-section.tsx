import React, { useState } from 'react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@radix-ui/react-collapsible';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface CollapsibleSectionProps {
  title: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  defaultOpen?: boolean;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  iconColor?: string;
  animation?: 'slide' | 'fade' | 'none';
  badge?: React.ReactNode;
  rightContent?: React.ReactNode;
  showExpandIcon?: boolean;
}

export function CollapsibleSection({
  title,
  icon,
  children,
  defaultOpen = true,
  className,
  headerClassName,
  contentClassName,
  iconColor,
  animation = 'slide',
  badge,
  rightContent,
  showExpandIcon = true
}: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const animationStyles = {
    slide: 'transition-all duration-300 ease-in-out',
    fade: 'transition-opacity duration-300 data-[state=closed]:opacity-0 data-[state=open]:opacity-100',
    none: ''
  };

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className={cn(
        'bg-white rounded-lg shadow-sm border border-[#f1f5f9] overflow-hidden',
        className
      )}
    >
      <CollapsibleTrigger className={cn(
        "flex w-full items-center justify-between p-4 hover:bg-slate-50 transition-colors cursor-pointer",
        headerClassName
      )}>
        <div className="flex items-center gap-2">
          {icon && (
            <div className={cn("text-lg", iconColor)}>
              {icon}
            </div>
          )}
          <h3 className="text-lg font-medium">{title}</h3>
          {badge && badge}
        </div>
        <div className="flex items-center gap-4">
          {rightContent}
          {showExpandIcon && (
            <div className="w-5 h-5 flex items-center justify-center text-gray-400">
              {isOpen ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
            </div>
          )}
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className={cn(animationStyles[animation], contentClassName)}>
        <div className="p-4 pt-0">
          {children}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
