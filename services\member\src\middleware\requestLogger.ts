import { Request, Response, NextFunction } from 'express';
import { generateRequestId, generateCorrelationId, logger } from '../utils/logger';

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      correlationId?: string;
      startTime?: number;
    }
  }
}



/**
 * Extract user context from request if available
 */
function getUserContext(req: Request): any {
  const user = (req as any).user;
  if (!user) return {};

  return {
    userId: user.id || user.sub,
    userEmail: user.email,
    userRole: user.role || user['custom:role'],
    userService: user['custom:service'],
  };
}

/**
 * Determine if request is from a health check or monitoring system
 */
function isHealthCheck(req: Request): boolean {
  const url = req.originalUrl || req.url;
  const userAgent = req.get('User-Agent')?.toLowerCase() || '';

  return url.includes('/health') ||
         url.includes('/status') ||
         userAgent.includes('health') ||
         userAgent.includes('monitor') ||
         userAgent.includes('probe');
}

export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  // Generate request ID and correlation ID using Pino utilities
  req.requestId = generateRequestId();
  req.correlationId = (req.headers['x-correlation-id'] as string) || generateCorrelationId();
  req.startTime = Date.now();

  // Add correlation ID to response headers (only if headers haven't been sent)
  if (req.correlationId && !res.headersSent) {
    res.setHeader('X-Correlation-ID', req.correlationId);
  }
  if (req.requestId && !res.headersSent) {
    res.setHeader('X-Request-ID', req.requestId);
  }

  // Add request ID and correlation ID to request for Pino HTTP logger
  (req as any).id = req.requestId;
  (req as any).correlationId = req.correlationId;

  // Get comprehensive request context
  const userContext = getUserContext(req);
  const isHealthCheckRequest = isHealthCheck(req);

  // Log minimal request context for business operations (only for non-health checks)
  if (!isHealthCheckRequest) {
    logger.info('Request initiated', {
      // Request identification
      requestId: req.requestId,
      correlationId: req.correlationId,

      // Business context (minimal for application log)
      operation: 'http_request',
      endpoint: req.originalUrl || req.url,
      method: req.method,

      // User context for business logic
      ...userContext,

      // Service context
      service: 'member',
      component: 'request-handler',

      // Timing
      timestamp: new Date().toISOString(),
      logType: 'business_operation',
    });
  }

  // Override res.end to capture comprehensive response data
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any, cb?: any): any {
    const responseTime = Date.now() - (req.startTime || Date.now());
    const userContext = getUserContext(req);
    const isHealthCheckRequest = isHealthCheck(req);

    // Add response time header (only if headers haven't been sent)
    if (!res.headersSent) {
      res.setHeader('X-Response-Time', `${responseTime}ms`);
    }

    // Log business operation completion (only for non-health checks)
    if (!isHealthCheckRequest) {
      const logLevel = res.statusCode >= 500 ? 'error' :
                      res.statusCode >= 400 ? 'warn' : 'info';

      logger[logLevel]('Request completed', {
        // Request identification
        requestId: req.requestId,
        correlationId: req.correlationId,

        // Business context
        operation: 'http_request',
        endpoint: req.originalUrl || req.url,
        method: req.method,

        // Operation result
        statusCode: res.statusCode,
        success: res.statusCode < 400,

        // Performance metrics
        responseTime: `${responseTime}ms`,
        responseTimeMs: responseTime,

        // User context
        ...userContext,

        // Service context
        service: 'member',
        component: 'request-handler',

        // Timing
        timestamp: new Date().toISOString(),
        logType: 'business_operation',

        // Error context (if applicable)
        ...(res.statusCode >= 400 && {
          errorCategory: res.statusCode >= 500 ? 'server_error' : 'client_error',
          errorType: res.statusCode === 401 ? 'unauthorized' :
                    res.statusCode === 403 ? 'forbidden' :
                    res.statusCode === 404 ? 'not_found' :
                    res.statusCode === 429 ? 'rate_limited' :
                    res.statusCode >= 500 ? 'internal_error' : 'client_error',
        }),
      });
    }

    // Call original end method
    return originalEnd.call(this, chunk, encoding, cb);
  };

  next();
};
