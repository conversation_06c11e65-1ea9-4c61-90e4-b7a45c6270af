import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import { Reward } from '@/lib/types';

interface RewardsCardProps {
  rewards?: Reward | null;
  isLoading?: boolean;
}

const RewardsCard: React.FC<RewardsCardProps> = ({ rewards, isLoading = false }) => {
  if (isLoading) {
    return (
      <Card className="h-full">
        <CardContent className="p-5 flex flex-col h-full">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-neutral-200 rounded w-36 animate-pulse"></div>
            <div className="h-4 w-4 bg-neutral-200 rounded animate-pulse"></div>
          </div>
          <div className="mb-4">
            <div className="h-3 bg-neutral-200 rounded w-28 mb-1 animate-pulse"></div>
            <div className="h-7 bg-neutral-200 rounded w-20 animate-pulse"></div>
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between mb-1">
              <div className="h-3 bg-neutral-200 rounded w-40 animate-pulse"></div>
              <div className="h-3 bg-neutral-200 rounded w-16 animate-pulse"></div>
            </div>
            <div className="w-full bg-neutral-200 rounded-full h-2"></div>
          </div>
          <div className="mt-4 h-4 bg-neutral-200 rounded w-28 animate-pulse"></div>
        </CardContent>
      </Card>
    );
  }

  if (!rewards) {
    return (
      <Card className="h-full">
        <CardContent className="p-5 flex flex-col h-full">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-neutral-600">Rewards & Incentives</h3>
            <span className="material-icons text-accent text-sm">redeem</span>
          </div>
          <div className="flex-1 flex items-center justify-center flex-col text-center">
            <span className="material-icons text-neutral-300 text-4xl mb-2">card_giftcard</span>
            <p className="text-sm text-neutral-500">No rewards information available</p>
          </div>
          <Link to="/member/rewards">
            <div className="mt-4 text-sm text-primary flex items-center cursor-pointer">
              Learn about Rewards
              <span className="material-icons text-sm ml-1">arrow_forward</span>
            </div>
          </Link>
        </CardContent>
      </Card>
    );
  }

  const currentPoints = rewards.currentPoints;
  const threshold = rewards.nextRewardThreshold;
  const pointsToGo = threshold - currentPoints > 0 ? threshold - currentPoints : 0;
  const progress = Math.min(Math.round((currentPoints / threshold) * 100), 100);

  return (
    <Card className="h-full">
      <CardContent className="p-5 flex flex-col h-full">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-neutral-600">Rewards & Incentives</h3>
          <span className="material-icons text-accent text-sm">redeem</span>
        </div>
        <div className="mb-4">
          <span className="text-xs text-neutral-500">Available Points</span>
          <div className="flex items-end">
            <p className="text-2xl font-bold">
              {currentPoints.toLocaleString()}
            </p>
            <span className="text-xs text-neutral-500 ml-1 mb-1">points</span>
          </div>
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-neutral-500">
              Next reward at {threshold.toLocaleString()} points
            </span>
            {pointsToGo > 0 ? (
              <span className="text-xs font-medium text-primary">
                {pointsToGo.toLocaleString()} to go
              </span>
            ) : (
              <span className="text-xs font-medium text-secondary">
                Ready to redeem!
              </span>
            )}
          </div>
          <div className="w-full bg-neutral-200 rounded-full h-2">
            <div 
              className="bg-accent h-2 rounded-full" 
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
        <Link to="/member/rewards">
          <div className="mt-4 text-sm text-primary flex items-center cursor-pointer">
            Redeem Rewards
            <span className="material-icons text-sm ml-1">arrow_forward</span>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
};

export default RewardsCard;
