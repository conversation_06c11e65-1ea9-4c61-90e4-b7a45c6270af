/**
 * Database Utility Classes for Aperion Health
 * Provides connection management and query helpers
 */

import { Pool, PoolClient } from 'pg';
import { DatabaseConfig, connectionOptions, healthCheckQuery, schemaQueries } from '../config/database.js';

export class DatabaseConnection {
  private pool: Pool;
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig) {
    this.config = config;
    this.pool = new Pool(connectionOptions.pg(config));

    // Handle pool errors
    this.pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
    });
  }

  /**
   * Get a client from the pool
   */
  async getClient(): Promise<PoolClient> {
    return this.pool.connect();
  }

  /**
   * Execute a query with automatic client management
   */
  async query(text: string, params?: any[]): Promise<any> {
    const client = await this.getClient();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }

  /**
   * Execute a transaction
   */
  async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.query(healthCheckQuery);
      return result.rows[0]?.health_check === 1;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  /**
   * Verify schema exists
   */
  async verifySchema(): Promise<boolean> {
    try {
      const result = await this.query(schemaQueries.checkSchema(this.config.schema));
      return result.rows.length > 0;
    } catch (error) {
      console.error('Schema verification failed:', error);
      return false;
    }
  }

  /**
   * List tables in schema
   */
  async listTables(): Promise<string[]> {
    try {
      const result = await this.query(schemaQueries.listTables(this.config.schema));
      return result.rows.map((row: any) => row.table_name);
    } catch (error) {
      console.error('Failed to list tables:', error);
      return [];
    }
  }

  /**
   * Close all connections
   */
  async close(): Promise<void> {
    await this.pool.end();
  }

  /**
   * Get pool statistics
   */
  getPoolStats() {
    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
    };
  }
}

export class DatabaseManager {
  private connections: Map<string, DatabaseConnection> = new Map();

  /**
   * Add a database connection
   */
  addConnection(name: string, config: DatabaseConfig): DatabaseConnection {
    const connection = new DatabaseConnection(config);
    this.connections.set(name, connection);
    return connection;
  }

  /**
   * Get a database connection
   */
  getConnection(name: string): DatabaseConnection {
    const connection = this.connections.get(name);
    if (!connection) {
      throw new Error(`Database connection '${name}' not found`);
    }
    return connection;
  }

  /**
   * Health check all connections
   */
  async healthCheckAll(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [name, connection] of this.connections) {
      results[name] = await connection.healthCheck();
    }

    return results;
  }

  /**
   * Close all connections
   */
  async closeAll(): Promise<void> {
    const promises = Array.from(this.connections.values()).map(conn => conn.close());
    await Promise.all(promises);
    this.connections.clear();
  }

  /**
   * Get statistics for all connections
   */
  getAllStats(): Record<string, any> {
    const stats: Record<string, any> = {};

    for (const [name, connection] of this.connections) {
      stats[name] = connection.getPoolStats();
    }

    return stats;
  }
}

// Query builder helpers
export class QueryBuilder {
  private schema: string;

  constructor(schema: string) {
    this.schema = schema;
  }

  /**
   * Build a SELECT query with schema prefix
   */
  select(table: string, columns: string[] = ['*'], where?: string, params?: any[]): { text: string; values?: any[] } {
    const columnList = columns.join(', ');
    let query = `SELECT ${columnList} FROM ${this.schema}.${table}`;

    if (where) {
      query += ` WHERE ${where}`;
    }

    const result: { text: string; values?: any[] } = { text: query };
    if (params) {
      result.values = params;
    }
    return result;
  }

  /**
   * Build an INSERT query with schema prefix
   */
  insert(table: string, data: Record<string, any>): { text: string; values: any[] } {
    const columns = Object.keys(data);
    const values = Object.values(data);
    const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');

    const query = `INSERT INTO ${this.schema}.${table} (${columns.join(', ')}) VALUES (${placeholders}) RETURNING *`;

    return { text: query, values };
  }

  /**
   * Build an UPDATE query with schema prefix
   */
  update(table: string, data: Record<string, any>, where: string, whereParams: any[]): { text: string; values: any[] } {
    const updates = Object.keys(data).map((key, index) => `${key} = $${index + 1}`).join(', ');
    const values = [...Object.values(data), ...whereParams];
    const whereClause = where.replace(/\$(\d+)/g, (_, num) => `$${parseInt(num) + Object.keys(data).length}`);

    const query = `UPDATE ${this.schema}.${table} SET ${updates} WHERE ${whereClause} RETURNING *`;

    return { text: query, values };
  }

  /**
   * Build a DELETE query with schema prefix
   */
  delete(table: string, where: string, params: any[]): { text: string; values: any[] } {
    const query = `DELETE FROM ${this.schema}.${table} WHERE ${where}`;

    return { text: query, values: params };
  }
}

// Audit logging helper
export class AuditLogger {
  private db: DatabaseConnection;

  constructor(db: DatabaseConnection) {
    this.db = db;
  }

  /**
   * Log an audit event
   */
  async log(event: {
    userId?: string;
    userType?: string;
    action: string;
    entityType: string;
    entityId?: string;
    oldValues?: any;
    newValues?: any;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    const query = `
      INSERT INTO shared_data.audit_logs
      (user_id, user_type, action, entity_type, entity_id, old_values, new_values, ip_address, user_agent)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `;

    const values = [
      event.userId || null,
      event.userType || null,
      event.action,
      event.entityType,
      event.entityId || null,
      event.oldValues ? JSON.stringify(event.oldValues) : null,
      event.newValues ? JSON.stringify(event.newValues) : null,
      event.ipAddress || null,
      event.userAgent || null,
    ];

    await this.db.query(query, values);
  }
}

export default DatabaseConnection;
