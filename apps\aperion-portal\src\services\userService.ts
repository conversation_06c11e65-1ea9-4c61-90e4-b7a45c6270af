import apiClient from './apiClient';
// API base URL - this will be proxied through the API Gateway to the Command Center service
// Note: apiClient already has baseURL '/api', so we only need the service path
const API_BASE_URL = '/command-center/admin/users/dev';

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: string;
  status: string;
  company?: string;
  subscription?: string;
  lastActive?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
  company?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  suspendedUsers: number;
  newUsersThisMonth: number;
  usersByRole: Record<string, number>;
  usersByStatus: Record<string, number>;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    timestamp: string;
  };
  meta?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface FilterApiResponse<T> extends ApiResponse<T> {
  filterInfo?: {
    appliedRole: string;
    mappedToUserType: string;
    availableRoles: string[];
    roleMapping: Record<string, string>;
  };
}

class UserService {

  async getUsersByRoleFilter(params: UserQueryParams): Promise<FilterApiResponse<User[]>> {
    try {
      console.log('Fetching users with role filter from:', `${API_BASE_URL}/filter`, 'with params:', params);
      const response = await apiClient.get(`${API_BASE_URL}/filter`, { params });
      console.log('Role filter API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching users with role filter:', error);

      // Return proper error response instead of fallback
      const errorMessage = error.response?.data?.error?.message ||
                          error.response?.statusText ||
                          error.message ||
                          'Failed to fetch filtered users';

      return {
        success: false,
        error: {
          code: error.response?.data?.error?.code || 'FILTER_ENDPOINT_ERROR',
          message: errorMessage,
          timestamp: new Date().toISOString(),
        }
      };
    }
  }

  async getUsers(params: UserQueryParams = {}): Promise<FilterApiResponse<User[]>> {
    // Clean up params - remove undefined values
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== undefined && value !== null && value !== '')
    );

    // If role is specified and not 'all', use the filter endpoint
    if (cleanParams.role && cleanParams.role !== 'all') {
      console.log('Using role filter endpoint for role:', cleanParams.role);
      return this.getUsersByRoleFilter(cleanParams);
    }

    // For "All Users" tab, remove role parameter entirely
    const { role, ...generalParams } = cleanParams;

    // General endpoint for all users or when no role filter is specified
    try {
      console.log('Fetching all users from:', API_BASE_URL, 'with params:', generalParams);
      const response = await apiClient.get(API_BASE_URL, { params: generalParams });
      console.log('Users API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching users:', error);

      // Return proper error response instead of mock data
      const errorMessage = error.response?.data?.error?.message ||
                          error.response?.statusText ||
                          error.message ||
                          'Failed to fetch users from database';

      return {
        success: false,
        error: {
          code: error.response?.data?.error?.code || 'DATABASE_CONNECTION_ERROR',
          message: errorMessage,
          timestamp: new Date().toISOString(),
        }
      };
    }
  }

  async getUserById(id: string): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  }

  async createUser(userData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      console.log('Creating user with Cognito integration:', userData);
      const response = await apiClient.post(API_BASE_URL, userData);
      console.log('Create user response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating user:', error);
      console.log('Create user error details:', error.response?.data || error.message);

      // Handle specific Cognito errors
      const errorResponse = error.response?.data;
      if (errorResponse && !errorResponse.success) {
        // Check for Cognito-specific errors
        if (errorResponse.error?.code === 'COGNITO_CREATION_ERROR') {
          console.error('Cognito user creation failed:', errorResponse.error.message);
          return {
            success: false,
            error: {
              code: 'COGNITO_ERROR',
              message: `AWS Cognito Error: ${errorResponse.error.message}`,
              timestamp: new Date().toISOString()
            }
          };
        } else if (errorResponse.error?.code === 'USER_ALREADY_EXISTS') {
          console.error('User already exists:', errorResponse.error.message);
          return {
            success: false,
            error: {
              code: 'USER_EXISTS',
              message: 'A user with this email already exists',
              timestamp: new Date().toISOString()
            }
          };
        } else if (errorResponse.error?.code === 'DATABASE_CREATION_ERROR') {
          console.error('Database creation failed after Cognito success:', errorResponse.error);
          return {
            success: false,
            error: {
              code: 'PARTIAL_CREATION',
              message: 'User created in Cognito but database save failed. Please contact support.',
              timestamp: new Date().toISOString()
            }
          };
        }

        // Return the original error for other cases
        return errorResponse;
      }

      // For network or other errors, throw to be handled by the calling component
      throw error;
    }
  }

  async updateUser(id: string, userData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.put(`${API_BASE_URL}/${id}`, userData);
      return response.data;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  async deleteUser(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    try {
      const response = await apiClient.delete(`${API_BASE_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  async suspendUser(id: string): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.patch(`${API_BASE_URL}/${id}/suspend`, {});
      return response.data;
    } catch (error) {
      console.error('Error suspending user:', error);
      throw error;
    }
  }

  async activateUser(id: string): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.patch(`${API_BASE_URL}/${id}/activate`, {});
      return response.data;
    } catch (error) {
      console.error('Error activating user:', error);
      throw error;
    }
  }

  async getUserStatistics(): Promise<ApiResponse<UserStatistics>> {
    try {
      console.log('Fetching user statistics from:', `${API_BASE_URL}/statistics`);
      const response = await apiClient.get(`${API_BASE_URL}/statistics`);
      console.log('Statistics API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching user statistics:', error);

      // Return proper error response instead of mock data
      const errorMessage = error.response?.data?.error?.message ||
                          error.response?.statusText ||
                          error.message ||
                          'Failed to fetch user statistics from database';

      return {
        success: false,
        error: {
          code: error.response?.data?.error?.code || 'STATISTICS_FETCH_ERROR',
          message: errorMessage,
          timestamp: new Date().toISOString(),
        }
      };
    }
  }

  async getCompanies(): Promise<ApiResponse<string[]>> {
    try {
      const endpoint = '/command-center/admin/users/dev/companies';
      console.log('🌐 Fetching companies from endpoint:', endpoint);

      const response = await apiClient.get(endpoint);

      console.log('✅ Companies API response status:', response.status);
      console.log('📊 Companies API response data:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching companies:', error);
      console.error('🔍 Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });

      // Return proper error response instead of mock data
      const errorMessage = error.response?.data?.error?.message ||
                          error.response?.statusText ||
                          error.message ||
                          'Failed to fetch companies from database';

      return {
        success: false,
        error: {
          code: error.response?.data?.error?.code || 'COMPANIES_FETCH_ERROR',
          message: errorMessage,
          timestamp: new Date().toISOString(),
        }
      };
    }
  }


}

export const userService = new UserService();
