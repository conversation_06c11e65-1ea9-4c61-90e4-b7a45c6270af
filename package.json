{"name": "aperion-health", "version": "1.0.0", "description": "Aperion Health Microservices Platform", "private": true, "workspaces": ["packages/*", "services/*", "apps/*"], "scripts": {"dev": "concurrently \"npm run dev:gateway\" \"npm run dev:services\" \"npm run dev:app\"", "dev:gateway": "npm run dev --workspace=packages/api-gateway", "dev:services": "concurrently \"npm run dev --workspace=services/member\" \"npm run dev --workspace=services/employer\" \"npm run dev --workspace=services/wellness-central\" \"npm run dev --workspace=services/zenx-lms\" \"npm run dev --workspace=services/command-center\"", "dev:app": "npm run dev --workspace=apps/aperion-portal", "build": "npm run build --workspaces", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "playwright test", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "db:setup": "cd database && npm install", "db:migrate": "cd database && npm run migrate", "db:test": "cd database && npm run test:connection", "db:backup": "cd database && npm run backup", "generate:service": "node scripts/generate-service.js", "start:all": "concurrently \"npm start --workspace=packages/api-gateway\" \"npm start --workspace=services/member\" \"npm start --workspace=services/employer\" \"npm start --workspace=services/wellness-central\" \"npm start --workspace=services/zenx-lms\" \"npm start --workspace=services/command-center\"", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "validate:env": "node scripts/validate-env.js"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/ejs": "^3.1.5", "@types/jest": "^29.5.0", "@types/multer": "^1.4.12", "@types/node": "^18.16.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.6.0", "nodemon": "^3.0.0", "prettier": "^3.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/aperion-health/platform.git"}, "keywords": ["healthcare", "microservices", "wellness", "lms", "nodejs", "typescript"], "author": "Aperion Health Team", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.705.0", "@aws-sdk/client-ses": "^3.826.0", "@aws-sdk/lib-storage": "^3.705.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "ejs": "^3.1.10", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "pg": "^8.16.0", "react-hook-form": "^7.57.0", "sharp": "^0.33.5", "wouter": "^3.7.1", "zod": "^3.25.51"}}