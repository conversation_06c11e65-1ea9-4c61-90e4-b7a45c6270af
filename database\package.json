{"name": "@aperion/database", "version": "1.0.0", "description": "Aperion Health Database Schema and Migrations", "main": "migrations/migrate.js", "scripts": {"migrate": "node migrations/migrate.js", "migrate:dev": "NODE_ENV=development node migrations/migrate.js", "migrate:prod": "NODE_ENV=production node migrations/migrate.js", "schema:dump": "pg_dump -h ************** -U postgres -d AperionHealth-Dev --schema-only > schema_dump.sql", "data:dump": "pg_dump -h ************** -U postgres -d AperionHealth-Dev --data-only > data_dump.sql", "backup": "pg_dump -h ************** -U postgres -d AperionHealth-Dev > backup_$(date +%Y%m%d_%H%M%S).sql", "restore": "psql -h ************** -U postgres -d AperionHealth-Dev", "test:connection": "node -e \"const {Client} = require('pg'); const client = new Client({host: '**************', user: 'postgres', password: 'Cloud@2025', database: 'AperionHealth-Dev'}); client.connect().then(() => {console.log('✅ Database connection successful'); client.end();}).catch(err => {console.error('❌ Database connection failed:', err.message); process.exit(1);});\""}, "dependencies": {"pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["aperion", "health", "database", "postgresql", "migrations", "schema"], "author": "Aperion Health Team", "license": "MIT"}