-- =====================================================
-- USER MANAGEMENT ENHANCEMENTS
-- Additional indexes and views for efficient user management
-- =====================================================

-- Additional indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_references_search ON shared_data.user_references 
USING gin(to_tsvector('english', first_name || ' ' || last_name || ' ' || email));

CREATE INDEX IF NOT EXISTS idx_user_references_created_month ON shared_data.user_references 
(DATE_TRUNC('month', created_at));

CREATE INDEX IF NOT EXISTS idx_user_references_last_login ON shared_data.user_references 
(updated_at DESC) WHERE status = 'active';

-- Create a view for user management with aggregated data
CREATE OR REPLACE VIEW command_center.user_management_view AS
SELECT 
  ur.id,
  ur.cognito_user_id,
  ur.first_name,
  ur.last_name,
  ur.email,
  ur.user_type,
  ur.service_user_id,
  ur.status,
  ur.created_at,
  ur.updated_at,
  -- Role mapping
  CASE 
    WHEN ur.user_type = 'member' THEN 'member'
    WHEN ur.user_type = 'employer' THEN 'employer'
    WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
    WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
    WHEN ur.user_type = 'system_admin' THEN 'system-admin'
    ELSE ur.user_type
  END as role,
  -- Mock company data (in real implementation, this would join with company tables)
  CASE 
    WHEN ur.user_type = 'member' THEN 'TechCorp Solutions'
    WHEN ur.user_type = 'employer' THEN 'Global Health Corp'
    WHEN ur.user_type = 'wellness_coach' THEN 'WellnessTech Inc'
    ELSE 'Platform Administration'
  END as company,
  -- Mock subscription data
  CASE 
    WHEN ur.user_type = 'member' THEN 'Premium Health Plan'
    WHEN ur.user_type = 'employer' THEN 'Enterprise'
    ELSE NULL
  END as subscription,
  -- Activity indicators
  CASE 
    WHEN ur.updated_at > NOW() - INTERVAL '1 hour' THEN '1 hour ago'
    WHEN ur.updated_at > NOW() - INTERVAL '3 hours' THEN '3 hours ago'
    WHEN ur.updated_at > NOW() - INTERVAL '1 day' THEN '1 day ago'
    ELSE 'More than 1 day ago'
  END as last_active
FROM shared_data.user_references ur
WHERE ur.status != 'deleted';

-- Create a function to get user statistics
CREATE OR REPLACE FUNCTION command_center.get_user_statistics()
RETURNS TABLE (
  total_users bigint,
  active_users bigint,
  suspended_users bigint,
  pending_users bigint,
  new_users_this_month bigint,
  member_count bigint,
  employer_count bigint,
  coach_count bigint,
  admin_count bigint,
  lms_creator_count bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN ur.status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN ur.status = 'suspended' THEN 1 END) as suspended_users,
    COUNT(CASE WHEN ur.status = 'pending' THEN 1 END) as pending_users,
    COUNT(CASE WHEN ur.created_at >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as new_users_this_month,
    COUNT(CASE WHEN ur.user_type = 'member' THEN 1 END) as member_count,
    COUNT(CASE WHEN ur.user_type = 'employer' THEN 1 END) as employer_count,
    COUNT(CASE WHEN ur.user_type = 'wellness_coach' THEN 1 END) as coach_count,
    COUNT(CASE WHEN ur.user_type = 'system_admin' THEN 1 END) as admin_count,
    COUNT(CASE WHEN ur.user_type = 'lms_creator' THEN 1 END) as lms_creator_count
  FROM shared_data.user_references ur
  WHERE ur.status != 'deleted';
END;
$$ LANGUAGE plpgsql;

-- Create function to get user company information
CREATE OR REPLACE FUNCTION command_center.get_user_company(user_id_param INTEGER)
RETURNS TEXT AS $$
DECLARE
  user_company TEXT;
  user_type_val TEXT;
BEGIN
  -- First try to get real company data from user_company_assignments
  SELECT c.name INTO user_company
  FROM command_center.user_company_assignments uca
  JOIN command_center.companies c ON uca.company_id = c.id
  WHERE uca.user_id = user_id_param AND uca.is_active = true
  LIMIT 1;

  -- If no real company assignment found, return mock data based on user type
  IF user_company IS NULL THEN
    SELECT ur.user_type INTO user_type_val
    FROM shared_data.user_references ur
    WHERE ur.id = user_id_param;

    user_company := CASE
      WHEN user_type_val = 'member' THEN 'TechCorp Solutions'
      WHEN user_type_val = 'employer' THEN 'Global Health Corp'
      WHEN user_type_val = 'wellness_coach' THEN 'WellnessTech Inc'
      WHEN user_type_val = 'lms_creator' THEN 'InnovateHealth Corp'
      ELSE 'Platform Administration'
    END;
  END IF;

  RETURN user_company;
END;
$$ LANGUAGE plpgsql;

-- Create function to get user subscription information
CREATE OR REPLACE FUNCTION command_center.get_user_subscription(user_id_param INTEGER)
RETURNS TEXT AS $$
DECLARE
  user_subscription TEXT;
  user_type_val TEXT;
BEGIN
  -- First try to get real subscription data from user_subscription_assignments
  SELECT sp.name INTO user_subscription
  FROM command_center.user_subscription_assignments usa
  JOIN command_center.subscription_plans sp ON usa.subscription_plan_id = sp.id
  WHERE usa.user_id = user_id_param AND usa.is_active = true
  LIMIT 1;

  -- If no real subscription assignment found, return mock data based on user type
  IF user_subscription IS NULL THEN
    SELECT ur.user_type INTO user_type_val
    FROM shared_data.user_references ur
    WHERE ur.id = user_id_param;

    user_subscription := CASE
      WHEN user_type_val = 'member' THEN 'Premium Health Plan'
      WHEN user_type_val = 'employer' THEN 'Enterprise'
      WHEN user_type_val = 'wellness_coach' THEN 'Professional'
      WHEN user_type_val = 'lms_creator' THEN 'Creator Pro'
      ELSE NULL
    END;
  END IF;

  RETURN user_subscription;
END;
$$ LANGUAGE plpgsql;

-- Create function to get user last activity information
CREATE OR REPLACE FUNCTION command_center.get_user_last_activity(user_id_param INTEGER)
RETURNS TEXT AS $$
DECLARE
  last_activity TEXT;
  last_session_activity TIMESTAMP;
  user_updated_at TIMESTAMP;
BEGIN
  -- First try to get real activity data from user_sessions
  SELECT MAX(us.last_activity_at) INTO last_session_activity
  FROM command_center.user_sessions us
  WHERE us.user_id = user_id_param;

  -- If we have session activity, use it; otherwise use user updated_at
  IF last_session_activity IS NOT NULL THEN
    last_activity := CASE
      WHEN last_session_activity > NOW() - INTERVAL '1 hour' THEN '1 hour ago'
      WHEN last_session_activity > NOW() - INTERVAL '3 hours' THEN '3 hours ago'
      WHEN last_session_activity > NOW() - INTERVAL '1 day' THEN '1 day ago'
      WHEN last_session_activity > NOW() - INTERVAL '1 week' THEN '1 week ago'
      ELSE 'More than 1 week ago'
    END;
  ELSE
    -- Fallback to user updated_at timestamp
    SELECT ur.updated_at INTO user_updated_at
    FROM shared_data.user_references ur
    WHERE ur.id = user_id_param;

    last_activity := CASE
      WHEN user_updated_at > NOW() - INTERVAL '1 hour' THEN '1 hour ago'
      WHEN user_updated_at > NOW() - INTERVAL '3 hours' THEN '3 hours ago'
      WHEN user_updated_at > NOW() - INTERVAL '1 day' THEN '1 day ago'
      WHEN user_updated_at > NOW() - INTERVAL '1 week' THEN '1 week ago'
      ELSE 'More than 1 week ago'
    END;
  END IF;

  RETURN last_activity;
END;
$$ LANGUAGE plpgsql;

-- Create a function to search users with full-text search
CREATE OR REPLACE FUNCTION command_center.search_users(
  search_term text DEFAULT NULL,
  user_role text DEFAULT NULL,
  user_status text DEFAULT NULL,
  page_size integer DEFAULT 20,
  page_offset integer DEFAULT 0
)
RETURNS TABLE (
  id uuid,
  cognito_user_id varchar,
  first_name varchar,
  last_name varchar,
  email varchar,
  user_type varchar,
  role text,
  status varchar,
  company text,
  subscription text,
  last_active text,
  created_at timestamp,
  updated_at timestamp,
  total_count bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    umv.id,
    umv.cognito_user_id,
    umv.first_name,
    umv.last_name,
    umv.email,
    umv.user_type,
    umv.role,
    umv.status,
    umv.company,
    umv.subscription,
    umv.last_active,
    umv.created_at,
    umv.updated_at,
    COUNT(*) OVER() as total_count
  FROM command_center.user_management_view umv
  WHERE 
    (search_term IS NULL OR 
     umv.first_name ILIKE '%' || search_term || '%' OR
     umv.last_name ILIKE '%' || search_term || '%' OR
     umv.email ILIKE '%' || search_term || '%' OR
     umv.company ILIKE '%' || search_term || '%')
    AND (user_role IS NULL OR umv.user_type = user_role)
    AND (user_status IS NULL OR umv.status = user_status)
  ORDER BY umv.created_at DESC
  LIMIT page_size OFFSET page_offset;
END;
$$ LANGUAGE plpgsql;

-- Create audit trigger for user changes
CREATE OR REPLACE FUNCTION shared_data.audit_user_changes()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'UPDATE' THEN
    INSERT INTO shared_data.audit_logs (
      user_id,
      user_type,
      action,
      entity_type,
      entity_id,
      old_values,
      new_values,
      created_at
    ) VALUES (
      NEW.id,
      'system_admin',
      'UPDATE_USER',
      'user_references',
      NEW.id,
      row_to_json(OLD),
      row_to_json(NEW),
      NOW()
    );
    RETURN NEW;
  ELSIF TG_OP = 'INSERT' THEN
    INSERT INTO shared_data.audit_logs (
      user_id,
      user_type,
      action,
      entity_type,
      entity_id,
      new_values,
      created_at
    ) VALUES (
      NEW.id,
      'system_admin',
      'CREATE_USER',
      'user_references',
      NEW.id,
      row_to_json(NEW),
      NOW()
    );
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO shared_data.audit_logs (
      user_id,
      user_type,
      action,
      entity_type,
      entity_id,
      old_values,
      created_at
    ) VALUES (
      OLD.id,
      'system_admin',
      'DELETE_USER',
      'user_references',
      OLD.id,
      row_to_json(OLD),
      NOW()
    );
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for user audit logging
DROP TRIGGER IF EXISTS user_audit_trigger ON shared_data.user_references;
CREATE TRIGGER user_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON shared_data.user_references
  FOR EACH ROW EXECUTE FUNCTION shared_data.audit_user_changes();

-- Grant necessary permissions to command center schema
GRANT USAGE ON SCHEMA command_center TO postgres;
GRANT SELECT ON command_center.user_management_view TO postgres;
GRANT EXECUTE ON FUNCTION command_center.get_user_statistics() TO postgres;
GRANT EXECUTE ON FUNCTION command_center.search_users(text, text, text, integer, integer) TO postgres;
GRANT EXECUTE ON FUNCTION command_center.get_user_company(integer) TO postgres;
GRANT EXECUTE ON FUNCTION command_center.get_user_subscription(integer) TO postgres;
GRANT EXECUTE ON FUNCTION command_center.get_user_last_activity(integer) TO postgres;

-- Create indexes on audit logs for better performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_management ON shared_data.audit_logs 
(entity_type, action, created_at) WHERE entity_type = 'user_references';

-- Add comments for documentation
COMMENT ON VIEW command_center.user_management_view IS 'Comprehensive view for user management with role mapping and activity indicators';
COMMENT ON FUNCTION command_center.get_user_statistics() IS 'Returns aggregated user statistics for dashboard display';
COMMENT ON FUNCTION command_center.search_users(text, text, text, integer, integer) IS 'Full-text search function for users with pagination';
COMMENT ON FUNCTION command_center.get_user_company(integer) IS 'Returns company name for a user, with fallback to mock data based on user type';
COMMENT ON FUNCTION command_center.get_user_subscription(integer) IS 'Returns subscription plan name for a user, with fallback to mock data based on user type';
COMMENT ON FUNCTION command_center.get_user_last_activity(integer) IS 'Returns formatted last activity string for a user based on session data or user updated timestamp';
COMMENT ON TRIGGER user_audit_trigger ON shared_data.user_references IS 'Audit trigger to log all user changes for compliance and tracking';
