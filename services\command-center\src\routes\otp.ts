import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import {
  CognitoIdentityProviderClient,
  InitiateAuthCommand,
  RespondToAuthChallengeCommand,
  ListUsersCommand,
  AdminGetUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { createHmac } from 'crypto';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';
import { asyncHandler } from '@aperion/shared';
import { UserRole, rolePermissions, roleServiceMapping } from '@aperion/shared';
import { config } from '../config';

const router = Router();

// Helper function to calculate SECRET_HASH for AWS Cognito
const calculateSecretHash = (username: string, clientId: string, clientSecret: string): string => {
  return createHmac('sha256', clientSecret)
    .update(username + clientId)
    .digest('base64');
};

// Role ID to role name mapping (reverse mapping from Cognito numeric roleId to role names)
const getRoleNameFromId = (roleId: string | number): string => {
  const numericRoleId = typeof roleId === 'string' ? parseInt(roleId, 10) : roleId;
  const roleIdToNameMapping: Record<number, string> = {
    1: 'system-admin',        // Admin = Command Center portal
    2: 'employer',            // Employer portal
    3: 'member',              // Member portal
    4: 'wellness-coach',      // Wellness Coach portal
  };

  return roleIdToNameMapping[numericRoleId] || 'member'; // Default to member if roleId not found
};

// Note: roleId is now extracted directly from Cognito's custom:roleId attribute
// No need for role name to roleId mapping since Cognito stores numeric values

// Initialize Cognito client
console.log('AWS Config:', {
  region: process.env.AWS_REGION || 'us-east-1',
  hasAccessKey: !!process.env.AWS_ACCESS_KEY_ID,
  hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY,
  clientId: process.env.CLIENT_ID,
});

const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// Validation middleware for send OTP
const sendOtpValidation = [
  body('userName').notEmpty().withMessage('Email or Phone number is required'),
];

// Validation middleware for verify OTP
const verifyOtpValidation = [
  body('userName').notEmpty().withMessage('Username is required'),
  body('answer').notEmpty().withMessage('OTP is required'),
  body('session').isString().notEmpty().withMessage('Session ID is required'),
];

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: Function): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      status: 2, // User error
      message: 'Validation failed',
      errors: errors.array(),
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    });
    return;
  }
  next();
};

/**
 * Send OTP endpoint
 * POST /api/command-center/auth/send-otp
 */
router.post('/send-otp', sendOtpValidation, handleValidationErrors, asyncHandler(async (req: Request, res: Response) => {
  const { userName } = req.body;

  logger.info('OTP send request', {
    requestId: req.requestId,
    userName,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  try {
    // Validate the input
    if (!userName) {
      res.status(400).json({
        status: 2,
        message: 'Email or Phone number is required',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      });
      return;
    }

    // Get user name - determine if it's email or phone
    const key = userName.includes('@') ? 'email' : 'phone_number';

    // Look up user by email/phone to get the actual username
    const userDetails = await cognitoClient.send(new ListUsersCommand({
      UserPoolId: process.env.USERPOOLID!,
      Filter: `${key} = "${userName}"`
    }));

    if (!userDetails.Users || userDetails.Users.length === 0) {
      res.status(400).json({
        status: 2,
        message: 'User does not exist. Please sign up first.',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      });
      return;
    }

    // Get the actual user details
    const userGetCommand = new AdminGetUserCommand({
      Username: userDetails.Users[0].Username!,
      UserPoolId: process.env.USERPOOLID!,
    });

    const user = await cognitoClient.send(userGetCommand);
    const secretHash = calculateSecretHash(userDetails.Users[0].Username!, process.env.CLIENT_ID!, process.env.CLIENT_SECRET!);

    // Initiate custom auth flow
    const params = {
      UserPoolId: process.env.USERPOOLID!,
      AuthFlow: process.env.AUTHFLOW! as any,
      ClientId: process.env.CLIENT_ID!,
      AuthParameters: {
        USERNAME: user.Username!,
        [`${key}`]: userName,
        SECRET_HASH: secretHash
      },
      ClientMetadata: {
        login_method: key
      }
    };

    const command = new InitiateAuthCommand(params);
    const response = await cognitoClient.send(command);

    logger.info('OTP sent successfully', {
      requestId: req.requestId,
      userName,
      challengeName: response.ChallengeName,
    });

    res.json({
      status: 1,
      message: "OTP sent successfully",
      session: response.Session,
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    });

  } catch (error) {
    console.error('AWS Cognito Error Details:', error);
    logger.error('Failed to send OTP', {
      requestId: req.requestId,
      userName,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    // Handle user not found error specifically
    if (error instanceof Error && error.name === 'UserNotFoundException') {
      res.status(400).json({
        status: 2,
        message: 'User does not exist. Please sign up first.',
        error: {
          name: error.name,
          message: error.message
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      });
      return;
    }

    // Handle other errors
    res.status(500).json({
      status: 0,
      message: error instanceof Error ? error.message : 'Something went wrong',
      error: {
        code: (error as any)?.$metadata?.httpStatusCode,
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    });
  }
}));

/**
 * Verify OTP endpoint
 * POST /api/command-center/auth/verify-otp
 */
router.post('/verify-otp', verifyOtpValidation, handleValidationErrors, asyncHandler(async (req: Request, res: Response) => {
  const { userName, answer, session } = req.body;

  logger.info('OTP verification request', {
    requestId: req.requestId,
    userName,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  try {
    if (!userName || !answer || !session) {
      res.status(400).json({
        status: 2,
        message: 'Username, OTP, and session are required',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      });
      return;
    }

    // Get user name - determine if it's email or phone
    const key = userName.includes('@') ? 'email' : 'phone_number';

    // Look up user by email/phone to get the actual username (same as send-otp)
    const userDetails = await cognitoClient.send(new ListUsersCommand({
      UserPoolId: process.env.USERPOOLID!,
      Filter: `${key} = "${userName}"`
    }));

    if (!userDetails.Users || userDetails.Users.length === 0) {
      res.status(400).json({
        status: 2,
        message: 'User does not exist. Please sign up first.',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      });
      return;
    }

    // Get the actual user details
    const userGetCommand = new AdminGetUserCommand({
      Username: userDetails.Users[0].Username!,
      UserPoolId: process.env.USERPOOLID!,
    });

    const user = await cognitoClient.send(userGetCommand);

    // Use the actual Cognito username for SECRET_HASH calculation (not the email)
    const actualUsername = user.Username!;
    const secretHash = calculateSecretHash(actualUsername, process.env.CLIENT_ID!, process.env.CLIENT_SECRET!);

    logger.info('OTP verification - user lookup successful', {
      requestId: req.requestId,
      inputUserName: userName,
      actualUsername: actualUsername,
      userExists: true,
    });

    const params = {
      ClientId: process.env.CLIENT_ID!,
      Session: session,
      ChallengeName: process.env.CHALLENGENAME! as any,
      ChallengeResponses: {
        USERNAME: actualUsername, // Use actual username, not email
        SECRET_HASH: secretHash,
        ANSWER: answer
      }
    };

    const command = new RespondToAuthChallengeCommand(params);
    const response = await cognitoClient.send(command);

    logger.info('OTP verification successful', {
      requestId: req.requestId,
      userName,
      actualUsername: actualUsername,
      hasAuthResult: !!response.AuthenticationResult,
    });

    // Extract user information from Cognito attributes
    const userAttributes = user.UserAttributes || [];
    const email = userAttributes.find(attr => attr.Name === 'email')?.Value || '';
    const phoneNumber = userAttributes.find(attr => attr.Name === 'phone_number')?.Value || '';
    const cognitoRoleId = userAttributes.find(attr => attr.Name === 'custom:roleId')?.Value || '3'; // Default to member roleId

    // Convert numeric roleId from Cognito to role name
    const cognitoRole = getRoleNameFromId(cognitoRoleId);
    const service = userAttributes.find(attr => attr.Name === 'custom:service')?.Value || roleServiceMapping[cognitoRole as UserRole];
    const permissions = userAttributes.find(attr => attr.Name === 'custom:permissions')?.Value;

    // Parse permissions or get default permissions for role
    let userPermissions: string[] = [];
    try {
      userPermissions = permissions ? JSON.parse(permissions) : rolePermissions[cognitoRole as UserRole] || [];
    } catch (error) {
      userPermissions = rolePermissions[cognitoRole as UserRole] || [];
    }

    // Generate roleId for frontend navigation (use the numeric value directly from Cognito)
    const roleId = typeof cognitoRoleId === 'string' ? parseInt(cognitoRoleId, 10) : cognitoRoleId;

    // Create JWT token payload
    // Using jwt.sign directly to avoid the audience conflict in TokenUtils.generateToken()
    const tokenPayload = {
      sub: actualUsername, // User ID
      email: email,
      'cognito:groups': [], // Can be populated if using Cognito groups
      'custom:role': cognitoRole as UserRole,
      'custom:service': service,
      'custom:permissions': userPermissions,
    };

    // Generate JWT token with 24-hour expiration using jwt.sign directly
    // This avoids the conflict where TokenUtils.generateToken() tries to set audience twice
    const jwtToken = jwt.sign(tokenPayload, config.jwtSecret, {
      expiresIn: '24h',
      issuer: 'aperion-command-center',
      audience: 'aperion-portal',
    });

    logger.info('JWT token generated for user', {
      requestId: req.requestId,
      userId: actualUsername,
      email: email,
      role: cognitoRole,
      roleId: roleId,
      service: service,
    });

    res.json({
      status: 1,
      message: "OTP verified successfully",
      data: {
        token: jwtToken,
        user: {
          id: phoneNumber,
          email: email,
          role: cognitoRole,
          service: service,
        },
        roleId: roleId,
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    });

  } catch (error) {
    console.error('AWS Cognito Verify OTP Error Details:', error);
    logger.error('Failed to verify OTP', {
      requestId: req.requestId,
      userName,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.name : 'Unknown',
    });

    // Handle specific AWS Cognito errors
    if (error instanceof Error) {
      // Handle user not found error specifically
      if (error.name === 'UserNotFoundException') {
        res.status(400).json({
          status: 2,
          message: 'User does not exist. Please sign up first.',
          error: {
            name: error.name,
            message: error.message
          },
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        });
        return;
      }

      // Handle invalid OTP error
      if (error.name === 'NotAuthorizedException' || error.message.includes('Incorrect username or password')) {
        res.status(400).json({
          status: 2,
          message: 'Invalid OTP code. Please try again.',
          error: {
            name: error.name,
            message: 'Invalid OTP'
          },
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        });
        return;
      }

      // Handle expired session error
      if (error.name === 'ExpiredCodeException' || error.message.includes('expired')) {
        res.status(400).json({
          status: 2,
          message: 'OTP has expired. Please request a new one.',
          error: {
            name: error.name,
            message: 'OTP expired'
          },
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        });
        return;
      }

      // Handle invalid session error
      if (error.name === 'InvalidParameterException' || error.message.includes('session')) {
        res.status(400).json({
          status: 2,
          message: 'Invalid session. Please start over.',
          error: {
            name: error.name,
            message: 'Invalid session'
          },
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        });
        return;
      }
    }

    // Handle other errors
    res.status(500).json({
      status: 0,
      message: 'Something went wrong',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    });
  }
}));

export { router as otpRouter };
