import { Request, Response } from 'express';
import { EmployeeService } from '../services/EmployeeService';
import { createEmployeeSchema, EmployeeQueryParams } from '../types/employee';
import { logger } from '../utils/logger';
import { Pool } from 'pg';
import { config } from '../config';

// Database connection pool
const pool = new Pool({
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  user: config.database.user,
  password: config.database.password,
  ssl: config.database.ssl,
  max: config.database.connectionLimit,
  idleTimeoutMillis: config.database.idleTimeout,
  connectionTimeoutMillis: config.database.connectionTimeout,
});

export class EmployeeController {
  private employeeService: EmployeeService;

  constructor() {
    this.employeeService = new EmployeeService();
  }

  /**
   * Helper method to get employer ID from user's Cognito ID
   */
  private async getEmployerIdFromUser(cognitoUserId: string): Promise<string | null> {
    const client = await pool.connect();

    try {
      const query = `
        SELECT id FROM ${config.database.schema}.employers
        WHERE cognito_user_id = $1
      `;

      const result = await client.query(query, [cognitoUserId]);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0].id;

    } finally {
      client.release();
    }
  }

  /**
   * Create a new employee
   * POST /api/employer/employees
   */
  createEmployee = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get employer ID from authenticated user's Cognito ID
      const cognitoUserId = req.user?.sub;
      if (!cognitoUserId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID not found in token',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Look up employer ID from database
      const employerId = await this.getEmployerIdFromUser(cognitoUserId);
      if (!employerId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'EMPLOYER_NOT_FOUND',
            message: 'Employer record not found for this user',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Validate request body
      const validationResult = createEmployeeSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid employee data',
            details: validationResult.error.errors,
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const employeeData = validationResult.data;

      logger.info('Creating employee via API:', {
        employerId,
        email: employeeData.email,
        requestId: req.headers['x-request-id']
      });

      // Create employee
      const result = await this.employeeService.createEmployee(employerId, employeeData);

      if (result.success) {
        logger.info('Employee created successfully via API:', {
          employerId,
          employeeId: result.data?.employee.id,
          email: employeeData.email
        });

        res.status(201).json(result);
      } else {
        logger.warn('Employee creation failed via API:', {
          employerId,
          email: employeeData.email,
          error: result.error
        });

        const statusCode = result.error?.code === 'EMPLOYEE_EXISTS' || result.error?.code === 'EMAIL_EXISTS' ? 409 : 400;
        res.status(statusCode).json(result);
      }

    } catch (error: any) {
      logger.error('Error in createEmployee controller:', {
        error: error.message,
        stack: error.stack,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred while creating employee',
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Create a new employee following Command Center pattern
   * POST /api/employer/employees/command-center
   */
  createEmployeeCommandCenter = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request body
      const validationResult = createEmployeeSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_REQUEST_DATA',
            message: 'Invalid employee data',
            details: validationResult.error.errors,
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'],
          }
        });
        return;
      }

      const result = await this.employeeService.createEmployeeCommandCenterPattern(validationResult.data);

      if (!result.success) {
        const statusCode = result.error?.code === 'EMPLOYEE_ALREADY_EXISTS'
          ? 409
          : 500;

        res.status(statusCode).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.headers['x-request-id'],
          }
        });
        return;
      }

      res.status(201).json(result);
    } catch (error) {
      logger.error('Error in EmployeeController.createEmployeeCommandCenter:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'],
        }
      });
    }
  };

  /**
   * Get employees for the authenticated employer
   * GET /api/employer/employees
   */
  getEmployees = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get employer ID from authenticated user's Cognito ID
      const cognitoUserId = req.user?.sub;
      if (!cognitoUserId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID not found in token',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Look up employer ID from database
      const employerId = await this.getEmployerIdFromUser(cognitoUserId);
      if (!employerId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'EMPLOYER_NOT_FOUND',
            message: 'Employer record not found for this user',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Parse query parameters
      const params: EmployeeQueryParams = {
        ...(req.query.page && { page: parseInt(req.query.page as string) }),
        ...(req.query.limit && { limit: parseInt(req.query.limit as string) }),
        ...(req.query.search && { search: req.query.search as string }),
        ...(req.query.department && { department: req.query.department as string }),
        ...(req.query.status && { status: req.query.status as string }),
        ...(req.query.sortBy && { sortBy: req.query.sortBy as string }),
        ...(req.query.sortOrder && { sortOrder: req.query.sortOrder as 'asc' | 'desc' })
      };

      logger.info('Fetching employees via API:', {
        employerId,
        params,
        requestId: req.headers['x-request-id']
      });

      const result = await this.employeeService.getEmployees(employerId, params);

      if (result.success) {
        logger.info('Employees fetched successfully via API:', {
          employerId,
          count: result.data?.length,
          total: result.meta?.total
        });
      }

      res.json(result);

    } catch (error: any) {
      logger.error('Error in getEmployees controller:', {
        error: error.message,
        stack: error.stack,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred while fetching employees',
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Get current (non-pending) employees for the authenticated employer
   * GET /api/employer/employees/current
   */
  getCurrentEmployees = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get employer ID from authenticated user's Cognito ID
      const cognitoUserId = req.user?.sub;
      if (!cognitoUserId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID not found in token',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Look up employer ID from database
      const employerId = await this.getEmployerIdFromUser(cognitoUserId);
      if (!employerId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'EMPLOYER_NOT_FOUND',
            message: 'Employer record not found for this user',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Parse query parameters
      const params: EmployeeQueryParams = {
        ...(req.query.page && { page: parseInt(req.query.page as string) }),
        ...(req.query.limit && { limit: parseInt(req.query.limit as string) }),
        ...(req.query.search && { search: req.query.search as string }),
        ...(req.query.department && { department: req.query.department as string }),
        ...(req.query.status && { status: req.query.status as string }),
        ...(req.query.sortBy && { sortBy: req.query.sortBy as string }),
        ...(req.query.sortOrder && { sortOrder: req.query.sortOrder as 'asc' | 'desc' })
      };

      logger.info('Fetching current employees via API:', {
        employerId,
        params,
        requestId: req.headers['x-request-id']
      });

      const result = await this.employeeService.getCurrentEmployees(employerId, params);

      if (result.success) {
        logger.info('Current employees fetched successfully via API:', {
          employerId,
          count: result.data?.length,
          total: result.meta?.total
        });
      }

      res.json(result);

    } catch (error: any) {
      logger.error('Error in getCurrentEmployees controller:', {
        error: error.message,
        stack: error.stack,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred while fetching current employees',
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Get pending employees for the authenticated employer
   * GET /api/employer/employees/pending
   */
  getPendingEmployees = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get employer ID from authenticated user's Cognito ID
      const cognitoUserId = req.user?.sub;
      if (!cognitoUserId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID not found in token',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Look up employer ID from database
      const employerId = await this.getEmployerIdFromUser(cognitoUserId);
      if (!employerId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'EMPLOYER_NOT_FOUND',
            message: 'Employer record not found for this user',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Parse query parameters
      const params: EmployeeQueryParams = {
        ...(req.query.page && { page: parseInt(req.query.page as string) }),
        ...(req.query.limit && { limit: parseInt(req.query.limit as string) }),
        ...(req.query.search && { search: req.query.search as string }),
        ...(req.query.department && { department: req.query.department as string }),
        ...(req.query.sortBy && { sortBy: req.query.sortBy as string }),
        ...(req.query.sortOrder && { sortOrder: req.query.sortOrder as 'asc' | 'desc' })
      };

      logger.info('Fetching pending employees via API:', {
        employerId,
        params,
        requestId: req.headers['x-request-id']
      });

      const result = await this.employeeService.getPendingEmployees(employerId, params);

      if (result.success) {
        logger.info('Pending employees fetched successfully via API:', {
          employerId,
          count: result.data?.length,
          total: result.meta?.total
        });
      }

      res.json(result);

    } catch (error: any) {
      logger.error('Error in getPendingEmployees controller:', {
        error: error.message,
        stack: error.stack,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred while fetching pending employees',
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Get departments for the authenticated employer
   * GET /api/employer/departments
   * TEMPORARILY DISABLED - Using hardcoded departments in frontend
   */
  /*
  getDepartments = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get employer ID from authenticated user
      const employerId = req.user?.['custom:employer_id'];
      if (!employerId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_EMPLOYER_ID',
            message: 'Employer ID not found in user context',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      logger.info('Fetching departments via API:', {
        employerId,
        requestId: req.headers['x-request-id']
      });

      const result = await this.employeeService.getDepartments(employerId);

      if (result.success) {
        logger.info('Departments fetched successfully via API:', {
          employerId,
          count: result.data?.length
        });
      }

      res.json(result);

    } catch (error: any) {
      logger.error('Error in getDepartments controller:', {
        error: error.message,
        stack: error.stack,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred while fetching departments',
          timestamp: new Date().toISOString()
        }
      });
    }
  };
  */

  /**
   * Delete pending employee invitation
   * DELETE /api/employer/employees/pending/:id
   */
  deleteInvitation = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_EMPLOYEE_ID',
            message: 'Employee ID is required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Get employer ID from authenticated user's Cognito ID
      const cognitoUserId = req.user?.sub;
      if (!cognitoUserId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID not found in token',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Look up employer ID from database
      const employerId = await this.getEmployerIdFromUser(cognitoUserId);
      if (!employerId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'EMPLOYER_NOT_FOUND',
            message: 'Employer record not found for this user',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      logger.info('Deleting employee invitation via API:', {
        employerId,
        employeeId: id,
        requestId: req.headers['x-request-id']
      });

      const result = await this.employeeService.deleteInvitation(employerId, id);

      if (result.success) {
        logger.info('Employee invitation deleted successfully via API:', {
          employerId,
          employeeId: id
        });
        res.json(result);
      } else {
        const statusCode = result.error?.code === 'EMPLOYEE_NOT_FOUND'
          ? 404
          : result.error?.code === 'INVALID_STATUS'
          ? 400
          : 500;

        res.status(statusCode).json(result);
      }

    } catch (error: any) {
      logger.error('Error in deleteInvitation controller:', {
        error: error.message,
        stack: error.stack,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred while deleting employee invitation',
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Get available health plans
   * GET /api/employer/health-plans
   */
  getHealthPlans = async (req: Request, res: Response): Promise<void> => {
    try {
      logger.info('Fetching health plans via API:', {
        requestId: req.headers['x-request-id']
      });

      const result = await this.employeeService.getHealthPlans();

      if (result.success) {
        logger.info('Health plans fetched successfully via API:', {
          count: result.data?.length
        });
      }

      res.json(result);

    } catch (error: any) {
      logger.error('Error in getHealthPlans controller:', {
        error: error.message,
        stack: error.stack,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred while fetching health plans',
          timestamp: new Date().toISOString()
        }
      });
    }
  };
}
