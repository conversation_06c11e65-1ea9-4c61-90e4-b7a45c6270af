{"name": "@aperion/database", "version": "1.0.0", "description": "Database package for Aperion Health platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist", "migrate": "node dist/migrate.js", "seed": "node dist/seed.js", "generate": "drizzle-kit generate:pg", "studio": "drizzle-kit studio"}, "dependencies": {"@aperion/shared": "^1.0.0", "drizzle-orm": "^0.28.6", "pg": "^8.11.3", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/pg": "^8.10.7", "@types/uuid": "^9.0.7", "drizzle-kit": "^0.19.13", "typescript": "^5.1.0", "rimraf": "^5.0.5"}}