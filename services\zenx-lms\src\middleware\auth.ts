import { Request, Response, NextFunction } from 'express';
import { TokenUtils } from '@aperion/shared/server';
import { JWTPayload, UserRole } from '@aperion/shared';
import { config } from '../config';
import { logger } from '../utils/logger';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
    }
  }
}

/**
 * Authentication middleware to verify JWT tokens
 */
export const authMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Authorization token is required',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify and decode token
    const decoded = TokenUtils.verifyToken(token, config.jwtSecret);
    req.user = decoded;

    // Log successful authentication
    logger.info('User authenticated', {
      requestId: req.requestId,
      userId: decoded.sub,
      role: decoded['custom:role'],
      service: decoded['custom:service'],
    });

    next();
  } catch (error) {
    logger.warn('Authentication failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      },
    });
  }
};

/**
 * Authorization middleware to check user permissions
 */
export const authorize = (requiredRoles: UserRole[] = []) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'User must be authenticated',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    const userRole = req.user['custom:role'] as UserRole;

    // Check if user has required role
    if (requiredRoles.length > 0 && !requiredRoles.includes(userRole)) {
      logger.warn('Authorization failed - insufficient privileges', {
        requestId: req.requestId,
        userId: req.user.sub,
        userRole,
        requiredRoles,
      });

      res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PRIVILEGES',
          message: 'Insufficient privileges to access this resource',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    next();
  };
};

/**
 * Service-to-service authentication middleware
 */
export const serviceAuthMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Check for service token in headers
    const serviceToken = req.headers['x-service-token'] as string;
    if (!serviceToken) {
      res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_SERVICE_TOKEN',
          message: 'Service token is required for internal API access',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    // Verify service token
    const decoded = TokenUtils.verifyServiceToken(serviceToken, config.serviceJwtSecret);

    // Add service info to request
    req.headers['x-source-service'] = decoded.sub;
    req.headers['x-target-service'] = decoded.aud;

    logger.info('Service authenticated', {
      requestId: req.requestId,
      sourceService: decoded.sub,
      targetService: decoded.aud,
    });

    next();
  } catch (error) {
    logger.warn('Service authentication failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    res.status(403).json({
      success: false,
      error: {
        code: 'INVALID_SERVICE_TOKEN',
        message: 'Invalid service token',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      },
    });
  }
};
