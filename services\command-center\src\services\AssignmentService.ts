import { UserAssignmentModel } from '../models/UserAssignment';
import { logger } from '../../../utils/logger';
import {
  UserCompanyAssignment,
  CreateUserCompanyAssignmentRequest,
  UpdateUserCompanyAssignmentRequest,
  UserCompanyAssignmentQueryParams,
  AssignmentStatistics
} from '../types/assignment';
import {
  UserSubscriptionAssignment,
  CreateUserSubscriptionAssignmentRequest,
  UpdateUserSubscriptionAssignmentRequest
} from '../types/subscription';
import { ApiResponse, ApiMeta } from '@aperion/shared';
import { createSuccessResponse, createErrorResponse } from '../utils/apiResponse';

export class AssignmentService {
  private userAssignmentModel: UserAssignmentModel;

  constructor() {
    this.userAssignmentModel = new UserAssignmentModel();
  }

  // =====================================================
  // COMPANY ASSIGNMENTS
  // =====================================================

  /**
   * Get all user company assignments with filtering and pagination
   */
  async getUserCompanyAssignments(params: UserCompanyAssignmentQueryParams): Promise<ApiResponse<{ assignments: UserCompanyAssignment[]; meta: ApiMeta }>> {
    try {
      logger.info('Getting user company assignments with params:', params);
      
      const result = await this.userAssignmentModel.getUserCompanyAssignments(params);
      
      logger.info('Successfully retrieved user company assignments', {
        count: result.assignments.length,
        total: result.meta.total
      });

      return createSuccessResponse(result);
    } catch (error) {
      logger.error('Error getting user company assignments:', error);
      return createErrorResponse(
        'USER_COMPANY_ASSIGNMENTS_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get user company assignments'
      );
    }
  }

  /**
   * Get user company assignment by ID
   */
  async getUserCompanyAssignmentById(id: string): Promise<ApiResponse<UserCompanyAssignment>> {
    try {
      logger.info('Getting user company assignment by ID:', { id });

      const assignment = await this.userAssignmentModel.getUserCompanyAssignmentById(id);

      if (!assignment) {
        return createErrorResponse('USER_COMPANY_ASSIGNMENT_NOT_FOUND', 'User company assignment not found');
      }

      logger.info('Successfully retrieved user company assignment:', { id, userId: assignment.userId });

      return createSuccessResponse(assignment);
    } catch (error) {
      logger.error('Error getting user company assignment by ID:', error);
      return createErrorResponse(
        'USER_COMPANY_ASSIGNMENT_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get user company assignment'
      );
    }
  }

  /**
   * Create a new user company assignment
   */
  async createUserCompanyAssignment(assignmentData: CreateUserCompanyAssignmentRequest): Promise<ApiResponse<UserCompanyAssignment>> {
    try {
      logger.info('Creating new user company assignment:', {
        userId: assignmentData.userId,
        companyId: assignmentData.companyId
      });

      const assignment = await this.userAssignmentModel.createUserCompanyAssignment(assignmentData);

      logger.info('Successfully created user company assignment:', {
        id: assignment.id,
        userId: assignment.userId,
        companyId: assignment.companyId
      });

      return createSuccessResponse(assignment);
    } catch (error) {
      logger.error('Error creating user company assignment:', error);
      return createErrorResponse(
        'USER_COMPANY_ASSIGNMENT_CREATE_ERROR',
        error instanceof Error ? error.message : 'Failed to create user company assignment'
      );
    }
  }

  /**
   * Update a user company assignment
   */
  async updateUserCompanyAssignment(id: string, assignmentData: UpdateUserCompanyAssignmentRequest): Promise<ApiResponse<UserCompanyAssignment>> {
    try {
      logger.info('Updating user company assignment:', { id, data: assignmentData });

      const assignment = await this.userAssignmentModel.updateUserCompanyAssignment(id, assignmentData);

      if (!assignment) {
        return createErrorResponse('USER_COMPANY_ASSIGNMENT_NOT_FOUND', 'User company assignment not found');
      }

      logger.info('Successfully updated user company assignment:', {
        id,
        userId: assignment.userId,
        companyId: assignment.companyId
      });

      return createSuccessResponse(assignment);
    } catch (error) {
      logger.error('Error updating user company assignment:', error);
      return createErrorResponse(
        'USER_COMPANY_ASSIGNMENT_UPDATE_ERROR',
        error instanceof Error ? error.message : 'Failed to update user company assignment'
      );
    }
  }

  /**
   * Delete a user company assignment
   */
  async deleteUserCompanyAssignment(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    try {
      logger.info('Deleting user company assignment:', { id });

      const deleted = await this.userAssignmentModel.deleteUserCompanyAssignment(id);

      if (!deleted) {
        return createErrorResponse('USER_COMPANY_ASSIGNMENT_NOT_FOUND', 'User company assignment not found');
      }

      logger.info('Successfully deleted user company assignment:', { id });

      return createSuccessResponse({ deleted: true });
    } catch (error) {
      logger.error('Error deleting user company assignment:', error);
      return createErrorResponse(
        'USER_COMPANY_ASSIGNMENT_DELETE_ERROR',
        error instanceof Error ? error.message : 'Failed to delete user company assignment'
      );
    }
  }

  // =====================================================
  // SUBSCRIPTION ASSIGNMENTS
  // =====================================================

  /**
   * Get all user subscription assignments
   */
  async getUserSubscriptionAssignments(userId?: number): Promise<ApiResponse<UserSubscriptionAssignment[]>> {
    try {
      logger.info('Getting user subscription assignments:', { userId });
      
      const assignments = await this.userAssignmentModel.getUserSubscriptionAssignments(userId);
      
      logger.info('Successfully retrieved user subscription assignments', {
        count: assignments.length,
        userId
      });

      return createSuccessResponse(assignments);
    } catch (error) {
      logger.error('Error getting user subscription assignments:', error);
      return createErrorResponse(
        'USER_SUBSCRIPTION_ASSIGNMENTS_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get user subscription assignments'
      );
    }
  }

  /**
   * Create a new user subscription assignment
   */
  async createUserSubscriptionAssignment(assignmentData: CreateUserSubscriptionAssignmentRequest): Promise<ApiResponse<UserSubscriptionAssignment>> {
    try {
      logger.info('Creating new user subscription assignment:', {
        userId: assignmentData.userId,
        subscriptionPlanId: assignmentData.subscriptionPlanId
      });

      const assignment = await this.userAssignmentModel.createUserSubscriptionAssignment(assignmentData);

      logger.info('Successfully created user subscription assignment:', {
        id: assignment.id,
        userId: assignment.userId,
        subscriptionPlanId: assignment.subscriptionPlanId
      });

      return createSuccessResponse(assignment);
    } catch (error) {
      logger.error('Error creating user subscription assignment:', error);
      return createErrorResponse(
        'USER_SUBSCRIPTION_ASSIGNMENT_CREATE_ERROR',
        error instanceof Error ? error.message : 'Failed to create user subscription assignment'
      );
    }
  }

  /**
   * Update a user subscription assignment
   */
  async updateUserSubscriptionAssignment(id: string, assignmentData: UpdateUserSubscriptionAssignmentRequest): Promise<ApiResponse<UserSubscriptionAssignment>> {
    try {
      logger.info('Updating user subscription assignment:', { id, data: assignmentData });

      const assignment = await this.userAssignmentModel.updateUserSubscriptionAssignment(id, assignmentData);

      if (!assignment) {
        return createErrorResponse('USER_SUBSCRIPTION_ASSIGNMENT_NOT_FOUND', 'User subscription assignment not found');
      }

      logger.info('Successfully updated user subscription assignment:', {
        id,
        userId: assignment.userId,
        subscriptionPlanId: assignment.subscriptionPlanId
      });

      return createSuccessResponse(assignment);
    } catch (error) {
      logger.error('Error updating user subscription assignment:', error);
      return createErrorResponse(
        'USER_SUBSCRIPTION_ASSIGNMENT_UPDATE_ERROR',
        error instanceof Error ? error.message : 'Failed to update user subscription assignment'
      );
    }
  }

  /**
   * Delete a user subscription assignment
   */
  async deleteUserSubscriptionAssignment(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    try {
      logger.info('Deleting user subscription assignment:', { id });

      const deleted = await this.userAssignmentModel.deleteUserSubscriptionAssignment(id);

      if (!deleted) {
        return createErrorResponse('USER_SUBSCRIPTION_ASSIGNMENT_NOT_FOUND', 'User subscription assignment not found');
      }

      logger.info('Successfully deleted user subscription assignment:', { id });

      return createSuccessResponse({ deleted: true });
    } catch (error) {
      logger.error('Error deleting user subscription assignment:', error);
      return createErrorResponse(
        'USER_SUBSCRIPTION_ASSIGNMENT_DELETE_ERROR',
        error instanceof Error ? error.message : 'Failed to delete user subscription assignment'
      );
    }
  }

  // =====================================================
  // STATISTICS
  // =====================================================

  /**
   * Get assignment statistics
   */
  async getAssignmentStatistics(): Promise<ApiResponse<AssignmentStatistics>> {
    try {
      logger.info('Getting assignment statistics');

      const statistics = await this.userAssignmentModel.getAssignmentStatistics();

      logger.info('Successfully retrieved assignment statistics:', {
        totalAssignments: statistics.totalAssignments,
        activeAssignments: statistics.activeAssignments
      });

      return createSuccessResponse(statistics);
    } catch (error) {
      logger.error('Error getting assignment statistics:', error);
      return createErrorResponse(
        'ASSIGNMENT_STATISTICS_ERROR',
        error instanceof Error ? error.message : 'Failed to get assignment statistics'
      );
    }
  }
}
