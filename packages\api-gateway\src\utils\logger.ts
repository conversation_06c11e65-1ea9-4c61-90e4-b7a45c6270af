import { LoggerFactory } from '@aperion/shared/server';

// Logger configuration for api-gateway service
const loggerConfig = {
  serviceName: 'api-gateway' as const,
  logLevel: (process.env.LOG_LEVEL as any) || 'info',
  enableConsole: true,
  enableFileLogging: true,
  enablePrettyPrint: process.env.NODE_ENV !== 'production',
  enableLogRotation: true,
  maxFileSize: '10M',
  maxFiles: 7,
  enableDebugFile: process.env.NODE_ENV !== 'production',
};

// Create application logger (for non-HTTP application events)
export const logger = LoggerFactory.createLogger(loggerConfig);

// Create HTTP logger middleware (uses dedicated access logger)
export const httpLogger = LoggerFactory.createHttpLogger(logger, loggerConfig);

// Utility functions for request/correlation IDs
export const generateRequestId = LoggerFactory.generateRequestId;
export const generateCorrelationId = LoggerFactory.generateCorrelationId;

// Legacy compatibility - create a stream object for any existing Morgan usage
export const loggerStream = {
  write: (message: string) => {
    logger.info(message.trim());
  },
};
