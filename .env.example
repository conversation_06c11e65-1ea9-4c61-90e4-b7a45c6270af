# Single Database Configuration - Aperion Health
DB_HOST=your-database-host
DB_PORT=5432
DB_NAME=AperionHealth-Dev
DB_USER=postgres
DB_PASSWORD=your-database-password
DB_SSL=false

# Database Connection Pool Settings
DB_CONNECTION_LIMIT=10
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=2000

# Database Schema Configuration (Single DB with Multiple Schemas)
SHARED_SCHEMA=shared_data
MEMBER_SCHEMA=member_service
EMPLOYER_SCHEMA=employer_service
WELLNESS_SCHEMA=wellness_service
LMS_SCHEMA=lms_service
COMMAND_CENTER_SCHEMA=command_center

# AWS Cognito Configuration
AWS_REGION=us-east-1
COGNITO_USER_POOL_ID=us-east-1_XXXXXXXXX
COGNITO_CLIENT_ID=your-client-id
COGNITO_CLIENT_SECRET=your-client-secret

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
SERVICE_JWT_SECRET=your-service-to-service-jwt-secret

# API Gateway Configuration
API_GATEWAY_PORT=3000
CORS_ORIGINS=http://localhost:4000,http://localhost:3000

# Service URLs
MEMBER_SERVICE_URL=http://localhost:3001
EMPLOYER_SERVICE_URL=http://localhost:3002
WELLNESS_SERVICE_URL=http://localhost:3003
LMS_SERVICE_URL=http://localhost:3004
COMMAND_CENTER_SERVICE_URL=http://localhost:3005

# Environment
NODE_ENV=development

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your-session-secret
SESSION_TIMEOUT=3600000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
