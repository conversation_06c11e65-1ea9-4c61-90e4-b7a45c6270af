// User types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  profilePictureUrl?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: Address;
  emergencyContact?: EmergencyContact;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phoneNumber: string;
  email?: string;
}

// Health Plan types
export interface HealthPlan {
  id: string;
  planName: string;
  planType: string;
  memberId: string;
  groupNumber?: string;
  coveragePeriodStart: string;
  coveragePeriodEnd: string;
  primaryCareVisitCopay?: number;
  specialistVisitCopay?: number;
  emergencyRoomCopay?: number;
  individualDeductible?: number;
  familyDeductible?: number;
  individualOutOfPocketMax?: number;
  familyOutOfPocketMax?: number;
  createdAt: string;
  updatedAt: string;
}

// Wellness Score types
export interface WellnessScore {
  id: string;
  userId: string;
  score: number;
  category: string;
  feedback: string;
  breakdown?: {
    physical: number;
    mental: number;
    nutrition: number;
    sleep: number;
  };
  lastUpdated: string;
  createdAt: string;
  updatedAt: string;
}

// Rewards types
export interface Reward {
  id: string;
  userId: string;
  currentPoints: number;
  totalEarnedPoints: number;
  nextRewardThreshold: number;
  availableRewards: RewardItem[];
  recentActivity: RewardActivity[];
  createdAt: string;
  updatedAt: string;
}

export interface RewardItem {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  category: string;
  imageUrl?: string;
  isAvailable: boolean;
}

export interface RewardActivity {
  id: string;
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  date: string;
}

// Wellness Goals types
export interface WellnessGoal {
  id: string;
  userId: string;
  title: string;
  description: string;
  category: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  icon?: string;
  isCompleted: boolean;
  dueDate?: string;
  lastUpdated: string;
  createdAt: string;
  updatedAt: string;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  activationCode: string;
}

// Notification types
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  createdAt: string;
}

// Dashboard types
export interface DashboardData {
  user: User;
  healthPlan: HealthPlan;
  wellnessScore: WellnessScore;
  rewards: Reward;
  goals: WellnessGoal[];
  notifications: Notification[];
}
