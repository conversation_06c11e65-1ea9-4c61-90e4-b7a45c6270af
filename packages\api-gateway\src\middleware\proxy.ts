import { createProxyMiddleware, Options } from 'http-proxy-middleware';
import { Request, Response, NextFunction } from 'express';
import { config } from '../config';
import { logger } from '../utils/logger';
import { TokenUtils } from '@aperion/shared/server';

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
    }
  }
}

// Circuit breaker state
interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
}

const circuitBreakers = new Map<string, CircuitBreakerState>();

// Map service names to their route prefixes
const getRoutePrefix = (serviceName: string): string => {
  const routeMap: Record<string, string> = {
    'member-service': 'member',
    'employer-service': 'employer',
    'wellness-central': 'wellness',
    'zenx-lms': 'lms',
    'command-center': 'command-center',
  };
  return routeMap[serviceName] || serviceName;
};

// Initialize circuit breaker for a service
const initCircuitBreaker = (_serviceName: string): CircuitBreakerState => {
  return {
    failures: 0,
    lastFailureTime: 0,
    state: 'CLOSED',
  };
};

// Check circuit breaker state
const checkCircuitBreaker = (serviceName: string): boolean => {
  let breaker = circuitBreakers.get(serviceName);
  if (!breaker) {
    breaker = initCircuitBreaker(serviceName);
    circuitBreakers.set(serviceName, breaker);
  }

  const now = Date.now();
  const { resetTimeout } = config.circuitBreaker;

  switch (breaker.state) {
    case 'OPEN':
      if (now - breaker.lastFailureTime > resetTimeout) {
        breaker.state = 'HALF_OPEN';
        logger.info(`Circuit breaker for ${serviceName} moved to HALF_OPEN`);
      }
      return breaker.state !== 'OPEN';

    case 'HALF_OPEN':
      return true;

    case 'CLOSED':
    default:
      return true;
  }
};

// Record circuit breaker success
const recordSuccess = (serviceName: string): void => {
  const breaker = circuitBreakers.get(serviceName);
  if (breaker) {
    breaker.failures = 0;
    breaker.state = 'CLOSED';
  }
};

// Record circuit breaker failure
const recordFailure = (serviceName: string): void => {
  let breaker = circuitBreakers.get(serviceName);
  if (!breaker) {
    breaker = initCircuitBreaker(serviceName);
    circuitBreakers.set(serviceName, breaker);
  }

  breaker.failures++;
  breaker.lastFailureTime = Date.now();

  if (breaker.failures >= config.circuitBreaker.failureThreshold) {
    breaker.state = 'OPEN';
    logger.warn(`Circuit breaker for ${serviceName} opened due to failures`, {
      failures: breaker.failures,
      threshold: config.circuitBreaker.failureThreshold,
    });
  }
};

// Create proxy middleware for a specific service
export const proxyMiddleware = (serviceName: string, customPathRewrite?: Record<string, string>) => {
  const serviceConfig = config.services[serviceName as keyof typeof config.services];

  if (!serviceConfig) {
    throw new Error(`Service configuration not found for: ${serviceName}`);
  }

  const proxyOptions: Options = {
    target: serviceConfig.url,
    changeOrigin: true,
    timeout: serviceConfig.timeout,
    proxyTimeout: serviceConfig.timeout,
    secure: false, // Allow self-signed certificates in development
    followRedirects: false,
    pathRewrite: customPathRewrite || {
      [`^/api/${getRoutePrefix(serviceName)}`]: '', // Remove service prefix
    },

    // Add headers to forwarded requests
    onProxyReq: (proxyReq, req: Request) => {
      // Add correlation ID
      if (req.correlationId) {
        proxyReq.setHeader('X-Correlation-ID', req.correlationId);
      }

      // Add request ID
      if (req.requestId) {
        proxyReq.setHeader('X-Request-ID', req.requestId);
      }

      // Generate service-to-service token
      if (req.user) {
        const serviceToken = TokenUtils.generateServiceToken(
          'api-gateway',
          serviceName,
          config.serviceJwtSecret
        );
        proxyReq.setHeader('X-Service-Token', serviceToken);
      }

      // Forward user information
      if (req.user) {
        proxyReq.setHeader('X-User-ID', req.user.sub);
        proxyReq.setHeader('X-User-Role', req.user['custom:role']);
        proxyReq.setHeader('X-User-Service', req.user['custom:service']);
      }

      // Ensure Content-Type is preserved for requests with body
      if (req.headers['content-type']) {
        proxyReq.setHeader('Content-Type', req.headers['content-type']);
      }

      // Ensure Content-Length is preserved for requests with body
      if (req.headers['content-length']) {
        proxyReq.setHeader('Content-Length', req.headers['content-length']);
      }

      // Log proxy request
      const hasBody = req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH';
      const contentLength = req.headers['content-length'] || '0';

      logger.info('Proxying request', {
        requestId: req.requestId,
        serviceName,
        method: req.method,
        path: req.path,
        target: serviceConfig.url,
        hasBody,
        bodySize: parseInt(contentLength, 10),
        contentType: req.headers['content-type'],
      });
    },



    // Handle proxy response
    onProxyRes: (proxyRes, req: Request, _res: Response) => {
      // Record success for circuit breaker
      recordSuccess(serviceName);

      // Log proxy response
      logger.info('Proxy response received', {
        requestId: req.requestId,
        serviceName,
        statusCode: proxyRes.statusCode,
        responseTime: Date.now() - (req.startTime || Date.now()),
      });
    },

    // Handle proxy errors
    onError: (err: any, req: Request, res: Response) => {
      // Record failure for circuit breaker
      recordFailure(serviceName);

      // Determine error type and status code
      let statusCode = 503;
      let errorCode = 'SERVICE_UNAVAILABLE';
      let message = `Service ${serviceName} is temporarily unavailable`;

      const errCode = err.code || '';
      if (errCode === 'ECONNREFUSED') {
        statusCode = 503;
        errorCode = 'SERVICE_UNAVAILABLE';
        message = `Service ${serviceName} is not responding`;
      } else if (errCode === 'ECONNRESET' || errCode === 'EPIPE') {
        statusCode = 502;
        errorCode = 'BAD_GATEWAY';
        message = `Connection to ${serviceName} was reset`;
      } else if (errCode === 'ETIMEDOUT' || err.message?.includes('timeout')) {
        statusCode = 408;
        errorCode = 'REQUEST_TIMEOUT';
        message = `Request to ${serviceName} timed out`;
      }

      logger.error('Proxy error', {
        requestId: req.requestId,
        serviceName,
        error: err.message,
        errorCode: errCode,
        target: serviceConfig.url,
        method: req.method,
        path: req.path,
      });

      // Only send response if not already sent
      if (!res.headersSent) {
        res.status(statusCode).json({
          success: false,
          error: {
            code: errorCode,
            message,
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          },
        });
      }
    },
  };

  // Return middleware that checks circuit breaker before proxying
  return (req: Request, res: Response, next: NextFunction): void => {
    // Check circuit breaker
    if (!checkCircuitBreaker(serviceName)) {
      logger.warn('Circuit breaker open, rejecting request', {
        requestId: req.requestId,
        serviceName,
      });

      res.status(503).json({
        success: false,
        error: {
          code: 'SERVICE_UNAVAILABLE',
          message: `Service ${serviceName} is temporarily unavailable (circuit breaker open)`,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    // Create and apply proxy middleware
    const proxy = createProxyMiddleware(proxyOptions);
    proxy(req, res, next);
  };
};

// Health check for all services
export const checkServiceHealth = async (serviceName: string): Promise<{
  name: string;
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  error?: string;
}> => {
  const serviceConfig = config.services[serviceName as keyof typeof config.services];

  if (!serviceConfig) {
    return {
      name: serviceName,
      status: 'unhealthy',
      error: 'Service configuration not found',
    };
  }

  const startTime = Date.now();

  try {
    const response = await fetch(`${serviceConfig.url}${serviceConfig.healthPath}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(config.healthCheck.timeout),
    });

    const responseTime = Date.now() - startTime;

    if (response.ok) {
      recordSuccess(serviceName);
      return {
        name: serviceName,
        status: 'healthy',
        responseTime,
      };
    } else {
      recordFailure(serviceName);
      return {
        name: serviceName,
        status: 'unhealthy',
        responseTime,
        error: `HTTP ${response.status}`,
      };
    }
  } catch (error) {
    recordFailure(serviceName);
    const responseTime = Date.now() - startTime;

    return {
      name: serviceName,
      status: 'unhealthy',
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Check all services health
export const checkAllServicesHealth = async () => {
  const serviceNames = Object.keys(config.services);
  const healthChecks = await Promise.all(
    serviceNames.map(serviceName => checkServiceHealth(serviceName))
  );

  return healthChecks;
};
