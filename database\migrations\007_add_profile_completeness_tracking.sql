-- Migration: Add profile completeness tracking
-- Description: Add field to track when profile completeness reaches 100% for the first time
-- Date: 2025-01-09

-- Add profile completeness achievement tracking to members table
ALTER TABLE member_service.members 
ADD COLUMN profile_completeness_achieved_at TIMESTAMP NULL;

-- Add comment for documentation
COMMENT ON COLUMN member_service.members.profile_completeness_achieved_at IS 'Timestamp when profile completeness first reached 100%. Used to permanently hide progress bar.';

-- Create index for efficient querying
CREATE INDEX idx_members_completeness_achieved ON member_service.members(profile_completeness_achieved_at);

-- Update existing members who might already have complete profiles
-- Only includes fields that are shown in the frontend profile form
-- This is a one-time update for existing data
UPDATE member_service.members
SET profile_completeness_achieved_at = updated_at
WHERE
  first_name IS NOT NULL AND first_name != '' AND
  last_name IS NOT NULL AND last_name != '' AND
  email IS NOT NULL AND email != '' AND
  phone IS NOT NULL AND phone != '' AND
  date_of_birth IS NOT NULL AND
  gender IS NOT NULL AND gender != '' AND
  address IS NOT NULL AND
  (address->>'street') IS NOT NULL AND (address->>'street') != '' AND
  (address->>'city') IS NOT NULL AND (address->>'city') != '' AND
  (address->>'state') IS NOT NULL AND (address->>'state') != '' AND
  (address->>'zipCode') IS NOT NULL AND (address->>'zipCode') != '' AND
  (address->>'country') IS NOT NULL AND (address->>'country') != '' AND
  profile_picture_url IS NOT NULL AND profile_picture_url != '' AND
  profile_completeness_achieved_at IS NULL;
