{"name": "@aperion/zenx-lms", "version": "1.0.0", "description": "Zenx-lms service for Aperion Health", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@aperion/shared": "^1.0.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "pg": "^8.11.3", "drizzle-orm": "^0.28.6", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1", "zod": "^3.22.4", "winston": "^3.11.0", "dotenv": "^16.3.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/pg": "^8.10.7", "@types/jsonwebtoken": "^9.0.5", "@types/bcrypt": "^5.0.2", "@types/uuid": "^9.0.7", "typescript": "^5.1.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "drizzle-kit": "^0.19.13", "rimraf": "^5.0.5"}}