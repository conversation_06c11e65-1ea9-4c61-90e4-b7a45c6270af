/**
 * Enhanced error handling for AWS Cognito operations
 * 
 * This module provides better error messages and troubleshooting guidance
 * for common Cognito errors, especially those related to custom attributes.
 */

import { logger } from './logger';

export interface CognitoErrorDetails {
  originalError: any;
  email?: string;
  operation: string;
}

/**
 * Enhanced error handler for Cognito operations
 * Provides specific guidance for custom attribute schema issues
 */
export function handleCognitoError(details: CognitoErrorDetails): Error {
  const { originalError, email, operation } = details;
  
  logger.error(`Error in Cognito ${operation}:`, { 
    email, 
    error: originalError.message,
    code: originalError.name 
  });
  
  // Handle specific Cognito errors with enhanced error messages
  if (originalError.name === 'UsernameExistsException') {
    return new Error(`User with email ${email} already exists in Cognito`);
    
  } else if (originalError.name === 'InvalidPasswordException') {
    return new Error('Generated password does not meet Cognito password policy');
    
  } else if (originalError.name === 'InvalidParameterException') {
    // Check if this is the custom attribute schema error
    if (originalError.message && originalError.message.includes('custom:role') && originalError.message.includes('could not be determined')) {
      return new Error(
        `COGNITO_SCHEMA_ERROR: Attributes did not conform to the schema: Type for attribute {custom:role} could not be determined.\n\n` +
        `🔧 SOLUTION: Your AWS Cognito User Pool does not have the required custom attributes configured.\n\n` +
        `To fix this issue:\n` +
        `1. Run: npm run setup:cognito (for new User Pool)\n` +
        `2. Or run: npm run migrate:cognito (to migrate existing User Pool)\n\n` +
        `Required custom attributes:\n` +
        `- custom:role (String, max 50 chars)\n` +
        `- custom:roleId (Number, 1-10)\n` +
        `- custom:service (String, max 100 chars)\n` +
        `- custom:permissions (String, max 2048 chars)\n\n` +
        `📖 See docs/development-setup.md for detailed setup instructions.`
      );
      
    } else if (originalError.message && originalError.message.includes('custom:roleId') && originalError.message.includes('could not be determined')) {
      return new Error(
        `COGNITO_SCHEMA_ERROR: Type for attribute {custom:roleId} could not be determined.\n\n` +
        `🔧 SOLUTION: The custom:roleId attribute is not configured as a Number type in your Cognito User Pool.\n\n` +
        `To fix this issue:\n` +
        `1. Run: npm run setup:cognito (for new User Pool)\n` +
        `2. Or run: npm run migrate:cognito (to migrate existing User Pool)\n\n` +
        `The custom:roleId attribute must be configured as Number type with constraints: MinValue=1, MaxValue=10`
      );
      
    } else if (originalError.message && originalError.message.includes('custom:')) {
      return new Error(
        `COGNITO_SCHEMA_ERROR: ${originalError.message}\n\n` +
        `🔧 SOLUTION: This error is related to custom attributes configuration.\n\n` +
        `To fix this issue:\n` +
        `1. Ensure your Cognito User Pool has all required custom attributes configured\n` +
        `2. Run: npm run setup:cognito to configure the User Pool properly\n` +
        `3. Check that attribute types match the schema requirements\n\n` +
        `📖 See docs/development-setup.md for detailed setup instructions.`
      );
      
    } else {
      return new Error(`Invalid user data: ${originalError.message}`);
    }
    
  } else if (originalError.name === 'ResourceNotFoundException') {
    return new Error(
      `COGNITO_CONFIG_ERROR: User Pool not found.\n\n` +
      `🔧 SOLUTION: Check your environment configuration:\n` +
      `1. Verify COGNITO_USER_POOL_ID in your .env file\n` +
      `2. Ensure the User Pool exists in the specified AWS region\n` +
      `3. Check AWS credentials and permissions\n\n` +
      `Current User Pool ID: ${process.env.USERPOOLID || 'NOT_SET'}\n` +
      `Current Region: ${process.env.AWS_REGION || 'NOT_SET'}`
    );
    
  } else if (originalError.name === 'NotAuthorizedException') {
    return new Error(
      `COGNITO_AUTH_ERROR: Not authorized to perform this operation.\n\n` +
      `🔧 SOLUTION: Check your AWS credentials and permissions:\n` +
      `1. Verify AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY\n` +
      `2. Ensure the IAM user/role has cognito-idp permissions\n` +
      `3. Required permissions: cognito-idp:AdminCreateUser, cognito-idp:AdminSetUserPassword\n\n` +
      `Error details: ${originalError.message}`
    );
    
  } else {
    return new Error(`Failed to ${operation} in Cognito: ${originalError.message}`);
  }
}

/**
 * Wrapper for createUserInCognito with enhanced error handling
 */
export function wrapCognitoError(operation: string, email?: string) {
  return (error: any): never => {
    const errorDetails: CognitoErrorDetails = {
      originalError: error,
      operation
    };

    // Only add email if it's defined to avoid exactOptionalPropertyTypes issues
    if (email !== undefined) {
      errorDetails.email = email;
    }

    throw handleCognitoError(errorDetails);
  };
}

/**
 * Check if an error is related to custom attribute schema issues
 */
export function isCustomAttributeSchemaError(error: any): boolean {
  return error.name === 'InvalidParameterException' && 
         error.message && 
         error.message.includes('custom:') && 
         error.message.includes('could not be determined');
}

/**
 * Get troubleshooting guidance for custom attribute errors
 */
export function getCustomAttributeTroubleshootingGuide(): string {
  return `
🔧 COGNITO CUSTOM ATTRIBUTES TROUBLESHOOTING GUIDE

The error you're experiencing is due to missing custom attributes in your AWS Cognito User Pool.

QUICK FIX:
1. Run: npm run setup:cognito
   - This creates a new User Pool with the correct schema
   
2. Or run: npm run migrate:cognito  
   - This helps migrate from an existing User Pool

MANUAL SETUP (if scripts don't work):
1. Go to AWS Cognito Console
2. Select your User Pool
3. Go to "Attributes" section
4. Add these custom attributes:
   - custom:role (String, max 50 chars)
   - custom:roleId (Number, min 1, max 10)
   - custom:service (String, max 100 chars)  
   - custom:permissions (String, max 2048 chars)

IMPORTANT NOTES:
- Custom attributes cannot be modified after User Pool creation
- If your User Pool already has users, you'll need to create a new one
- Make sure to update your .env file with the new User Pool ID

📖 For detailed instructions, see: docs/development-setup.md
`;
}
