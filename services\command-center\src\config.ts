import * as dotenv from 'dotenv';

dotenv.config();
export const config = {
  serviceName: 'command-center',
  port: parseInt(process.env.COMMAND_CENTER_PORT || '3005', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  // Single Database Configuration
  database: {
    host: process.env.DB_HOST || '**************',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    database: process.env.DB_NAME || 'AperionHealth-Dev',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'Cloud@2025',
    schema: process.env.DB_SCHEMA || 'command_center',
    ssl: process.env.DB_SSL === 'true',
    maxConnections: parseInt(process.env.DB_CONNECTION_LIMIT || '10', 10),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000', 10),
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000', 10),
  },

  // JWT
  jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret',
  serviceJwtSecret: process.env.SERVICE_JWT_SECRET || 'your-service-jwt-secret',

  // CORS
  corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],

  // Logging
  logLevel: process.env.LOG_LEVEL || 'info',
};
