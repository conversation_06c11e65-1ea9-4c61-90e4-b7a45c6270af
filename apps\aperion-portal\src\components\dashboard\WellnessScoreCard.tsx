import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import { WellnessScore } from '@/lib/types';
import { HeartbeatLoader } from '@/components/ui/loaders';

interface WellnessScoreCardProps {
  wellnessScore?: WellnessScore | null;
  isLoading?: boolean;
}

const WellnessScoreCard: React.FC<WellnessScoreCardProps> = ({ wellnessScore, isLoading = false }) => {
  const [animatedScore, setAnimatedScore] = useState(0);
  const [isCardHovered, setIsCardHovered] = useState(false);
  
  if (isLoading) {
    return (
      <Card className="h-full shadow-sm border border-[hsl(var(--muted))]">
        <CardContent className="p-5 flex flex-col h-full">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-neutral-200 rounded w-28 animate-pulse"></div>
            <div className="h-4 w-4 bg-neutral-200 rounded-full animate-pulse"></div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <HeartbeatLoader size="lg" color="hsl(var(--wellness-teal))" />
          </div>
          <div className="mt-2 h-4 bg-neutral-200 rounded w-32 animate-pulse"></div>
        </CardContent>
      </Card>
    );
  }

  const score = wellnessScore?.score || 0;
  const dashArray = `${animatedScore}, 100`;
  const feedback = wellnessScore?.feedback || getDefaultFeedback(score);
  
  useEffect(() => {
    // Animate the score from 0 to the actual value
    const timer = setTimeout(() => {
      let start = 0;
      const duration = 1500; // ms
      const interval = 16; // ms (approx. 60 fps)
      const steps = Math.ceil(duration / interval);
      const increment = score / steps;
      
      const animation = setInterval(() => {
        start += increment;
        if (start >= score) {
          setAnimatedScore(score);
          clearInterval(animation);
        } else {
          setAnimatedScore(Math.floor(start));
        }
      }, interval);
      
      return () => clearInterval(animation);
    }, 500); // Delay to allow for card rendering
    
    return () => clearTimeout(timer);
  }, [score]);

  function getDefaultFeedback(score: number): string {
    if (score >= 80) return "Excellent Progress!";
    if (score >= 60) return "Great Progress!";
    if (score >= 40) return "Good Progress!";
    return "Getting Started!";
  }
  
  function getScoreColor(score: number): string {
    if (score >= 80) return "hsl(var(--wellness-green))";
    if (score >= 60) return "hsl(var(--wellness-teal))";
    if (score >= 40) return "hsl(var(--wellness-blue))";
    return "hsl(var(--wellness-purple))";
  }

  return (
    <Card 
      className="h-full shadow-sm border border-[hsl(var(--muted))] card-transition"
      onMouseEnter={() => setIsCardHovered(true)}
      onMouseLeave={() => setIsCardHovered(false)}
    >
      <CardContent className="p-5 flex flex-col h-full relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute -left-12 -top-12 w-28 h-28 rounded-full bg-[hsl(var(--wellness-teal))/5] transform transition-transform duration-500" 
          style={{ transform: isCardHovered ? 'scale(1.1)' : 'scale(1)' }}
        />
        
        <div className="flex items-center justify-between mb-4 relative">
          <h3 className="text-sm font-medium text-neutral-600">Wellness Score</h3>
          <div className="relative">
            <i 
              className={`material-icons text-sm transition-transform duration-300 ${isCardHovered ? 'scale-110' : ''}`}
              style={{ 
                color: 'hsl(var(--wellness-magenta))',
                filter: 'drop-shadow(0 0 2px hsl(var(--wellness-pink) / 70%))'
              }}
            >
              favorite
            </i>
          </div>
        </div>
        
        <div className="flex-1 flex items-center justify-center relative">
          <div className={`text-center transition-transform duration-300 ${isCardHovered ? 'scale-105' : ''}`}>
            <div className="relative inline-block">
              <svg className="w-32 h-32" viewBox="0 0 36 36">
                {/* Background circle */}
                <path 
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                  fill="none" 
                  stroke="hsl(var(--muted))" 
                  strokeWidth="3" 
                />
                {/* Animated progress circle */}
                <path 
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                  fill="none" 
                  stroke={getScoreColor(score)}
                  strokeWidth="3.5" 
                  strokeDasharray={dashArray}
                  strokeLinecap="round"
                  style={{ transition: "all 0.3s ease" }}
                  className="animate-progress"
                />
                {/* Score text */}
                <text 
                  x="18" 
                  y="20.5" 
                  textAnchor="middle" 
                  fontSize="8.5"
                  fontWeight="700"
                  fill="#334155"
                >
                  {animatedScore}
                </text>
                {/* Visual pulse effect around circle */}
                <circle
                  cx="18"
                  cy="18"
                  r="17.5"
                  fill="none"
                  stroke={`${getScoreColor(score)}50`}
                  strokeWidth="0.5"
                  className={isCardHovered ? "pulse" : ""}
                />
              </svg>
            </div>
            <div className="mt-2 relative">
              <p className={`text-sm font-medium transition-colors duration-300 ${isCardHovered ? `text-[${getScoreColor(score)}]` : 'text-neutral-700'}`}>
                {feedback}
              </p>
              <p className="text-xs text-neutral-500 mt-1">
                Based on your recent activities
              </p>
            </div>
          </div>
        </div>
        
        <Link to="/member/wellness-goals">
          <div className={`mt-2 text-sm text-[hsl(var(--wellness-teal))] flex items-center cursor-pointer transition-all ${isCardHovered ? 'translate-x-1' : ''}`}>
            View Wellness Details
            <i className={`material-icons text-sm ml-1 transition-transform ${isCardHovered ? 'translate-x-1' : ''}`}>arrow_forward</i>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
};

export default WellnessScoreCard;
