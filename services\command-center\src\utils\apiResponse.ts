import { ApiResponse, ApiError } from '@aperion/shared';

/**
 * Create a successful API response
 */
export function createSuccessResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data,
  };
}

/**
 * Create an error API response
 */
export function createErrorResponse(code: string, message: string, details?: any): ApiResponse<never> {
  const error: ApiError = {
    code,
    message,
    timestamp: new Date().toISOString(),
    details,
  };

  return {
    success: false,
    error,
  };
}

/**
 * Create a paginated success response
 */
export function createPaginatedResponse<T>(
  data: T[], 
  page: number, 
  limit: number, 
  total: number
): ApiResponse<{ data: T[]; meta: any }> {
  const totalPages = Math.ceil(total / limit);
  
  return {
    success: true,
    data: {
      data,
      meta: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    },
  };
}
