-- =====================================================
-- REMOVE DELETED STATUS FROM USER REFERENCES TABLE
-- Migration to remove 'deleted' status from shared_data.user_references status constraint
-- since we're now using hard deletes instead of soft deletes
-- =====================================================

-- Drop the existing check constraint
ALTER TABLE shared_data.user_references 
DROP CONSTRAINT IF EXISTS user_references_status_check;

-- Add the new check constraint without 'deleted' status
ALTER TABLE shared_data.user_references 
ADD CONSTRAINT user_references_status_check 
CHECK (status IN ('active', 'inactive', 'suspended', 'pending'));

-- Update comment for documentation
COMMENT ON CONSTRAINT user_references_status_check ON shared_data.user_references 
IS 'Allowed status values: active, inactive, suspended, pending. Deleted records are permanently removed from the database.';
