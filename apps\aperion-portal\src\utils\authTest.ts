/**
 * Authentication Test Utility
 * 
 * This file demonstrates how the authentication token system works
 * and can be used to test the token integration.
 */

import apiClient from '../services/apiClient';

/**
 * Test function to verify authentication token is properly attached to requests
 */
export const testAuthenticationFlow = async () => {
  console.log('🔐 Testing Authentication Flow...');
  
  // Check if token exists in localStorage
  const storedToken = localStorage.getItem('aperion_token');
  console.log('📋 Stored Token:', storedToken ? 'Present' : 'Not Found');
  
  if (!storedToken) {
    console.log('❌ No authentication token found. Please log in first.');
    return false;
  }
  
  try {
    // Test API call that requires authentication
    console.log('🚀 Making authenticated API call...');
    
    // This will automatically include the Bearer token in the Authorization header
    const response = await apiClient.get('/employer/employees', {
      params: { limit: 1 }
    });
    
    console.log('✅ Authenticated API call successful:', response.status);
    console.log('📊 Response data:', response.data);
    
    return true;
  } catch (error: any) {
    console.error('❌ Authentication test failed:', error.message);
    
    if (error.response?.status === 401) {
      console.log('🔒 Token appears to be invalid or expired');
    }
    
    return false;
  }
};

/**
 * Simulate the OTP verification response structure
 * This shows the exact format that the Auth2Page expects
 */
export const simulateOTPResponse = () => {
  return {
    status: 1,
    message: "OTP verified successfully",
    data: {
      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************.example-signature",
      user: {
        id: "+918681853074",
        email: "<EMAIL>",
        role: "employer",
        service: "employer-service"
      },
      roleId: 2
    }
  };
};

/**
 * Test token extraction from OTP response
 */
export const testTokenExtraction = () => {
  console.log('🧪 Testing Token Extraction...');
  
  const mockResponse = simulateOTPResponse();
  
  // This is how Auth2Page.tsx extracts the token (line 162)
  const { token, user, roleId } = mockResponse.data;
  
  console.log('📤 Extracted Token:', token.substring(0, 50) + '...');
  console.log('👤 Extracted User:', user);
  console.log('🏷️ Extracted Role ID:', roleId);
  
  // Verify the structure matches what's expected
  const isValidStructure = 
    mockResponse.status === 1 &&
    typeof mockResponse.data.token === 'string' &&
    typeof mockResponse.data.user === 'object' &&
    typeof mockResponse.data.roleId === 'number';
  
  console.log('✅ Response structure valid:', isValidStructure);
  
  return { token, user, roleId };
};

/**
 * Demonstrate how the API client automatically adds authentication headers
 */
export const demonstrateAuthHeaders = () => {
  console.log('🔧 API Client Configuration:');
  console.log('- Base URL: /api');
  console.log('- Timeout: 30 seconds');
  console.log('- Auto-adds Authorization header when token is present');
  console.log('- Auto-redirects to login on 401 errors');
  console.log('- Token source: localStorage.getItem("aperion_token")');
};

// Export for use in browser console during development
if (typeof window !== 'undefined') {
  (window as any).authTest = {
    testAuthenticationFlow,
    simulateOTPResponse,
    testTokenExtraction,
    demonstrateAuthHeaders
  };
}
