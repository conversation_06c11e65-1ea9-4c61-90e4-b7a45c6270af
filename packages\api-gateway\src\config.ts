import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root .env
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

export const config = {
  // Server configuration
  port: parseInt(process.env.API_GATEWAY_PORT || '3000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  // CORS configuration
  corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:4000'],

  // Rate limiting
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),

  // JWT configuration
  jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret',
  serviceJwtSecret: process.env.SERVICE_JWT_SECRET || 'your-service-jwt-secret',

  // AWS Cognito configuration
  cognito: {
    region: process.env.AWS_REGION || 'us-east-1',
    userPoolId: process.env.COGNITO_USER_POOL_ID || '',
    clientId: process.env.COGNITO_CLIENT_ID || '',
    clientSecret: process.env.COGNITO_CLIENT_SECRET || '',
  },

  // Service URLs
  services: {
    'member-service': {
      url: process.env.MEMBER_SERVICE_URL || 'http://localhost:3001',
      healthPath: '/health',
      timeout: 30000, // Increased to 30 seconds
    },
    'employer-service': {
      url: process.env.EMPLOYER_SERVICE_URL || 'http://localhost:3002',
      healthPath: '/health',
      timeout: 30000, // Increased to 30 seconds
    },
    'wellness-central': {
      url: process.env.WELLNESS_SERVICE_URL || 'http://localhost:3003',
      healthPath: '/health',
      timeout: 30000, // Increased to 30 seconds
    },
    'zenx-lms': {
      url: process.env.LMS_SERVICE_URL || 'http://localhost:3004',
      healthPath: '/health',
      timeout: 30000, // Increased to 30 seconds
    },
    'command-center': {
      url: process.env.COMMAND_CENTER_SERVICE_URL || 'http://localhost:3005',
      healthPath: '/health',
      timeout: 30000, // Increased to 30 seconds
    },
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.NODE_ENV === 'production' ? 'json' : 'simple',
  },

  // Circuit breaker configuration
  circuitBreaker: {
    failureThreshold: 5,
    resetTimeout: 30000,
    monitoringPeriod: 60000,
  },

  // Retry configuration
  retry: {
    maxRetries: 3,
    backoffMultiplier: 2,
    initialDelay: 1000,
    maxDelay: 10000,
  },

  // Health check configuration
  healthCheck: {
    interval: 30000, // 30 seconds
    timeout: 5000,   // 5 seconds
  },

  // Request timeout
  requestTimeout: 30000, // 30 seconds

  // Enable metrics collection
  enableMetrics: process.env.ENABLE_METRICS === 'true',
  metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
};

// Validate required configuration
const requiredEnvVars = [
  'JWT_SECRET',
  'SERVICE_JWT_SECRET',
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  process.exit(1);
}

// Validate service URLs
Object.entries(config.services).forEach(([serviceName, serviceConfig]) => {
  try {
    new URL(serviceConfig.url);
  } catch (error) {
    console.error(`Invalid URL for service ${serviceName}: ${serviceConfig.url}`);
    process.exit(1);
  }
});

export default config;
