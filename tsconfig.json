{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@aperion/shared": ["packages/shared/src/index"], "@aperion/shared/server": ["packages/shared/src/server"], "@aperion/shared/*": ["packages/shared/src/*"], "@aperion/auth/*": ["packages/auth/src/*"], "@aperion/database/*": ["packages/database/src/*"], "@/*": ["src/*"]}}, "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}