import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { authorize } from '../middleware/auth';
import { UserRole } from '@aperion/shared';
import { confirmEmailController } from '../controllers/confirmController';

/**
 * User Management Router
 *
 * Handles user operations including CRUD operations, role filtering,
 * statistics, account management, and email confirmation.
 *
 * Authentication: Required for all routes except account confirmation
 * Authorization: System Admin, Operations Manager
 *
 * @routes
 * GET    /test                    - API health check
 * GET    /account-confirmation    - Email confirmation (public)
 * GET    /dev                     - Get all users (development)
 * GET    /dev/filter              - Filter users by role (development)
 * GET    /dev/statistics          - Get user statistics (development)
 * GET    /dev/companies           - Get companies list (development)
 * GET    /dev/:id                 - Get user by ID (development)
 * POST   /dev                     - Create new user (development)
 * PUT    /dev/:id                 - Update user (development)
 * DELETE /dev/:id                 - Delete user (development)
 * PATCH  /dev/:id/suspend         - Suspend user (development)
 * PATCH  /dev/:id/activate        - Activate user (development)
 * GET    /                        - Get all users (production)
 * GET    /statistics              - Get user statistics (production)
 * GET    /companies               - Get companies list (production)
 * GET    /:id                     - Get user by ID (production)
 * POST   /                        - Create new user (production)
 * PUT    /:id                     - Update user (production)
 * DELETE /:id                     - Delete user (production)
 * PATCH  /:id/suspend             - Suspend user (production)
 * PATCH  /:id/activate            - Activate user (production)
 */

const router = Router();
const userController = new UserController();

// =============================================================================
// PUBLIC ROUTES (No Authorization Required)
// =============================================================================

/**
 * @route   GET /test
 * @desc    API health check endpoint
 * @access  Public
 * @returns {Object} Health status and timestamp
 */
router.get('/test', (_req, res) => {
  res.json({
    success: true,
    message: 'User management API is working!',
    timestamp: new Date().toISOString(),
    service: 'command-center',
    module: 'users',
  });
});

/**
 * @route   GET /account-confirmation
 * @desc    Confirm user account via email link
 * @access  Public (no auth required for confirmation links)
 * @query   {string} token - Encrypted confirmation token
 * @returns {Object} Confirmation result
 */
router.get('/account-confirmation', confirmEmailController);

// =============================================================================
// PROTECTED ROUTES (Authentication & Authorization Required)
// =============================================================================

// Apply authorization middleware to all subsequent routes except account confirmation
router.use((req, res, next) => {
  // Skip authorization for public confirmation endpoint (already handled above)
  if (req.path === '/account-confirmation') {
    return next();
  }
  // Apply authorization for all other routes
  return authorize([UserRole.SYSTEM_ADMIN, UserRole.OPERATIONS_MANAGER])(req, res, next);
});

// =============================================================================
// DEVELOPMENT ENDPOINTS
// =============================================================================

/**
 * @route   GET /dev
 * @desc    Get all users with pagination and filtering
 * @access  Private (System Admin, Operations Manager)
 * @query   {number} page - Page number (default: 1)
 * @query   {number} limit - Items per page (default: 20, max: 100)
 * @query   {string} search - Search term for name, email, or company
 * @query   {string} role - Filter by user role
 * @query   {string} status - Filter by user status
 * @query   {string} company - Filter by company
 * @query   {string} sortBy - Sort field (default: createdAt)
 * @query   {string} sortOrder - Sort order: asc|desc (default: desc)
 */
router.get('/dev', userController.getUsers);

/**
 * @route   GET /dev/filter
 * @desc    Get users filtered by role with enhanced role mapping
 * @access  Private (System Admin, Operations Manager)
 * @query   {number} page - Page number (default: 1)
 * @query   {number} limit - Items per page (default: 20, max: 100)
 * @query   {string} search - Search term for name, email, or company
 * @query   {string} role - Filter by user role (frontend role names: member, employer, coach, admin)
 * @query   {string} status - Filter by user status
 * @query   {string} company - Filter by company
 * @query   {string} sortBy - Sort field (default: createdAt)
 * @query   {string} sortOrder - Sort order: asc|desc (default: desc)
 * @description Role mapping: 'member' -> 'member', 'employer' -> 'employer', 'coach' -> 'wellness_coach', 'admin' -> 'system_admin'
 */
router.get('/dev/filter', userController.getUsersByRoleFilter);

/**
 * @route   GET /dev/statistics
 * @desc    Get user statistics and metrics
 * @access  Private (System Admin, Operations Manager)
 * @returns {Object} User statistics including counts and trends
 */
router.get('/dev/statistics', userController.getUserStatistics);

/**
 * @route   GET /dev/companies
 * @desc    Get list of companies for filtering
 * @access  Private (System Admin, Operations Manager)
 * @returns {Array} List of companies
 */
router.get('/dev/companies', userController.getCompanies);

/**
 * @route   GET /dev/:id
 * @desc    Get user by ID
 * @access  Private (System Admin, Operations Manager)
 * @param   {string} id - User ID
 * @returns {Object} User details
 */
router.get('/dev/:id', userController.getUserById);

/**
 * @route   POST /dev
 * @desc    Create a new user
 * @access  Private (System Admin, Operations Manager)
 * @body    {Object} userData - User data
 * @returns {Object} Created user details
 */
router.post('/dev', userController.createUser);

/**
 * @route   PUT /dev/:id
 * @desc    Update user by ID
 * @access  Private (System Admin, Operations Manager)
 * @param   {string} id - User ID
 * @body    {Object} userData - Updated user data
 * @returns {Object} Updated user details
 */
router.put('/dev/:id', userController.updateUser);

/**
 * @route   DELETE /dev/:id
 * @desc    Delete user by ID (soft delete)
 * @access  Private (System Admin, Operations Manager)
 * @param   {string} id - User ID
 * @returns {Object} Deletion confirmation
 */
router.delete('/dev/:id', userController.deleteUser);

/**
 * @route   PATCH /dev/:id/suspend
 * @desc    Suspend user account
 * @access  Private (System Admin, Operations Manager)
 * @param   {string} id - User ID
 * @returns {Object} Suspension confirmation
 */
router.patch('/dev/:id/suspend', userController.suspendUser);

/**
 * @route   PATCH /dev/:id/activate
 * @desc    Activate user account
 * @access  Private (System Admin, Operations Manager)
 * @param   {string} id - User ID
 * @returns {Object} Activation confirmation
 */
router.patch('/dev/:id/activate', userController.activateUser);

// =============================================================================
// PRODUCTION ENDPOINTS
// =============================================================================

/**
 * @route   GET /
 * @desc    Get all users with pagination and filtering
 * @access  Private (System Admin, Operations Manager)
 */
router.get('/', userController.getUsers);

/**
 * @route   GET /statistics
 * @desc    Get user statistics and metrics
 * @access  Private (System Admin, Operations Manager)
 */
router.get('/statistics', userController.getUserStatistics);

/**
 * @route   GET /companies
 * @desc    Get list of companies for filtering
 * @access  Private (System Admin, Operations Manager)
 */
router.get('/companies', userController.getCompanies);

/**
 * @route   GET /:id
 * @desc    Get user by ID
 * @access  Private (System Admin, Operations Manager)
 */
router.get('/:id', userController.getUserById);

/**
 * @route   POST /
 * @desc    Create a new user
 * @access  Private (System Admin, Operations Manager)
 */
router.post('/', userController.createUser);

/**
 * @route   PUT /:id
 * @desc    Update user by ID
 * @access  Private (System Admin, Operations Manager)
 */
router.put('/:id', userController.updateUser);

/**
 * @route   DELETE /:id
 * @desc    Delete user by ID (soft delete)
 * @access  Private (System Admin, Operations Manager)
 */
router.delete('/:id', userController.deleteUser);

/**
 * @route   PATCH /:id/suspend
 * @desc    Suspend user account
 * @access  Private (System Admin, Operations Manager)
 */
router.patch('/:id/suspend', userController.suspendUser);

/**
 * @route   PATCH /:id/activate
 * @desc    Activate user account
 * @access  Private (System Admin, Operations Manager)
 */
router.patch('/:id/activate', userController.activateUser);

export { router as userRouter };
