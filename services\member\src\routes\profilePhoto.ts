/**
 * Profile Photo Upload Routes
 * Handles profile photo upload endpoints with authentication and file validation
 */

import { Router } from 'express';
import { asyncHandler } from '@aperion/shared';
import { handleFileUpload } from '@aperion/shared/server';
import { uploadProfilePhoto, getProfilePhotoInfo, serveProfilePhoto, deleteProfilePhoto } from '../controllers/profilePhoto';
import { authMiddleware } from '../middleware/auth';

const router = Router();

/**
 * @route   POST /api/member/profile/photo
 * @desc    Upload profile photo
 * @access  Private (Member)
 * @body    multipart/form-data with 'photo' field
 */
router.post(
  '/',
  authMiddleware,
  handleFileUpload, // File upload middleware with validation
  asyncHandler(uploadProfilePhoto)
);

/**
 * @route   GET /api/member/profile/photo
 * @desc    Get current profile photo information
 * @access  Private (Member)
 */
router.get(
  '/',
  authMiddleware,
  asyncHandler(getProfilePhotoInfo)
);

/**
 * @route   GET /api/member/profile/photo/view
 * @desc    Serve profile photo securely without exposing AWS credentials
 * @access  Private (Member)
 */
router.get(
  '/view',
  authMiddleware,
  asyncHandler(serveProfilePhoto)
);

/**
 * @route   DELETE /api/member/profile/photo
 * @desc    Delete profile photo from S3 and database
 * @access  Private (Member)
 */
router.delete(
  '/',
  authMiddleware,
  asyncHandler(deleteProfilePhoto)
);

export { router as profilePhotoRouter };
