import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { HealthPlan } from '@/lib/types';
import { useQuery } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Download, ChevronRight } from 'lucide-react';

interface HealthIDCardProps {
  healthPlan?: HealthPlan;
  userName?: string;
}

const HealthIDCard: React.FC<HealthIDCardProps> = ({ healthPlan, userName }) => {
  const [showDetails, setShowDetails] = useState(false);
  
  // Fetch user data if not provided
  const { data: userData } = useQuery({
    queryKey: ['/api/users/me'],
    enabled: !userName
  });

  const displayName = userName || (userData ? `${userData.firstName} ${userData.lastName}` : 'Loading...');
  
  if (!healthPlan) {
    return (
      <Card className="rounded-lg shadow-sm border border-neutral-200">
        <CardContent className="p-3">
          <div className="text-center py-3">
            <span className="material-icons text-2xl text-neutral-300">health_and_safety</span>
            <h3 className="mt-2 text-sm font-medium">No Health Plan Found</h3>
            <p className="mt-1 text-neutral-500 text-xs">
              Contact HR for assistance
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  return (
    <>
      <div className="md:flex md:space-x-4">
        {/* Main ID Card with purple hue - Enhanced design with more depth */}
        <div className="md:w-1/2 mb-4 md:mb-0 transform transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
          <div className="rounded-xl overflow-hidden border-0 bg-white" 
               style={{
                 boxShadow: "0 10px 30px -10px rgba(124, 58, 237, 0.25), 0 5px 15px -5px rgba(124, 58, 237, 0.1)",
                 position: "relative"
               }}>
            {/* Card background with enhanced gradients */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#8b5cf6]/15 via-[#a78bfa]/20 to-[#7c3aed]/10 z-0"></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-[#a78bfa]/40 to-transparent rounded-bl-full z-0"></div>
            <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-[#7c3aed]/30 to-transparent rounded-tr-full z-0"></div>
            
            {/* Holographic effect element */}
            <div className="absolute top-4 right-8 w-16 h-16 rounded-full bg-gradient-to-bl from-[#8b5cf6]/5 via-[#a78bfa]/10 to-[#7c3aed]/5 blur-sm z-0"></div>
            
            {/* Card content with enhanced depth */}
            <div className="p-6 relative z-10">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-[#6b7280] font-medium mb-1">Member Name</p>
                  <p className="text-base font-semibold text-[#111827] mb-6">{displayName}</p>
                </div>
                <div className="bg-white rounded-full p-2 shadow-md flex items-center justify-center" 
                     style={{ boxShadow: "0 4px 6px -1px rgba(124, 58, 237, 0.1), 0 2px 4px -1px rgba(124, 58, 237, 0.06)" }}>
                  <span className="text-[#7c3aed]">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 16H15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 13V19" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M8.5 2H15.5C16.8807 2 18 3.11929 18 4.5V11.5C18 12.8807 16.8807 14 15.5 14H8.5C7.11929 14 6 12.8807 6 11.5V4.5C6 3.11929 7.11929 2 8.5 2Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M10.5 7C10.5 8.10457 9.60457 9 8.5 9C7.39543 9 6.5 8.10457 6.5 7C6.5 5.89543 7.39543 5 8.5 5C9.60457 5 10.5 5.89543 10.5 7Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </span>
                </div>
              </div>
              
              <div className="flex justify-between mb-6">
                <div>
                  <p className="text-sm text-[#6b7280] font-medium mb-1">Member ID</p>
                  <p className="text-base font-semibold text-[#7c3aed]">{healthPlan.memberId}</p>
                </div>
                <div>
                  <p className="text-sm text-[#6b7280] font-medium mb-1">Group #</p>
                  <p className="text-base font-semibold text-[#111827]">{healthPlan.groupNumber || 'N/A'}</p>
                </div>
              </div>
              
              <div className="flex justify-between items-center pt-2 border-t border-[#a78bfa]/30">
                <div>
                  <p className="text-sm text-[#6b7280] font-medium mb-1">Effective Date</p>
                  <p className="text-sm font-medium text-[#111827]">{formatDate(healthPlan.coveragePeriodStart)}</p>
                </div>
                
                <div className="flex items-center text-[#7c3aed] font-medium bg-white py-1.5 px-4 rounded-full shadow-md text-sm">
                  <span className="inline-block w-2 h-2 bg-[#7c3aed] rounded-full mr-2 animate-pulse"></span>
                  Active
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Quick benefits section */}
        <div className="md:w-1/2">
          <Card className="rounded-xl shadow-sm border border-[#a78bfa]/20">
            <CardContent className="p-4">
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center">
                  <div className="bg-[#8b5cf6]/10 p-1.5 rounded-md mr-2">
                    <span className="material-icons text-[#7c3aed] text-sm">shield</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-sm text-[#111827]">{healthPlan.planName}</h3>
                    <p className="text-xs text-[#6b7280]">{healthPlan.planType} Plan</p>
                  </div>
                </div>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setShowDetails(true)}
                  className="h-8 text-xs rounded-lg text-[#7c3aed] border-[#a78bfa]/30 hover:bg-[#8b5cf6]/5"
                >
                  Plan Details
                  <ChevronRight className="h-3 w-3 ml-1" />
                </Button>
              </div>
              
              <div className="space-y-2 mb-4">
                <div className="bg-[#f5f3ff] rounded-lg p-2.5 flex justify-between items-center">
                  <div className="flex items-center">
                    <span className="material-icons text-[#7c3aed]/70 text-sm mr-2">local_hospital</span>
                    <span className="text-xs font-medium text-[#4b5563]">Primary Care</span>
                  </div>
                  <span className="text-xs bg-[#8b5cf6]/10 text-[#7c3aed] px-2.5 py-1 rounded-md font-medium">
                    ${healthPlan.primaryCareVisitCopay || 'N/A'}
                  </span>
                </div>
                
                <div className="bg-[#f5f3ff] rounded-lg p-2.5 flex justify-between items-center">
                  <div className="flex items-center">
                    <span className="material-icons text-[#7c3aed]/70 text-sm mr-2">medical_services</span>
                    <span className="text-xs font-medium text-[#4b5563]">Specialist</span>
                  </div>
                  <span className="text-xs bg-[#8b5cf6]/10 text-[#7c3aed] px-2.5 py-1 rounded-md font-medium">
                    ${healthPlan.specialistVisitCopay || 'N/A'}
                  </span>
                </div>
                
                <div className="bg-[#f5f3ff] rounded-lg p-2.5 flex justify-between items-center">
                  <div className="flex items-center">
                    <span className="material-icons text-[#7c3aed]/70 text-sm mr-2">emergency</span>
                    <span className="text-xs font-medium text-[#4b5563]">Emergency</span>
                  </div>
                  <span className="text-xs bg-[#8b5cf6]/10 text-[#7c3aed] px-2.5 py-1 rounded-md font-medium">
                    ${healthPlan.emergencyRoomCopay || 'N/A'}
                  </span>
                </div>
              </div>
              
              <div className="border-t border-[#a78bfa]/10 pt-3 flex justify-between items-center">
                <div className="flex items-center text-xs text-[#6b7280]">
                  <span className="material-icons text-xs mr-1">help_outline</span>
                  For assistance: 1-800-555-HELP
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7 text-xs text-[#7c3aed] hover:bg-[#8b5cf6]/5"
                  onClick={() => setShowDetails(true)}
                >
                  <Download className="h-3 w-3 mr-1" />
                  Download ID Card
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Detailed Plan Information Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-[#7c3aed]">Your Health Plan Details</DialogTitle>
            <DialogDescription>
              Complete coverage information for your {healthPlan.planName} plan
            </DialogDescription>
          </DialogHeader>
          
          <div className="mt-3">
            <div className="bg-[#8b5cf6]/5 p-4 rounded-lg mb-5 border border-[#a78bfa]/20">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-bold text-sm text-[#111827]">{healthPlan.planName}</h3>
                  <p className="text-xs text-[#4b5563] mt-1">Member ID: <span className="text-[#7c3aed] font-medium">{healthPlan.memberId}</span></p>
                  <p className="text-xs text-[#4b5563] mt-0.5">Group #: {healthPlan.groupNumber || 'N/A'}</p>
                </div>
                <div className="text-right">
                  <div className="bg-[#8b5cf6]/10 text-[#7c3aed] text-xs font-medium px-2.5 py-1 rounded-full mb-1.5">
                    {healthPlan.planType}
                  </div>
                  <p className="text-xs text-[#4b5563]">Effective: {formatDate(healthPlan.coveragePeriodStart)}</p>
                </div>
              </div>
            </div>
            
            <h4 className="text-xs font-medium mb-3 flex items-center text-[#111827]">
              <span className="material-icons text-[#7c3aed] text-sm mr-1.5">payments</span>
              Coverage Information
            </h4>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between pb-2.5 border-b border-[#a78bfa]/10">
                <div>
                  <p className="font-medium text-[#111827]">Primary Care Visit</p>
                  <p className="text-xs text-[#6b7280]">In-network</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-[#7c3aed]">
                    {healthPlan.primaryCareVisitCopay 
                      ? `$${healthPlan.primaryCareVisitCopay} copay` 
                      : 'Check plan details'}
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between pb-2.5 border-b border-[#a78bfa]/10">
                <div>
                  <p className="font-medium text-[#111827]">Specialist Visit</p>
                  <p className="text-xs text-[#6b7280]">In-network</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-[#7c3aed]">
                    {healthPlan.specialistVisitCopay 
                      ? `$${healthPlan.specialistVisitCopay} copay` 
                      : 'Check plan details'}
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between pb-2.5 border-b border-[#a78bfa]/10">
                <div>
                  <p className="font-medium text-[#111827]">Emergency Room</p>
                  <p className="text-xs text-[#6b7280]">In-network</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-[#7c3aed]">
                    {healthPlan.emergencyRoomCopay 
                      ? `$${healthPlan.emergencyRoomCopay} copay` 
                      : 'Check plan details'}
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between pb-2.5 border-b border-[#a78bfa]/10">
                <div>
                  <p className="font-medium text-[#111827]">Annual Deductible</p>
                  <p className="text-xs text-[#6b7280]">Individual/Family</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-[#7c3aed]">
                    {healthPlan.individualDeductible && healthPlan.familyDeductible
                      ? `$${healthPlan.individualDeductible.toLocaleString()}/$${healthPlan.familyDeductible.toLocaleString()}`
                      : 'Check plan details'}
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between">
                <div>
                  <p className="font-medium text-[#111827]">Out-of-Pocket Maximum</p>
                  <p className="text-xs text-[#6b7280]">Individual/Family</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-[#7c3aed]">
                    {healthPlan.individualOutOfPocketMax && healthPlan.familyOutOfPocketMax
                      ? `$${healthPlan.individualOutOfPocketMax.toLocaleString()}/$${healthPlan.familyOutOfPocketMax.toLocaleString()}`
                      : 'Check plan details'}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="mt-5 flex justify-end">
              <Button className="bg-[#7c3aed] text-white hover:bg-[#6d28d9] h-8 text-xs rounded-lg">
                <Download className="h-3 w-3 mr-1.5" />
                Download ID Card PDF
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default HealthIDCard;
