import { Request, Response } from 'express';
import { SessionService } from '../services/SessionService';
import { logger } from '../../../utils/logger';
import { 
  createUserSessionSchema, 
  updateUserSessionSchema, 
  userSessionQuerySchema,
  UserSessionQueryParams 
} from '../types/session';
import { HttpStatus } from '@aperion/shared';

export class SessionController {
  private sessionService: SessionService;

  constructor() {
    this.sessionService = new SessionService();
  }

  /**
   * Get all user sessions with filtering and pagination
   */
  getUserSessions = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate and parse query parameters
      const validationResult = userSessionQuerySchema.safeParse(req.query);
      
      if (!validationResult.success) {
        logger.warn('Invalid query parameters for getUserSessions:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const params: UserSessionQueryParams = validationResult.data;
      const result = await this.sessionService.getUserSessions(params);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getUserSessions controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get user session by ID
   */
  getUserSessionById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Session ID is required'
          }
        });
        return;
      }

      const result = await this.sessionService.getUserSessionById(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_SESSION_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getUserSessionById controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Create a new user session
   */
  createUserSession = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request body
      const validationResult = createUserSessionSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for createUserSession:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const sessionData = validationResult.data;
      const result = await this.sessionService.createUserSession(sessionData);

      if (result.success) {
        res.status(HttpStatus.CREATED).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in createUserSession controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Update a user session
   */
  updateUserSession = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Session ID is required'
          }
        });
        return;
      }

      // Validate request body
      const validationResult = updateUserSessionSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        logger.warn('Invalid request body for updateUserSession:', validationResult.error);
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: validationResult.error.errors
          }
        });
        return;
      }

      const sessionData = validationResult.data;
      const result = await this.sessionService.updateUserSession(id, sessionData);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_SESSION_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.BAD_REQUEST).json(result);
      }
    } catch (error) {
      logger.error('Error in updateUserSession controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Delete a user session
   */
  deleteUserSession = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Session ID is required'
          }
        });
        return;
      }

      const result = await this.sessionService.deleteUserSession(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_SESSION_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in deleteUserSession controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Terminate a user session
   */
  terminateUserSession = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Session ID is required'
          }
        });
        return;
      }

      const result = await this.sessionService.terminateUserSession(id);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_SESSION_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in terminateUserSession controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Update session activity
   */
  updateSessionActivity = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Session ID is required'
          }
        });
        return;
      }

      const result = await this.sessionService.updateSessionActivity(sessionId);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else if (result.error?.code === 'USER_SESSION_NOT_FOUND') {
        res.status(HttpStatus.NOT_FOUND).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in updateSessionActivity controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get user session statistics
   */
  getUserSessionStatistics = async (_req: Request, res: Response): Promise<void> => {
    try {
      const result = await this.sessionService.getUserSessionStatistics();

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getUserSessionStatistics controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Get active sessions for a specific user
   */
  getActiveUserSessions = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;

      if (!userId) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'User ID is required'
          }
        });
        return;
      }

      const userIdNum = parseInt(userId, 10);
      if (isNaN(userIdNum)) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid User ID format'
          }
        });
        return;
      }

      const result = await this.sessionService.getActiveUserSessions(userIdNum);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in getActiveUserSessions controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };

  /**
   * Terminate all sessions for a specific user
   */
  terminateAllUserSessions = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;

      if (!userId) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'User ID is required'
          }
        });
        return;
      }

      const userIdNum = parseInt(userId, 10);
      if (isNaN(userIdNum)) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid User ID format'
          }
        });
        return;
      }

      const result = await this.sessionService.terminateAllUserSessions(userIdNum);

      if (result.success) {
        res.status(HttpStatus.OK).json(result);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      logger.error('Error in terminateAllUserSessions controller:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      });
    }
  };
}
