import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import {
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Shield,
  Users,
  Heart,
  Activity,
  Lock
} from 'lucide-react';



const Auth2Page: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { loginWithOTP } = useAuth();

  // Get email and session from sessionStorage
  const storedEmail = sessionStorage.getItem('auth_email');
  const storedSession = sessionStorage.getItem('auth_session');

  // Check if we have the required data from step 1
  const hasAuthData = storedEmail && storedSession;

  // OTP entry state
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [otpError, setOtpError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'verifying' | 'success' | 'error'>('idle');
  const [resendTimer, setResendTimer] = useState(30); // Start with 30 seconds on page load
  const [isResending, setIsResending] = useState(false); // New state for resend loading
  const [shakeOtp, setShakeOtp] = useState(false);

  const otpRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Check if we have auth data, redirect to send-code if not
  useEffect(() => {
    if (!hasAuthData) {
      toast({
        title: "Session expired",
        description: "Please start the authentication process again.",
        variant: "destructive",
      });
      navigate('/auth/send-code');
    }
  }, [hasAuthData, navigate, toast]);

  // Timer effect for resend OTP with proper cleanup
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(prev => {
          if (prev <= 1) {
            // Timer reached 0, clear interval
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    // Cleanup function to clear interval on unmount or dependency change
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [resendTimer]);



  // OTP handling functions
  const handleOtpChange = (index: number, value: string) => {
    // Prevent input changes during verification
    if (isVerifying || verificationStatus === 'verifying') return;

    if (value.length > 1 || !/^\d*$/.test(value)) return; // Only allow single digits

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setOtpError('');
    setVerificationStatus('idle');

    // Auto-focus next input
    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }

    // Auto-verify when all digits are entered
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      // Immediate visual feedback
      setIsVerifying(true);
      setVerificationStatus('verifying');
      setTimeout(() => verifyOtp(newOtp.join('')), 100);
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    // Prevent keyboard input during verification
    if (isVerifying || verificationStatus === 'verifying') {
      e.preventDefault();
      return;
    }

    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };



  const verifyOtp = async (otpCode: string) => {
    setIsLoading(true);
    setIsVerifying(true);
    setVerificationStatus('verifying');
    setOtpError('');

    try {
      // Get email and session from sessionStorage
      const email = sessionStorage.getItem('auth_email');
      const session = sessionStorage.getItem('auth_session');

      if (!email || !session) {
        setOtpError('Session expired. Please start over.');
        setVerificationStatus('error');
        setIsVerifying(false);
        setTimeout(() => navigate('/auth/send-code'), 2000);
        return;
      }

      // Call the verify OTP API via API Gateway
      const response = await fetch('http://localhost:3000/api/command-center/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userName: email,
          answer: otpCode,
          session,
        }),
      });

      const data = await response.json();

      if (data.status === 1) {
        // Success - extract user data and roleId from API response
        const { token, user, roleId } = data.data;

        // Set success status
        setVerificationStatus('success');

        // Store additional Cognito tokens if provided
        if (data.data.accessToken) {
          sessionStorage.setItem('cognito_access_token', data.data.accessToken);
        }
        if (data.data.idToken) {
          sessionStorage.setItem('cognito_id_token', data.data.idToken);
        }
        if (data.data.refreshToken) {
          sessionStorage.setItem('cognito_refresh_token', data.data.refreshToken);
        }

        // Clear auth session data
        sessionStorage.removeItem('auth_email');
        sessionStorage.removeItem('auth_session');

        toast({
          title: "✅ Verification Successful",
          description: "Redirecting to your dashboard...",
          duration: 2000,
        });

        // Keep loading state until navigation completes
        // Use AuthContext to handle login and role-based navigation
        loginWithOTP(token, user, roleId);
      } else {
        // Handle API errors
        setVerificationStatus('error');
        setIsVerifying(false);
        setShakeOtp(true);
        setOtpError(data.message || 'Invalid OTP code');

        // Reset OTP inputs for retry
        // setOtp(['', '', '', '', '', '']);

        // Reset shake animation after 700ms
        setTimeout(() => setShakeOtp(false), 700);

        // Focus first input for retry after animation
        setTimeout(() => otpRefs.current[0]?.focus(), 750);
      }
    } catch (error) {
      console.error('Verify OTP error:', error);
      setVerificationStatus('error');
      setIsVerifying(false);
      setOtpError('Network error. Please check your connection and try again.');

      // Reset OTP inputs for retry
      setOtp(['', '', '', '', '', '']);

      // Focus first input for retry
      setTimeout(() => otpRefs.current[0]?.focus(), 100);
    } finally {
      // Only clear loading if not successful (success state maintained until navigation)
      if (verificationStatus !== 'success') {
        setIsLoading(false);
      }
    }
  };



  const handleResendOTP = async () => {
    const email = sessionStorage.getItem('auth_email');

    if (!email) {
      setOtpError('Session expired. Please start over.');
      setTimeout(() => navigate('/auth/send-code'), 2000);
      return;
    }

    // Immediately disable button and start loading state
    setIsResending(true);
    setOtp(['', '', '', '', '', '']);
    setOtpError('');
    setVerificationStatus('idle');

    try {
      // Call the send OTP API again via API Gateway
      const response = await fetch('http://localhost:3000/api/command-center/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userName: email,
        }),
      });

      const data = await response.json();

      if (data.status === 1) {
        // Update session ID
        sessionStorage.setItem('auth_session', data.session);

        // Restart the 30-second countdown timer
        setResendTimer(30);

        // Show success toast
        toast({
          title: "✅ OTP Resent Successfully",
          description: "A new verification code has been sent to your email",
          duration: 4000,
        });

        // Focus first OTP input for user convenience
        setTimeout(() => otpRefs.current[0]?.focus(), 100);
      } else {
        // Handle API errors - show error but don't restart timer
        setOtpError(data.message || 'Failed to resend OTP. Please try again.');

        toast({
          title: "❌ Resend Failed",
          description: data.message || 'Failed to resend OTP. Please try again.',
          variant: "destructive",
          duration: 4000,
        });
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      const errorMessage = 'Network error. Please check your connection and try again.';
      setOtpError(errorMessage);

      toast({
        title: "❌ Network Error",
        description: errorMessage,
        variant: "destructive",
        duration: 4000,
      });
    } finally {
      setIsResending(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="min-h-screen flex flex-col lg:flex-row">

        {/* Left Side - Project Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 relative overflow-hidden"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-32 h-32 bg-white rounded-full blur-xl"></div>
            <div className="absolute bottom-32 right-20 w-48 h-48 bg-white rounded-full blur-xl"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white rounded-full blur-lg"></div>
          </div>

          <div className="relative z-10 flex flex-col justify-center px-12 xl:px-20 text-white">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
            >
              <div className="flex items-center mb-8">
                <div className="w-12 h-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center mr-4">
                  <Heart className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-3xl font-bold">Aperion Health</h1>
              </div>

              <h2 className="text-4xl xl:text-5xl font-bold mb-6 leading-tight">
                Secure Access to Your
                <span className="block text-blue-200">Health Journey</span>
              </h2>

              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                Experience seamless, secure authentication designed for healthcare professionals and patients.
              </p>

              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Shield className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Bank-Level Security</h3>
                    <p className="text-blue-200">Multi-factor authentication keeps your data safe</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Trusted by Healthcare</h3>
                    <p className="text-blue-200">Used by 10,000+ healthcare professionals</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.0, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Activity className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Real-time Access</h3>
                    <p className="text-blue-200">Instant access to your health dashboard</p>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Right Side - Authentication Form */}
        <div className="flex-1 lg:w-1/2 xl:w-2/5 flex flex-col">

          {/* Mobile Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:hidden bg-gradient-to-r from-blue-600 to-indigo-700 p-6 text-white"
          >
            <div className="flex items-center justify-center">
              <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center mr-3">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold">Aperion Health</h1>
            </div>
            <p className="text-center text-blue-100 mt-2 text-sm">
              Secure Healthcare Authentication
            </p>
          </motion.div>

          <div className="flex-1 flex items-center justify-center p-4 sm:p-6 lg:p-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="w-full max-w-md"
            >
            <Card className="shadow-2xl border-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-md">
              <CardHeader className="space-y-1 text-center pb-6">
                <motion.div
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                  className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg"
                >
                  <Lock className="w-8 h-8" />
                </motion.div>
                <CardTitle className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-400 dark:to-indigo-500">
                  Enter Verification Code
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-300">
                  Enter the 6-digit code sent to your email
                </CardDescription>
              </CardHeader>

          <CardContent className="space-y-6">
            {/* OTP Entry */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                {/* OTP Input Grid */}
                <div className="space-y-4">
                  <motion.div
                    className="flex gap-2 justify-center"
                    animate={shakeOtp ? {
                      x: [0, -10, 10, -10, 10, -5, 5, 0],
                      transition: { duration: 0.7, ease: "easeInOut" }
                    } : {}}
                  >
                    {otp.map((digit, index) => (
                      <Input
                        key={`otp-${index}`}
                        ref={(el) => (otpRefs.current[index] = el)}
                        type="text"
                        inputMode="numeric"
                        maxLength={1}
                        value={digit}
                        disabled={isVerifying || verificationStatus === 'verifying' || verificationStatus === 'success'}
                        onChange={(e) => handleOtpChange(index, e.target.value)}
                        onKeyDown={(e) => handleOtpKeyDown(index, e)}
                        className={`w-12 h-12 sm:w-14 sm:h-14 text-center text-lg font-semibold border-2 transition-all duration-300 ${
                          shakeOtp || otpError
                            ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                            : verificationStatus === 'verifying' || isVerifying
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 cursor-not-allowed'
                            : verificationStatus === 'success'
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20 cursor-not-allowed'
                            : 'border-slate-200 focus:border-blue-500 hover:border-slate-300'
                        }`}
                      />
                    ))}
                  </motion.div>
                </div>

                {/* Error Display */}
                {otpError && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Alert variant="destructive" className="border-red-200 bg-red-50 dark:bg-red-900/20">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="font-medium">{otpError}</AlertDescription>
                    </Alert>
                  </motion.div>
                )}

                {/* Verification Status Messages */}
                {verificationStatus === 'verifying' && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-900/20">
                      <div className="flex items-center">
                        <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                        <AlertDescription className="text-blue-700 dark:text-blue-300 font-medium">
                          Verifying your code...
                        </AlertDescription>
                      </div>
                    </Alert>
                  </motion.div>
                )}

                {verificationStatus === 'success' && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Alert className="border-green-200 bg-green-50 dark:bg-green-900/20">
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                        <AlertDescription className="text-green-700 dark:text-green-300 font-medium">
                          Verification successful! Redirecting to your dashboard...
                        </AlertDescription>
                      </div>
                    </Alert>
                  </motion.div>
                )}

                {/* Resend OTP Section */}
                <div className="text-center space-y-3 py-4">
                  {resendTimer > 0 ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-slate-400 border-t-transparent rounded-full animate-spin"></div>
                      <p className="text-sm text-slate-600 dark:text-slate-400 font-medium">
                        Resend OTP in {resendTimer}s
                      </p>
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      onClick={handleResendOTP}
                      disabled={isVerifying || verificationStatus === 'verifying' || verificationStatus === 'success' || isResending}
                      className="h-10 px-6 text-sm border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      {isResending ? (
                        <>
                          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2" />
                          Resend OTP
                        </>
                      )}
                    </Button>
                  )}

                  {/* {attemptCount > 0 && (
                    <div className="flex items-center justify-center gap-2">
                      <div className="flex gap-1">
                        {[...Array(3)].map((_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full ${
                              i < attemptCount ? 'bg-red-400' : 'bg-slate-200 dark:bg-slate-700'
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-xs text-slate-500">
                        {attemptCount}/3 attempts
                      </p>
                    </div>
                  )} */}
                </div>

              </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
        </div>
      </div>
    </div>
  );
};

export default Auth2Page;
