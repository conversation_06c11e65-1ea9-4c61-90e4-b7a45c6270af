-- =====================================================
-- MEMBER SERVICE SCHEMA
-- =====================================================

-- Members Table
CREATE TABLE member_service.members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cognito_user_id VARCHAR(255) UNIQUE NOT NULL,
  employer_id UUID NOT NULL, -- References employer_service.employers(id)
  activation_code VARCHAR(50) UNIQUE,
  activation_code_used_at TIMESTAMP,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  date_of_birth DATE,
  gender VARCHAR(20) CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
  address JSONB,
  profile_picture_url VARCHAR(500),
  emergency_contacts JSONB DEFAULT '[]',
  health_preferences JSONB DEFAULT '{}',
  privacy_settings JSONB DEFAULT '{"data_sharing": true, "marketing": false, "analytics": true}',
  onboarding_completed BOOLEAN DEFAULT false,
  onboarding_step INTEGER DEFAULT 1,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending_activation')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Member Dependents Table
CREATE TABLE member_service.member_dependents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES member_service.members(id) ON DELETE CASCADE,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  relationship VARCHAR(50) NOT NULL CHECK (relationship IN ('spouse', 'child', 'parent', 'sibling', 'other')),
  date_of_birth DATE NOT NULL,
  member_id_dependent VARCHAR(50) UNIQUE,
  gender VARCHAR(20) CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Member Health Metrics Table
CREATE TABLE member_service.member_health_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES member_service.members(id) ON DELETE CASCADE,
  metric_type VARCHAR(50) NOT NULL,
  value DECIMAL(10,2) NOT NULL,
  unit VARCHAR(20),
  target_value DECIMAL(10,2),
  recorded_at TIMESTAMP DEFAULT NOW(),
  notes TEXT,
  source VARCHAR(50) DEFAULT 'manual' CHECK (source IN ('manual', 'device', 'import', 'coach')),
  verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Member Health Goals Table
CREATE TABLE member_service.member_health_goals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES member_service.members(id) ON DELETE CASCADE,
  goal_type VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  target_value DECIMAL(10,2),
  target_unit VARCHAR(20),
  target_date DATE,
  current_value DECIMAL(10,2) DEFAULT 0,
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Member Wellness Plans Table
CREATE TABLE member_service.member_wellness_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES member_service.members(id) ON DELETE CASCADE,
  plan_name VARCHAR(200) NOT NULL,
  description TEXT,
  plan_type VARCHAR(50) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  goals JSONB DEFAULT '[]',
  activities JSONB DEFAULT '[]',
  coach_id UUID, -- References wellness_service.wellness_coaches(id)
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Member Appointments Table
CREATE TABLE member_service.member_appointments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES member_service.members(id) ON DELETE CASCADE,
  coach_id UUID, -- References wellness_service.wellness_coaches(id)
  appointment_type VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  scheduled_at TIMESTAMP NOT NULL,
  duration_minutes INTEGER DEFAULT 30,
  location VARCHAR(200),
  meeting_link VARCHAR(500),
  status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no_show')),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);



-- =====================================================
-- INDEXES FOR MEMBER SERVICE
-- =====================================================

CREATE INDEX idx_members_employer ON member_service.members(employer_id);
CREATE INDEX idx_members_activation_code ON member_service.members(activation_code);
CREATE INDEX idx_members_email ON member_service.members(email);
CREATE INDEX idx_members_status ON member_service.members(status);
CREATE INDEX idx_members_cognito_id ON member_service.members(cognito_user_id);

CREATE INDEX idx_health_metrics_member ON member_service.member_health_metrics(member_id);
CREATE INDEX idx_health_metrics_type ON member_service.member_health_metrics(metric_type);
CREATE INDEX idx_health_metrics_recorded ON member_service.member_health_metrics(recorded_at);
CREATE INDEX idx_health_metrics_source ON member_service.member_health_metrics(source);

CREATE INDEX idx_health_goals_member ON member_service.member_health_goals(member_id);
CREATE INDEX idx_health_goals_type ON member_service.member_health_goals(goal_type);
CREATE INDEX idx_health_goals_status ON member_service.member_health_goals(status);

CREATE INDEX idx_wellness_plans_member ON member_service.member_wellness_plans(member_id);
CREATE INDEX idx_wellness_plans_coach ON member_service.member_wellness_plans(coach_id);
CREATE INDEX idx_wellness_plans_status ON member_service.member_wellness_plans(status);

CREATE INDEX idx_appointments_member ON member_service.member_appointments(member_id);
CREATE INDEX idx_appointments_coach ON member_service.member_appointments(coach_id);
CREATE INDEX idx_appointments_scheduled ON member_service.member_appointments(scheduled_at);
CREATE INDEX idx_appointments_status ON member_service.member_appointments(status);



-- =====================================================
-- TRIGGERS FOR MEMBER SERVICE
-- =====================================================

CREATE TRIGGER update_members_updated_at 
  BEFORE UPDATE ON member_service.members 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_health_goals_updated_at 
  BEFORE UPDATE ON member_service.member_health_goals 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_wellness_plans_updated_at 
  BEFORE UPDATE ON member_service.member_wellness_plans 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at
  BEFORE UPDATE ON member_service.member_appointments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


