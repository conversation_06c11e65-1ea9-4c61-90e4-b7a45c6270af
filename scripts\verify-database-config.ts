#!/usr/bin/env tsx

/**
 * Aperion Health Database Configuration Verification Script
 * Verifies all services are properly configured for single database architecture
 */

import { Client, ClientConfig } from 'pg';
import * as fs from 'fs';

// Expected database configuration
interface DatabaseConfig extends ClientConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
}

const expectedConfig: DatabaseConfig = {
  host: '**************',
  port: 5432,
  database: 'AperionHealth-Dev',
  user: 'postgres',
  password: 'Cloud@2025'
};

// Service schema mappings
const serviceSchemas: Record<string, string> = {
  'member': 'member_service',
  'employer': 'employer_service',
  'wellness-central': 'wellness_service',
  'zenx-lms': 'lms_service',
  'command-center': 'command_center'
};

// Services to verify
const services: string[] = Object.keys(serviceSchemas);

interface VerificationResult {
  status: 'pending' | 'success' | 'warning' | 'error';
  details: string[];
}

interface OverallResult {
  status: 'pending' | 'success' | 'warning' | 'error';
  score: number;
  total: number;
}

interface VerificationResults {
  database: VerificationResult;
  schemas: VerificationResult;
  services: VerificationResult;
  overall: OverallResult;
}

class ConfigurationVerifier {
  private results: VerificationResults;

  constructor() {
    this.results = {
      database: { status: 'pending', details: [] },
      schemas: { status: 'pending', details: [] },
      services: { status: 'pending', details: [] },
      overall: { status: 'pending', score: 0, total: 0 }
    };
  }

  async verifyDatabaseConnection(): Promise<boolean> {
    console.log('🔍 Verifying database connection...');

    try {
      const client = new Client(expectedConfig);
      await client.connect();

      // Test basic connection
      const result = await client.query('SELECT version()');
      const version = result.rows[0].version;

      this.results.database.status = 'success';
      this.results.database.details.push(`✅ Connected to PostgreSQL: ${version.split(' ')[0]} ${version.split(' ')[1]}`);
      this.results.database.details.push(`✅ Database: ${expectedConfig.database}`);
      this.results.database.details.push(`✅ Host: ${expectedConfig.host}:${expectedConfig.port}`);

      await client.end();
      return true;

    } catch (error: any) {
      this.results.database.status = 'error';
      this.results.database.details.push(`❌ Connection failed: ${error.message}`);

      if (error.code === 'ECONNREFUSED') {
        this.results.database.details.push('💡 Check if PostgreSQL is running and accessible');
      } else if (error.code === '3D000') {
        this.results.database.details.push('💡 Database does not exist - run database migration');
      } else if (error.code === '28P01') {
        this.results.database.details.push('💡 Authentication failed - check credentials');
      }

      return false;
    }
  }

  async verifySchemas(): Promise<boolean> {
    console.log('🔍 Verifying database schemas...');

    try {
      const client = new Client(expectedConfig);
      await client.connect();

      // Check if schemas exist
      const schemaQuery = `
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name IN ('shared_data', 'member_service', 'employer_service', 'wellness_service', 'lms_service', 'command_center')
        ORDER BY schema_name
      `;

      const result = await client.query(schemaQuery);
      const existingSchemas = result.rows.map((row: any) => row.schema_name);

      const expectedSchemas = ['shared_data', 'member_service', 'employer_service', 'wellness_service', 'lms_service', 'command_center'];

      let allSchemasExist = true;

      for (const schema of expectedSchemas) {
        if (existingSchemas.includes(schema)) {
          this.results.schemas.details.push(`✅ Schema exists: ${schema}`);
        } else {
          this.results.schemas.details.push(`❌ Schema missing: ${schema}`);
          allSchemasExist = false;
        }
      }

      if (allSchemasExist) {
        this.results.schemas.status = 'success';
        this.results.schemas.details.push(`✅ All ${expectedSchemas.length} schemas are present`);
      } else {
        this.results.schemas.status = 'warning';
        this.results.schemas.details.push('⚠️  Some schemas are missing - run database migration');
      }

      await client.end();
      return allSchemasExist;

    } catch (error: any) {
      this.results.schemas.status = 'error';
      this.results.schemas.details.push(`❌ Schema verification failed: ${error.message}`);
      return false;
    }
  }

  verifyServiceConfigurations(): boolean {
    console.log('🔍 Verifying service configurations...');

    let allServicesValid = true;

    for (const service of services) {
      const serviceValid = this.verifyServiceConfiguration(service);
      if (!serviceValid) {
        allServicesValid = false;
      }
    }

    this.results.services.status = allServicesValid ? 'success' : 'warning';

    return allServicesValid;
  }

  private verifyServiceConfiguration(service: string): boolean {
    const configPath = `services/${service}/src/config.ts`;
    const envExamplePath = `services/${service}/.env.example`;

    let serviceValid = true;
    const serviceDetails: string[] = [];

    // Check config.ts file
    if (fs.existsSync(configPath)) {
      const configContent = fs.readFileSync(configPath, 'utf8');

      // Check for new database configuration
      if (configContent.includes('DB_NAME') && configContent.includes('DB_SCHEMA')) {
        serviceDetails.push(`✅ config.ts updated with single database configuration`);
      } else {
        serviceDetails.push(`❌ config.ts still uses old database configuration`);
        serviceValid = false;
      }

      // Check for old database variables
      const oldVarPattern = new RegExp(`${service.toUpperCase().replace('-', '_')}_DB`, 'g');
      if (configContent.match(oldVarPattern)) {
        serviceDetails.push(`⚠️  config.ts contains old database variables`);
      }

    } else {
      serviceDetails.push(`❌ config.ts file not found`);
      serviceValid = false;
    }

    // Check .env.example file
    if (fs.existsSync(envExamplePath)) {
      const envContent = fs.readFileSync(envExamplePath, 'utf8');

      if (envContent.includes('DB_NAME') && envContent.includes('DB_SCHEMA')) {
        serviceDetails.push(`✅ .env.example updated with single database configuration`);
      } else {
        serviceDetails.push(`❌ .env.example still uses old database configuration`);
        serviceValid = false;
      }

    } else {
      serviceDetails.push(`❌ .env.example file not found`);
      serviceValid = false;
    }

    // Add service results
    this.results.services.details.push(`\n📦 ${service.toUpperCase()} SERVICE:`);
    this.results.services.details.push(...serviceDetails.map(detail => `   ${detail}`));

    return serviceValid;
  }

  async runVerification(): Promise<boolean> {
    console.log('🚀 Aperion Health Database Configuration Verification\n');
    console.log(`📍 Target Database: ${expectedConfig.host}:${expectedConfig.port}/${expectedConfig.database}\n`);

    // Run all verifications
    const dbConnected = await this.verifyDatabaseConnection();
    console.log('');

    const schemasValid = await this.verifySchemas();
    console.log('');

    const servicesValid = this.verifyServiceConfigurations();
    console.log('');

    // Calculate overall score
    let score = 0;
    const total = 3;

    if (dbConnected) score++;
    if (schemasValid) score++;
    if (servicesValid) score++;

    this.results.overall.score = score;
    this.results.overall.total = total;

    if (score === total) {
      this.results.overall.status = 'success';
    } else if (score > 0) {
      this.results.overall.status = 'warning';
    } else {
      this.results.overall.status = 'error';
    }

    this.printResults();

    return score === total;
  }

  printResults(): void {
    console.log('📊 VERIFICATION RESULTS\n');

    // Database Connection
    console.log('🔗 Database Connection:');
    this.results.database.details.forEach(detail => console.log(`   ${detail}`));
    console.log('');

    // Schemas
    console.log('📁 Database Schemas:');
    this.results.schemas.details.forEach(detail => console.log(`   ${detail}`));
    console.log('');

    // Services
    console.log('⚙️  Service Configurations:');
    this.results.services.details.forEach(detail => console.log(detail));
    console.log('');

    // Overall Status
    const { score, total, status } = this.results.overall;
    const percentage = Math.round((score / total) * 100);

    console.log('🎯 OVERALL STATUS:');

    if (status === 'success') {
      console.log(`   🎉 ALL VERIFICATIONS PASSED! (${score}/${total} - ${percentage}%)`);
      console.log('   ✅ Database configuration is ready for production');
    } else if (status === 'warning') {
      console.log(`   ⚠️  PARTIAL SUCCESS (${score}/${total} - ${percentage}%)`);
      console.log('   🔧 Some issues need to be addressed');
    } else {
      console.log(`   ❌ VERIFICATION FAILED (${score}/${total} - ${percentage}%)`);
      console.log('   🚨 Critical issues need immediate attention');
    }

    console.log('\n📋 Next Steps:');
    if (this.results.database.status !== 'success') {
      console.log('   1. Fix database connection issues');
    }
    if (this.results.schemas.status !== 'success') {
      console.log('   2. Run database migration: cd database && npm run migrate');
    }
    if (this.results.services.status !== 'success') {
      console.log('   3. Update service configurations with new database settings');
    }
    if (status === 'success') {
      console.log('   🚀 Ready to start services: npm run dev');
    }
  }
}

// Main execution
async function main(): Promise<void> {
  const verifier = new ConfigurationVerifier();
  const success = await verifier.runVerification();

  process.exit(success ? 0 : 1);
}

// Handle command line arguments
function handleCommandLine(): void {
  if (process.argv.includes('--help')) {
    console.log('Aperion Health Database Configuration Verification');
    console.log('');
    console.log('Usage:');
    console.log('  tsx scripts/verify-database-config.ts        # Run full verification');
    console.log('  tsx scripts/verify-database-config.ts --help # Show this help');
    console.log('');
    console.log('This script verifies:');
    console.log('  - Database connection to AperionHealth-Dev');
    console.log('  - Existence of all required schemas');
    console.log('  - Service configuration files are updated');
    console.log('  - Environment variables are properly configured');
  } else {
    main().catch(error => {
      console.error('❌ Verification failed:', error);
      process.exit(1);
    });
  }
}

if (require.main === module) {
  handleCommandLine();
}

export { ConfigurationVerifier };
