import {
  CognitoIdentityProviderClient,
  AdminC<PERSON><PERSON>ser<PERSON>ommand,
  AdminSetUserPasswordCommand,
  AdminAddUserToGroupCommand,
  AdminGetUserCommand,
  AdminUpdateUserAttributesCommand,
  AdminDeleteUserCommand,
  InitiateAuthCommand,
  RespondToAuthChallengeCommand,
  ConfirmSignUpCommand,
  ResendConfirmationCodeCommand,
  ForgotPasswordCommand,
  ConfirmForgotPasswordCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { UserRole, JWTPayload, rolePermissions, roleServiceMapping } from '@aperion/shared';

export interface CognitoConfig {
  region: string;
  userPoolId: string;
  clientId: string;
  clientSecret?: string;
}

export interface CreateUserParams {
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  temporaryPassword?: string;
  sendWelcomeEmail?: boolean;
}

export interface AuthenticateParams {
  email: string;
  password: string;
}

export interface AuthResult {
  accessToken: string;
  refreshToken: string;
  idToken: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    service: string;
    permissions: string[];
    emailVerified: boolean;
  };
}

export class CognitoService {
  private client: CognitoIdentityProviderClient;
  private config: CognitoConfig;

  constructor(config: CognitoConfig) {
    this.config = config;
    this.client = new CognitoIdentityProviderClient({
      region: config.region,
    });
  }

  /**
   * Create a new user in Cognito
   */
  async createUser(params: CreateUserParams): Promise<string> {
    const { email, firstName, lastName, role, temporaryPassword, sendWelcomeEmail = true } = params;

    try {
      // Create user in Cognito
      const createUserCommand = new AdminCreateUserCommand({
        UserPoolId: this.config.userPoolId,
        Username: email,
        UserAttributes: [
          { Name: 'email', Value: email },
          { Name: 'email_verified', Value: 'true' },
          { Name: 'given_name', Value: firstName },
          { Name: 'family_name', Value: lastName },
          { Name: 'custom:role', Value: role },
          { Name: 'custom:service', Value: roleServiceMapping[role] },
          { Name: 'custom:permissions', Value: JSON.stringify(rolePermissions[role]) },
        ],
        TemporaryPassword: temporaryPassword,
        MessageAction: sendWelcomeEmail ? 'SEND' : 'SUPPRESS',
      });

      const result = await this.client.send(createUserCommand);
      const userId = result.User?.Username;

      if (!userId) {
        throw new Error('Failed to create user - no user ID returned');
      }

      // Set permanent password if provided
      if (temporaryPassword) {
        await this.setUserPassword(userId, temporaryPassword, true);
      }

      // Add user to role group
      await this.addUserToGroup(userId, this.getRoleGroup(role));

      return userId;
    } catch (error) {
      throw new Error(`Failed to create user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Authenticate user with email and password
   */
  async authenticate(params: AuthenticateParams): Promise<AuthResult> {
    const { email, password } = params;

    try {
      // Initiate authentication
      const authCommand = new InitiateAuthCommand({
        ClientId: this.config.clientId,
        AuthFlow: 'USER_PASSWORD_AUTH',
        AuthParameters: {
          USERNAME: email,
          PASSWORD: password,
          SECRET_HASH: this.config.clientSecret ? this.calculateSecretHash(email) : undefined,
        },
      });

      const authResult = await this.client.send(authCommand);

      if (!authResult.AuthenticationResult) {
        throw new Error('Authentication failed - no result returned');
      }

      const { AccessToken, RefreshToken, IdToken } = authResult.AuthenticationResult;

      if (!AccessToken || !RefreshToken || !IdToken) {
        throw new Error('Authentication failed - missing tokens');
      }

      // Get user details
      const user = await this.getUserDetails(email);

      return {
        accessToken: AccessToken,
        refreshToken: RefreshToken,
        idToken: IdToken,
        user,
      };
    } catch (error) {
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user details from Cognito
   */
  async getUserDetails(username: string): Promise<AuthResult['user']> {
    try {
      const getUserCommand = new AdminGetUserCommand({
        UserPoolId: this.config.userPoolId,
        Username: username,
      });

      const result = await this.client.send(getUserCommand);

      if (!result.UserAttributes) {
        throw new Error('User attributes not found');
      }

      const attributes = this.parseUserAttributes(result.UserAttributes);

      return {
        id: result.Username || username,
        email: attributes.email || username,
        firstName: attributes.given_name || '',
        lastName: attributes.family_name || '',
        role: (attributes['custom:role'] as UserRole) || UserRole.MEMBER,
        service: attributes['custom:service'] || 'member-service',
        permissions: attributes['custom:permissions'] ? JSON.parse(attributes['custom:permissions']) : [],
        emailVerified: attributes.email_verified === 'true',
      };
    } catch (error) {
      throw new Error(`Failed to get user details: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update user attributes
   */
  async updateUserAttributes(username: string, attributes: Record<string, string>): Promise<void> {
    try {
      const updateCommand = new AdminUpdateUserAttributesCommand({
        UserPoolId: this.config.userPoolId,
        Username: username,
        UserAttributes: Object.entries(attributes).map(([name, value]) => ({
          Name: name,
          Value: value,
        })),
      });

      await this.client.send(updateCommand);
    } catch (error) {
      throw new Error(`Failed to update user attributes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Set user password
   */
  async setUserPassword(username: string, password: string, permanent: boolean = false): Promise<void> {
    try {
      const setPasswordCommand = new AdminSetUserPasswordCommand({
        UserPoolId: this.config.userPoolId,
        Username: username,
        Password: password,
        Permanent: permanent,
      });

      await this.client.send(setPasswordCommand);
    } catch (error) {
      throw new Error(`Failed to set user password: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Add user to group
   */
  async addUserToGroup(username: string, groupName: string): Promise<void> {
    try {
      const addToGroupCommand = new AdminAddUserToGroupCommand({
        UserPoolId: this.config.userPoolId,
        Username: username,
        GroupName: groupName,
      });

      await this.client.send(addToGroupCommand);
    } catch (error) {
      throw new Error(`Failed to add user to group: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete user
   */
  async deleteUser(username: string): Promise<void> {
    try {
      const deleteCommand = new AdminDeleteUserCommand({
        UserPoolId: this.config.userPoolId,
        Username: username,
      });

      await this.client.send(deleteCommand);
    } catch (error) {
      throw new Error(`Failed to delete user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<{ accessToken: string; idToken: string }> {
    try {
      const refreshCommand = new InitiateAuthCommand({
        ClientId: this.config.clientId,
        AuthFlow: 'REFRESH_TOKEN_AUTH',
        AuthParameters: {
          REFRESH_TOKEN: refreshToken,
          SECRET_HASH: this.config.clientSecret ? this.calculateSecretHash('') : undefined,
        },
      });

      const result = await this.client.send(refreshCommand);

      if (!result.AuthenticationResult?.AccessToken || !result.AuthenticationResult?.IdToken) {
        throw new Error('Failed to refresh token');
      }

      return {
        accessToken: result.AuthenticationResult.AccessToken,
        idToken: result.AuthenticationResult.IdToken,
      };
    } catch (error) {
      throw new Error(`Failed to refresh token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Helper methods
   */
  private parseUserAttributes(attributes: Array<{ Name?: string; Value?: string }>): Record<string, string> {
    const parsed: Record<string, string> = {};
    attributes.forEach(attr => {
      if (attr.Name && attr.Value) {
        parsed[attr.Name] = attr.Value;
      }
    });
    return parsed;
  }

  private getRoleGroup(role: UserRole): string {
    const groupMapping: Record<UserRole, string> = {
      [UserRole.MEMBER]: 'members',
      [UserRole.EMPLOYER]: 'employers',
      [UserRole.HR_MANAGER]: 'hr-managers',
      [UserRole.WELLNESS_COACH]: 'wellness-coaches',
      [UserRole.WELLNESS_COORDINATOR]: 'wellness-coordinators',
      [UserRole.LEARNER]: 'learners',
      [UserRole.CONTENT_CREATOR]: 'content-creators',
      [UserRole.LMS_ADMIN]: 'lms-admins',
      [UserRole.SYSTEM_ADMIN]: 'system-admins',
      [UserRole.OPERATIONS_MANAGER]: 'operations-managers',
    };

    return groupMapping[role];
  }

  private calculateSecretHash(username: string): string {
    const crypto = require('crypto');
    const message = username + this.config.clientId;
    return crypto.createHmac('SHA256', this.config.clientSecret).update(message).digest('base64');
  }
}
