import * as winston from 'winston';
import { config } from '../command-center/src/config';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Define format for production (no colors, structured JSON)
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Define which transports the logger must use
const transports = [
  new winston.transports.Console({
    format: config.nodeEnv === 'production' ? productionFormat : format,
  }),
];

// Add file transport for production (only in production)
// Commented out to avoid TypeScript issues in development
// if (config.nodeEnv === 'production') {
//   transports.push(
//     new winston.transports.File({
//       filename: 'logs/error.log',
//       level: 'error',
//       format: productionFormat,
//     }),
//     new winston.transports.File({
//       filename: 'logs/combined.log',
//       format: productionFormat,
//     })
//   );
// }

// Create the logger
export const logger = winston.createLogger({
  level: config.logLevel,
  levels,
  format: config.nodeEnv === 'production' ? productionFormat : format,
  transports,
  exitOnError: false,
});

// If we're not in production then log to the `console` with the format:
// `${info.level}: ${info.message} JSON.stringify({ ...rest }) `
if (config.nodeEnv !== 'production') {
  logger.add(
    new winston.transports.Console({
      format: winston.format.simple(),
    })
  );
}

// Create a stream object with a 'write' function that will be used by `morgan`
export const loggerStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};
