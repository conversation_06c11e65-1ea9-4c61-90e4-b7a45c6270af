import { z } from 'zod';

// Standard API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  meta?: ApiMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  path?: string;
  method?: string;
  requestId?: string;
}

export interface ApiMeta {
  page?: number;
  limit?: number;
  total?: number;
  totalPages?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
}

// Pagination
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Common HTTP Status Codes
export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}

// Error Codes
export enum ErrorCode {
  // Authentication Errors
  INVALID_TOKEN = 'INVALID_TOKEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  MISSING_TOKEN = 'MISSING_TOKEN',
  INSUFFICIENT_PRIVILEGES = 'INSUFFICIENT_PRIVILEGES',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',

  // Validation Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  ACTIVATION_CODE_INVALID = 'ACTIVATION_CODE_INVALID',

  // Resource Errors
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',

  // Service Errors
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',

  // Business Logic Errors
  ACTIVATION_CODE_EXPIRED = 'ACTIVATION_CODE_EXPIRED',
  MEMBER_ALREADY_ACTIVATED = 'MEMBER_ALREADY_ACTIVATED',
  COACH_CAPACITY_EXCEEDED = 'COACH_CAPACITY_EXCEEDED',
  ENROLLMENT_LIMIT_EXCEEDED = 'ENROLLMENT_LIMIT_EXCEEDED',
}

// Request/Response Schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z
    .object({
      code: z.string(),
      message: z.string(),
      details: z.any().optional(),
      timestamp: z.string(),
      path: z.string().optional(),
      method: z.string().optional(),
      requestId: z.string().optional(),
    })
    .optional(),
  meta: z
    .object({
      page: z.number().optional(),
      limit: z.number().optional(),
      total: z.number().optional(),
      totalPages: z.number().optional(),
      hasNext: z.boolean().optional(),
      hasPrev: z.boolean().optional(),
    })
    .optional(),
});

// Health Check Response
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  service: string;
  version: string;
  uptime: number;
  dependencies?: HealthCheckDependency[];
}

export interface HealthCheckDependency {
  name: string;
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  error?: string;
}

export const healthCheckSchema = z.object({
  status: z.enum(['healthy', 'unhealthy']),
  timestamp: z.string(),
  service: z.string(),
  version: z.string(),
  uptime: z.number(),
  dependencies: z
    .array(
      z.object({
        name: z.string(),
        status: z.enum(['healthy', 'unhealthy']),
        responseTime: z.number().optional(),
        error: z.string().optional(),
      })
    )
    .optional(),
});

// Service Communication Types
export interface ServiceRequest<T = any> {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  headers?: Record<string, string>;
  data?: T;
  timeout?: number;
}

export interface ServiceResponse<T = any> {
  status: number;
  data: T;
  headers: Record<string, string>;
}

// Event Types for Inter-Service Communication
export interface ServiceEvent<T = any> {
  id: string;
  eventType: string;
  sourceService: string;
  targetService?: string;
  payload: T;
  timestamp: string;
  correlationId?: string;
  retryCount?: number;
}

export const serviceEventSchema = z.object({
  id: z.string().uuid(),
  eventType: z.string(),
  sourceService: z.string(),
  targetService: z.string().optional(),
  payload: z.any(),
  timestamp: z.string(),
  correlationId: z.string().optional(),
  retryCount: z.number().default(0),
});

// Common Event Types
export enum EventType {
  // User Events
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  USER_ACTIVATED = 'USER_ACTIVATED',

  // Member Events
  MEMBER_CREATED = 'MEMBER_CREATED',
  MEMBER_ACTIVATED = 'MEMBER_ACTIVATED',
  MEMBER_PROFILE_UPDATED = 'MEMBER_PROFILE_UPDATED',

  // Employer Events
  EMPLOYER_CREATED = 'EMPLOYER_CREATED',
  EMPLOYEE_INVITED = 'EMPLOYEE_INVITED',
  ACTIVATION_CODE_GENERATED = 'ACTIVATION_CODE_GENERATED',

  // Wellness Events
  COACH_ASSIGNED = 'COACH_ASSIGNED',
  SESSION_SCHEDULED = 'SESSION_SCHEDULED',
  WELLNESS_GOAL_ACHIEVED = 'WELLNESS_GOAL_ACHIEVED',

  // LMS Events
  COURSE_ENROLLED = 'COURSE_ENROLLED',
  COURSE_COMPLETED = 'COURSE_COMPLETED',
  ASSESSMENT_COMPLETED = 'ASSESSMENT_COMPLETED',

  // System Events
  SERVICE_HEALTH_CHECK = 'SERVICE_HEALTH_CHECK',
  SYSTEM_ALERT = 'SYSTEM_ALERT',
}

// Rate Limiting
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

export const rateLimitSchema = z.object({
  limit: z.number(),
  remaining: z.number(),
  reset: z.number(),
  retryAfter: z.number().optional(),
});

// File Upload Types
export interface FileUpload {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
}

export const fileUploadSchema = z.object({
  fieldname: z.string(),
  originalname: z.string(),
  encoding: z.string(),
  mimetype: z.string(),
  size: z.number(),
  destination: z.string(),
  filename: z.string(),
  path: z.string(),
});

// Search and Filter Types
export interface SearchParams {
  query?: string;
  filters?: Record<string, any>;
  pagination?: PaginationParams;
}

export const searchParamsSchema = z.object({
  query: z.string().optional(),
  filters: z.record(z.any()).optional(),
  pagination: paginationSchema.optional(),
});

// Audit Log Types
export interface AuditLog {
  id: string;
  userId?: string;
  userType?: string;
  action: string;
  resourceType?: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
}

export const auditLogSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().optional(),
  userType: z.string().optional(),
  action: z.string(),
  resourceType: z.string().optional(),
  resourceId: z.string().optional(),
  details: z.any().optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  timestamp: z.string(),
});
