import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import { LoggerFactory } from '../utils/pinoLogger.js';

// Create a simple logger for the shared middleware
const logger = LoggerFactory.createLogger({ serviceName: 'api-gateway' });

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1,
  },
  fileFilter: (_req, file, cb) => {
    // Accept only image files
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Only JPEG, PNG, and WebP images are allowed'));
    }
  },
});

const uploadMiddleware = upload.single('photo');

/**
 * Enhanced upload middleware with error handling
 */
export const handleFileUpload = (req: Request, res: Response, next: NextFunction): void => {
  const requestId = (req as any).requestId || 'unknown';
  
  uploadMiddleware(req, res, (err: any): void => {
    if (err) {
      logger.error('File upload middleware error', {
        requestId,
        error: err.message,
        code: err.code,
      });

      // Handle multer-specific errors
      if (err instanceof multer.MulterError) {
        switch (err.code) {
          case 'LIMIT_FILE_SIZE':
            res.status(400).json({
              success: false,
              error: {
                code: 'FILE_TOO_LARGE',
                message: 'File size exceeds limit of 5MB',
                timestamp: new Date().toISOString(),
                requestId,
              },
            });
            return;
          
          case 'LIMIT_FILE_COUNT':
            res.status(400).json({
              success: false,
              error: {
                code: 'TOO_MANY_FILES',
                message: 'Only one file is allowed',
                timestamp: new Date().toISOString(),
                requestId,
              },
            });
            return;
          
          case 'LIMIT_UNEXPECTED_FILE':
            res.status(400).json({
              success: false,
              error: {
                code: 'UNEXPECTED_FIELD',
                message: 'File must be uploaded in "photo" field',
                timestamp: new Date().toISOString(),
                requestId,
              },
            });
            return;
          
          default:
            res.status(400).json({
              success: false,
              error: {
                code: 'UPLOAD_ERROR',
                message: err.message,
                timestamp: new Date().toISOString(),
                requestId,
              },
            });
            return;
        }
      }

      // Handle custom validation errors
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: err.message,
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Check if file was uploaded
    if (!req.file) {
      logger.warn('No file uploaded', { requestId });
      res.status(400).json({
        success: false,
        error: {
          code: 'NO_FILE',
          message: 'No file uploaded. Please select a photo to upload.',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    logger.info('File upload successful', {
      requestId,
      filename: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype,
    });

    next();
  });
};
