import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { ApiResponse, ApiError, HttpStatus, ErrorCode } from '@aperion/shared';
import {
  RegistrationService,
  RegistrationData,
  ServiceError
} from '../services/registrationService';

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
    }
  }
}

/**
 * Register a new member/employee
 */
export const registerMember = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const registrationData: RegistrationData = req.body;

    logger.info('Member registration attempt', {
      requestId,
      email: registrationData.email,
      firstName: registrationData.firstName,
      lastName: registrationData.lastName,
      hasPhone: !!registrationData.phone,
      hasAddress: !!registrationData.streetAddress,
      ip: req.ip,
    });

    // Validate registration data
    const validation = RegistrationService.validateRegistrationData(registrationData);
    if (!validation.isValid) {
      logger.warn('Registration validation failed', {
        requestId,
        email: registrationData.email,
        errors: validation.errors,
      });

      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        'Registration data validation failed',
        { errors: validation.errors }
      );
    }

    // Call the service layer
    const result = await RegistrationService.registerMember(registrationData, requestId);

    // Prepare response (exclude sensitive data)
    const responseData = {
      member: {
        id: result.member.id,
        email: result.member.email,
        firstName: result.member.first_name,
        lastName: result.member.last_name,
        phone: result.member.phone,
        status: result.member.status,
        createdAt: result.member.created_at,
      },
      message: result.message,
    };

    const response: ApiResponse<typeof responseData> = {
      success: true,
      data: responseData,
    };

    logger.info('Member registration successful', {
      requestId,
      memberId: result.member.id,
      email: registrationData.email,
    });

    res.status(HttpStatus.CREATED).json(response);

  } catch (error) {
    // Handle ServiceError with proper error codes and status mapping
    if (error instanceof ServiceError) {
      let httpStatus: HttpStatus;

      // Map service error codes to HTTP status codes
      switch (error.code) {
        case ErrorCode.VALIDATION_ERROR:
        case ErrorCode.INVALID_INPUT:
        case ErrorCode.MISSING_REQUIRED_FIELD:
          httpStatus = HttpStatus.BAD_REQUEST;
          break;
        case ErrorCode.RESOURCE_NOT_FOUND:
          httpStatus = HttpStatus.NOT_FOUND;
          break;
        case ErrorCode.RESOURCE_ALREADY_EXISTS:
        case ErrorCode.RESOURCE_CONFLICT:
          httpStatus = HttpStatus.CONFLICT;
          break;
        case ErrorCode.INSUFFICIENT_PRIVILEGES:
          httpStatus = HttpStatus.FORBIDDEN;
          break;
        case ErrorCode.DATABASE_ERROR:
        case ErrorCode.SERVICE_UNAVAILABLE:
          httpStatus = HttpStatus.SERVICE_UNAVAILABLE;
          break;
        case ErrorCode.INTERNAL_ERROR:
        default:
          httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
          break;
      }

      logger.warn('Registration service error occurred', {
        requestId,
        errorCode: error.code,
        message: error.message,
        details: error.details,
        email: req.body?.email,
      });

      const apiError: ApiError = {
        code: error.code,
        message: error.message,
        details: error.details,
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(httpStatus).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    // Handle unexpected errors
    logger.error('Unexpected registration error occurred', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      email: req.body?.email,
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Registration failed due to an unexpected error',
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Validate registration data endpoint (for frontend validation)
 */
export const validateRegistrationData = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const registrationData: RegistrationData = req.body;

    logger.info('Registration data validation request', {
      requestId,
      email: registrationData.email,
    });

    // Validate registration data
    const validation = RegistrationService.validateRegistrationData(registrationData);

    const response: ApiResponse<{ isValid: boolean; errors: string[] }> = {
      success: true,
      data: validation,
    };

    res.status(HttpStatus.OK).json(response);

  } catch (error) {
    logger.error('Registration validation error', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Validation failed due to an unexpected error',
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Check email availability
 */
export const checkEmailAvailability = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const { email } = req.query;

    if (!email || typeof email !== 'string') {
      throw new ServiceError(
        ErrorCode.MISSING_REQUIRED_FIELD,
        'Email parameter is required',
        { field: 'email' }
      );
    }

    logger.info('Email availability check', {
      requestId,
      email,
    });

    // This would typically check against the database
    // For now, we'll do a simple validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValidFormat = emailRegex.test(email);

    if (!isValidFormat) {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        'Invalid email format',
        { field: 'email' }
      );
    }

    // In a real implementation, you'd check the database here
    const response: ApiResponse<{ available: boolean; email: string }> = {
      success: true,
      data: {
        available: true, // Simplified for demo
        email,
      },
    };

    res.status(HttpStatus.OK).json(response);

  } catch (error) {
    if (error instanceof ServiceError) {
      const apiError: ApiError = {
        code: error.code,
        message: error.message,
        details: error.details,
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    logger.error('Email availability check error', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Email availability check failed',
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Create mock activation code for testing
 */
export const createMockActivationCode = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const { email, firstName, lastName } = req.body;

    if (!email) {
      throw new ServiceError(
        ErrorCode.MISSING_REQUIRED_FIELD,
        'Email is required',
        { field: 'email' }
      );
    }

    logger.info('Creating mock activation code', {
      requestId,
      email,
      firstName,
      lastName,
    });

    const result = await RegistrationService.createMockActivationCode(
      email,
      firstName,
      lastName,
      requestId
    );

    const response: ApiResponse<{ activationCode: string; invitationId: string; registrationUrl: string }> = {
      success: true,
      data: {
        ...result,
        registrationUrl: `/auth/register?code=${result.activationCode}&email=${encodeURIComponent(email)}`
      },
    };

    res.status(HttpStatus.CREATED).json(response);
  } catch (error) {
    if (error instanceof ServiceError) {
      const apiError: ApiError = {
        code: error.code,
        message: error.message,
        details: error.details,
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      const statusCode = error.code === ErrorCode.MISSING_REQUIRED_FIELD ? HttpStatus.BAD_REQUEST : HttpStatus.INTERNAL_SERVER_ERROR;
      res.status(statusCode).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    logger.error('Mock activation code creation failed', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to create mock activation code',
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Validate activation code
 */
export const validateActivationCode = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const { activationCode } = req.query;

    if (!activationCode || typeof activationCode !== 'string') {
      throw new ServiceError(
        ErrorCode.MISSING_REQUIRED_FIELD,
        'Activation code is required',
        { field: 'activationCode' }
      );
    }

    logger.info('Validating activation code', {
      requestId,
      activationCode,
    });

    const invitation = await RegistrationService.validateActivationCode(activationCode, requestId);

    const response: ApiResponse<{
      valid: boolean;
      invitation: {
        email: string;
        firstName?: string;
        lastName?: string;
        department?: string;
        jobTitle?: string;
        employerId: string;
      }
    }> = {
      success: true,
      data: {
        valid: true,
        invitation: {
          email: invitation.email,
          ...(invitation.first_name && { firstName: invitation.first_name }),
          ...(invitation.last_name && { lastName: invitation.last_name }),
          ...(invitation.department && { department: invitation.department }),
          ...(invitation.job_title && { jobTitle: invitation.job_title }),
          employerId: invitation.employer_id
        }
      },
    };

    res.status(HttpStatus.OK).json(response);
  } catch (error) {
    if (error instanceof ServiceError) {
      const apiError: ApiError = {
        code: error.code,
        message: error.message,
        details: error.details,
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      const statusCode = error.code === ErrorCode.MISSING_REQUIRED_FIELD ? HttpStatus.BAD_REQUEST :
                        error.code === ErrorCode.RESOURCE_NOT_FOUND ? HttpStatus.NOT_FOUND :
                        error.code === ErrorCode.VALIDATION_ERROR ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;
      res.status(statusCode).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    logger.error('Activation code validation failed', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to validate activation code',
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};
