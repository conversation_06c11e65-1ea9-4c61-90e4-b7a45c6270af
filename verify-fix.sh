#!/bin/bash

# Aperion Health - Fix Verification Script
# Verifies that the UserRole import fix is working correctly

set -e

echo "🔍 Aperion Health - UserRole Fix Verification"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_check() {
    echo -e "${BLUE}🔍 Checking: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "packages/shared" ]; then
    print_error "Please run this script from the Aperion Health project root directory"
    exit 1
fi

echo "Running comprehensive verification..."
echo ""

# 1. Check Node.js version
print_check "Node.js version compatibility"
NODE_VERSION=$(node --version)
echo "Node.js version: $NODE_VERSION"
if [[ "$NODE_VERSION" < "v16" ]]; then
    print_warning "Node.js 16+ recommended for best ES modules support"
else
    print_success "Node.js version is compatible"
fi
echo ""

# 2. Check shared package structure
print_check "Shared package dual-format build structure"
if [ -d "packages/shared/dist/cjs" ] && [ -d "packages/shared/dist/esm" ] && [ -d "packages/shared/dist/types" ]; then
    print_success "Dual-format build directories exist"
    
    # Check specific files
    if [ -f "packages/shared/dist/cjs/index.js" ] && [ -f "packages/shared/dist/esm/index.js" ] && [ -f "packages/shared/dist/types/index.d.ts" ]; then
        print_success "All required build files exist"
    else
        print_error "Some build files are missing"
        echo "Expected files:"
        echo "  - packages/shared/dist/cjs/index.js"
        echo "  - packages/shared/dist/esm/index.js"
        echo "  - packages/shared/dist/types/index.d.ts"
        exit 1
    fi
else
    print_error "Shared package build structure is incorrect"
    echo "Expected directories:"
    echo "  - packages/shared/dist/cjs/"
    echo "  - packages/shared/dist/esm/"
    echo "  - packages/shared/dist/types/"
    exit 1
fi
echo ""

# 3. Check CommonJS format
print_check "CommonJS format (for backend services)"
CJS_CONTENT=$(head -5 packages/shared/dist/cjs/index.js)
if echo "$CJS_CONTENT" | grep -q "use strict" && echo "$CJS_CONTENT" | grep -q "exports"; then
    print_success "CommonJS format is correct"
    echo "Sample CJS content:"
    echo "$CJS_CONTENT" | head -2
else
    print_error "CommonJS format is incorrect"
    echo "Expected CommonJS format with 'use strict' and 'exports'"
    echo "Actual content:"
    echo "$CJS_CONTENT"
    exit 1
fi
echo ""

# 4. Check ES modules format
print_check "ES modules format (for frontend)"
ESM_CONTENT=$(cat packages/shared/dist/esm/index.js)
if echo "$ESM_CONTENT" | grep -q "export \*" && ! echo "$ESM_CONTENT" | grep -q "require"; then
    print_success "ES modules format is correct"
    echo "ESM content:"
    echo "$ESM_CONTENT"
else
    print_error "ES modules format is incorrect"
    echo "Expected ES modules format with 'export' statements, no 'require'"
    echo "Actual content:"
    echo "$ESM_CONTENT"
    exit 1
fi
echo ""

# 5. Check UserRole exports
print_check "UserRole export in both formats"
if [ -f "packages/shared/dist/cjs/types/user.js" ] && [ -f "packages/shared/dist/esm/types/user.js" ]; then
    if grep -q "UserRole" packages/shared/dist/cjs/types/user.js && grep -q "UserRole" packages/shared/dist/esm/types/user.js; then
        print_success "UserRole exported in both CJS and ESM formats"
    else
        print_error "UserRole export missing in one or both formats"
        exit 1
    fi
else
    print_error "User types files missing"
    exit 1
fi
echo ""

# 6. Check TypeScript declarations
print_check "TypeScript declarations"
TYPES_CONTENT=$(cat packages/shared/dist/types/index.d.ts)
if echo "$TYPES_CONTENT" | grep -q "export \* from './types/user'"; then
    print_success "TypeScript declarations are correct"
    echo "Types content:"
    echo "$TYPES_CONTENT"
else
    print_error "TypeScript declarations are incorrect"
    echo "Expected export from './types/user'"
    echo "Actual content:"
    echo "$TYPES_CONTENT"
    exit 1
fi
echo ""

# 7. Check package.json configuration
print_check "Package.json dual-format configuration"
PACKAGE_JSON="packages/shared/package.json"
if grep -q '"main": "dist/cjs/index.js"' "$PACKAGE_JSON" && \
   grep -q '"module": "dist/esm/index.js"' "$PACKAGE_JSON" && \
   grep -q '"types": "dist/types/index.d.ts"' "$PACKAGE_JSON"; then
    print_success "Package.json dual-format configuration is correct"
else
    print_error "Package.json configuration is incorrect"
    echo "Expected:"
    echo '  "main": "dist/cjs/index.js"'
    echo '  "module": "dist/esm/index.js"'
    echo '  "types": "dist/types/index.d.ts"'
    exit 1
fi
echo ""

# 8. Check environment configuration
print_check "Environment configuration"
if [ -f ".env" ]; then
    if grep -q "CORS_ORIGINS" .env; then
        CORS_CONFIG=$(grep "CORS_ORIGINS" .env)
        echo "CORS configuration: $CORS_CONFIG"
        if echo "$CORS_CONFIG" | grep -q "4001"; then
            print_success "CORS includes port 4001"
        else
            print_warning "CORS may not include port 4001 - add it if frontend runs on 4001"
        fi
    else
        print_warning "CORS_ORIGINS not found in .env"
    fi
else
    print_warning ".env file not found"
fi
echo ""

# 9. Check if services can import the package
print_check "Import compatibility test"
cd packages/shared
if npm run build >/dev/null 2>&1; then
    print_success "Shared package builds without errors"
else
    print_error "Shared package build has errors"
    exit 1
fi
cd ../..
echo ""

# 10. Generate summary report
echo "📊 VERIFICATION SUMMARY"
echo "======================="
echo ""
print_success "✅ All checks passed!"
echo ""
echo "📋 What was verified:"
echo "  ✅ Node.js version compatibility"
echo "  ✅ Dual-format build structure"
echo "  ✅ CommonJS format for backend"
echo "  ✅ ES modules format for frontend"
echo "  ✅ UserRole exports in both formats"
echo "  ✅ TypeScript declarations"
echo "  ✅ Package.json configuration"
echo "  ✅ Environment setup"
echo "  ✅ Build process"
echo ""
echo "🎉 The UserRole import fix is properly configured!"
echo ""
echo "Next steps:"
echo "1. Start services: ./start-services.sh"
echo "2. Test the application in browser"
echo "3. Verify no import errors in browser console"
echo ""
echo "If you encounter any issues:"
echo "1. Run: ./setup-aperion-fix.sh (to rebuild)"
echo "2. Check browser console for errors"
echo "3. Check service logs for backend errors"
