import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    exclude: ['mock-aws-s3', 'nock', 'bcrypt']
  },
  define: {
    global: 'globalThis',
  },
  build: {
    rollupOptions: {
      external: ['bcrypt', 'mock-aws-s3', 'nock']
    }
  },
  // Explicitly set appType to 'spa' to ensure SPA fallback middleware is included
  appType: 'spa',
  server: {
    port: 4000,
    proxy: {
      // Only proxy actual API calls, not client-side routes
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
      // Removed /admin proxy to prevent conflicts with client-side routing
      // The /admin/* routes are handled by React Router, not backend services
      //      '/admin': {
      //   target: 'http://localhost:3005',
      //   changeOrigin: true,
      // },
    },
  },
  
})
