import { Request, Response, NextFunction } from 'express';

/**
 * Async handler wrapper to catch async errors and pass them to error middleware
 * This eliminates the need for try-catch blocks in every async route handler
 * 
 * @param fn - The async route handler function
 * @returns Express middleware function that handles async errors
 * 
 * @example
 * ```typescript
 * import { asyncHandler } from '@aperion/shared';
 * 
 * router.get('/users', asyncHandler(async (req, res) => {
 *   const users = await UserService.getAll();
 *   res.json(users);
 * }));
 * ```
 */
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<void>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

export default asyncHandler;
