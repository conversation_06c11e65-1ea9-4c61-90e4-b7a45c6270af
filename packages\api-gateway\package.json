{"name": "@aperion/api-gateway", "version": "1.0.0", "description": "API Gateway for Aperion Health microservices", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@aperion/shared": "^1.0.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.2", "winston": "^3.11.0", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-validator": "^7.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "typescript": "^5.1.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "rimraf": "^5.0.5"}}