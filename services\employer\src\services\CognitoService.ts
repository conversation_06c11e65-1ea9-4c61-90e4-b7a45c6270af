import { CognitoIdentityProviderClient, SignUpCommand } from '@aws-sdk/client-cognito-identity-provider';
import { createHmac } from 'crypto';
import { logger } from '../utils/logger';

// Initialize Cognito client (same as Command Center)
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// Generate secret hash for Cognito (same as Command Center)
const secretHash = (username: string): string => {
  return createHmac('SHA256', process.env.CLIENT_SECRET!)
    .update(username + process.env.CLIENT_ID!)
    .digest('base64');
};

// Generate random password (same as Command Center)
const generateRandomPassword = (): string => {
  const length = 12;
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';

  // Ensure at least one of each required character type
  password += 'A'; // uppercase
  password += 'a'; // lowercase
  password += '1'; // number
  password += '!'; // special char

  // Fill remaining length with random characters
  for (let i = 4; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  // Shuffle the password
  return password.split('').sort(() => 0.5 - Math.random()).join('');
};

/**
 * Sign up user in Cognito (EXACT copy from Command Center)
 * Uses phone number as username and sets role ID to 3
 */
export const signUpUser = async ({
  phoneNumber,
  email,
  roleId,
}: {
  phoneNumber: string;
  email: string;
  roleId: number;
}): Promise<{ userSub: string; password: string }> => {
  try {
    const username = phoneNumber;
    const password = generateRandomPassword();
    const secretHashUser = secretHash(username);

    const command = new SignUpCommand({
      ClientId: process.env.CLIENT_ID!,
      SecretHash: secretHashUser,
      Password: password,
      Username: username,
      UserAttributes: [
        { Name: 'phone_number', Value: phoneNumber }, // Using phone_number attribute
        { Name: 'email', Value: email },
        { Name: 'custom:roleId', Value: String(roleId) },
      ],
    });

    const response = await cognitoClient.send(command);
    return { userSub: response.UserSub!, password };
  } catch (error: any) {
    logger.error('Error in signUpUser:', error);
    throw new Error(`Failed to sign up user: ${error.message}`);
  }
};
