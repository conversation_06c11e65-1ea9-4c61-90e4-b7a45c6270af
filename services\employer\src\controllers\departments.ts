import { Request, Response } from 'express';
import { z } from 'zod';
import { getServiceDatabaseConfig, DatabaseConnection } from '@aperion/shared/server';
import { logger } from '../utils/logger';
import { ApiResponse, ApiError, HttpStatus, ErrorCode } from '@aperion/shared';

// Initialize database connection using shared configuration
const dbConfig = getServiceDatabaseConfig('employer');
const db = new DatabaseConnection(dbConfig);

/**
 * Department interface based on database schema
 */
interface Department {
  id: string;
  employer_id: string;
  name: string;
  description: string | null;
  manager_email: string | null;
  budget: number | null;
  member_count: number;
  status: 'active' | 'inactive';
  created_at: Date;
  updated_at: Date;
}

/**
 * Query parameters validation schema
 */
const getDepartmentsQuerySchema = z.object({
  status: z.enum(['active', 'inactive']).optional(),
  page: z.string().transform(Number).pipe(z.number().min(1)).optional().default('1'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional().default('10'),
  sortBy: z.enum(['name', 'created_at', 'member_count', 'budget']).optional().default('name'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
});

/**
 * Get departments for a specific employer
 */
export const getDepartmentsByEmployer = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    // Get employer ID from authenticated user
    const employerId = req.user?.employerId;

    if (!employerId) {
      const error: ApiError = {
        code: ErrorCode.AUTHENTICATION_FAILED,
        message: 'Employer ID not found in token',
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      logger.warn('Unauthorized access attempt - no employer ID', { requestId });

      res.status(HttpStatus.UNAUTHORIZED).json({
        success: false,
        error,
      } as ApiResponse);
      return;
    }

    // Validate query parameters
    const queryValidation = getDepartmentsQuerySchema.safeParse(req.query);

    if (!queryValidation.success) {
      const error: ApiError = {
        code: ErrorCode.VALIDATION_ERROR,
        message: 'Invalid query parameters',
        details: queryValidation.error.errors,
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      logger.warn('Invalid query parameters', { requestId, errors: queryValidation.error.errors });

      res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        error,
      } as ApiResponse);
      return;
    }

    const { status, page, limit, sortBy, sortOrder } = queryValidation.data;
    const offset = (page - 1) * limit;

    // Build the query
    let whereClause = 'WHERE employer_id = $1';
    const queryParams: any[] = [employerId];
    let paramIndex = 2;

    if (status) {
      whereClause += ` AND status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    const orderClause = `ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
    const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    // Execute the main query
    const query = `
      SELECT
        id,
        employer_id,
        name,
        description,
        manager_email,
        budget,
        member_count,
        status,
        created_at,
        updated_at
      FROM company_departments
      ${whereClause}
      ${orderClause}
      ${limitClause}
    `;

    const result = await db.query(query, queryParams);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM company_departments
      ${whereClause}
    `;
    const countResult = await db.query(countQuery, queryParams.slice(0, -2)); // Remove limit and offset
    const total = parseInt(countResult.rows[0].total, 10);

    const departments: Department[] = result.rows;
    const totalPages = Math.ceil(total / limit);

    logger.info('Departments retrieved successfully', {
      requestId,
      employerId,
      count: departments.length,
      total,
      page,
      limit,
    });

    const response: ApiResponse<Department[]> = {
      success: true,
      data: departments,
      meta: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };

    res.status(HttpStatus.OK).json(response);

  } catch (error) {
    logger.error('Failed to retrieve departments', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to retrieve departments',
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Get department statistics for an employer
 */
export const getDepartmentStats = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const employerId = req.user?.employerId;

    if (!employerId) {
      const error: ApiError = {
        code: ErrorCode.AUTHENTICATION_FAILED,
        message: 'Employer ID not found in token',
        timestamp: new Date().toISOString(),
        requestId,
      };

      res.status(HttpStatus.UNAUTHORIZED).json({
        success: false,
        error,
      } as ApiResponse);
      return;
    }

    const statsQuery = `
      SELECT
        COUNT(*) as total_departments,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_departments,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_departments,
        SUM(member_count) as total_members,
        SUM(budget) as total_budget,
        AVG(member_count) as avg_members_per_department
      FROM company_departments
      WHERE employer_id = $1
    `;

    const result = await db.query(statsQuery, [employerId]);
    const stats = result.rows[0];

    // Convert string numbers to proper types
    const departmentStats = {
      totalDepartments: parseInt(stats.total_departments, 10),
      activeDepartments: parseInt(stats.active_departments, 10),
      inactiveDepartments: parseInt(stats.inactive_departments, 10),
      totalMembers: parseInt(stats.total_members || '0', 10),
      totalBudget: parseFloat(stats.total_budget || '0'),
      avgMembersPerDepartment: parseFloat(stats.avg_members_per_department || '0'),
    };

    logger.info('Department statistics retrieved', {
      requestId,
      employerId,
      stats: departmentStats,
    });

    res.status(HttpStatus.OK).json({
      success: true,
      data: departmentStats,
    } as ApiResponse);

  } catch (error) {
    logger.error('Failed to retrieve department statistics', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to retrieve department statistics',
      timestamp: new Date().toISOString(),
      requestId,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};
