import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { config } from './config';
import { logger, httpLogger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';
import { authMiddleware } from './middleware/auth';
import { healthRouter } from './routes/health';
import { registrationRouter } from './routes/registration';
import { profileRouter } from './routes/profile';
import { profilePhotoRouter } from './routes/profilePhoto';

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: config.corsOrigins,
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Body parsing
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging with Pino
app.use(requestLogger); // Custom request ID and correlation ID middleware
app.use(httpLogger); // Custom HTTP logger for access.log

// Health check (no auth required)
app.use('/health', healthRouter);

// Registration routes (no auth required for registration)
app.use('/api/member', registrationRouter);

// Protected routes
app.use('/api/member', authMiddleware);
app.use('/', authMiddleware); // For API Gateway proxied requests

// Profile routes (protected)
app.use('/api/member/profile', profileRouter); // Direct access
app.use('/profile', profileRouter); // API Gateway proxied access

// Profile photo routes (protected)
app.use('/api/member/profile/photo', profilePhotoRouter); // Direct access
app.use('/profile/photo', profilePhotoRouter); // API Gateway proxied access

// Error handling
app.use(errorHandler);

const server = app.listen(config.port, () => {
  logger.info(`${config.serviceName} started on port ${config.port}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;
