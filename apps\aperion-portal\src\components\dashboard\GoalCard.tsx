import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { WellnessGoal } from '@/lib/types';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { queryClient } from '@/lib/queryClient';

// Helper functions for colorful icons
const getIconColor = (category: string): string => {
  switch (category?.toLowerCase()) {
    case 'activity':
      return 'hsl(var(--wellness-green))';
    case 'nutrition':
      return 'hsl(var(--wellness-orange))';
    case 'sleep':
      return 'hsl(var(--wellness-purple))';
    case 'mental':
      return 'hsl(var(--wellness-blue))';
    case 'medical':
      return 'hsl(var(--wellness-magenta))';
    default:
      return 'hsl(var(--wellness-teal))';
  }
};

const getIconBgColor = (category: string): string => {
  switch (category?.toLowerCase()) {
    case 'activity':
      return 'hsl(var(--wellness-green) / 15%)';
    case 'nutrition':
      return 'hsl(var(--wellness-orange) / 15%)';
    case 'sleep':
      return 'hsl(var(--wellness-purple) / 15%)';
    case 'mental':
      return 'hsl(var(--wellness-blue) / 15%)';
    case 'medical':
      return 'hsl(var(--wellness-magenta) / 15%)';
    default:
      return 'hsl(var(--wellness-teal) / 15%)';
  }
};

const getIconShadowColor = (category: string): string => {
  switch (category?.toLowerCase()) {
    case 'activity':
      return 'hsl(var(--wellness-green) / 30%)';
    case 'nutrition':
      return 'hsl(var(--wellness-orange) / 30%)';
    case 'sleep':
      return 'hsl(var(--wellness-purple) / 30%)';
    case 'mental':
      return 'hsl(var(--wellness-blue) / 30%)';
    case 'medical':
      return 'hsl(var(--wellness-magenta) / 30%)';
    default:
      return 'hsl(var(--wellness-teal) / 30%)';
  }
};

interface GoalCardProps {
  goal: WellnessGoal;
}

const GoalCard: React.FC<GoalCardProps> = ({ goal }) => {
  const { toast } = useToast();
  
  // Calculate progress percentage
  const progress = Math.min(Math.round((goal.currentValue / goal.targetValue) * 100), 100);
  
  // Format the progress text based on the unit
  const progressText = `${goal.currentValue}/${goal.targetValue} ${goal.unit}`;

  // Format the last updated time
  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Handle goal update button click
  const handleUpdate = async () => {
    try {
      // Simple case: increment the current value by 1
      const newValue = goal.currentValue + 1;
      
      await apiRequest('PATCH', `/api/wellness-goals/${goal.id}`, {
        currentValue: newValue,
        isCompleted: newValue >= goal.targetValue
      });
      
      // Invalidate goals cache to trigger refetch
      queryClient.invalidateQueries({ queryKey: ['/api/wellness-goals'] });
      
      toast({
        title: 'Goal updated',
        description: 'Your progress has been updated successfully.',
        variant: 'default'
      });
    } catch (error) {
      toast({
        title: 'Failed to update goal',
        description: 'Please try again later.',
        variant: 'destructive'
      });
    }
  };

  return (
    <Card className="bg-white rounded-lg shadow-sm">
      <CardContent className="p-5">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-medium">{goal.title}</h3>
          <span 
            className="material-icons flex items-center justify-center w-8 h-8 rounded-full"
            style={{ 
              color: getIconColor(goal.category),
              background: getIconBgColor(goal.category),
              filter: `drop-shadow(0 2px 3px ${getIconShadowColor(goal.category)})`,
              transition: 'all 0.3s ease'
            }}
          >
            {goal.icon || 'favorite'}
          </span>
        </div>
        
        <p className="text-sm text-neutral-600 mb-3">{goal.description}</p>
        
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-neutral-500">Progress</span>
            <span className="text-xs font-medium">{progressText}</span>
          </div>
          <div className="w-full bg-neutral-200 rounded-full h-2">
            <div 
              className="bg-secondary h-2 rounded-full" 
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-xs text-neutral-500">Updated {getTimeAgo(goal.lastUpdated)}</span>
          <button 
            onClick={handleUpdate}
            className="text-sm text-primary hover:text-primary-dark"
            disabled={goal.isCompleted}
          >
            {goal.isCompleted ? 'Completed' : 'Update'}
          </button>
        </div>
      </CardContent>
    </Card>
  );
};

export default GoalCard;
