import { useToast } from "@/hooks/use-toast"
import { X } from "lucide-react"
import { Button } from "@/components/ui/button"

export function Toaster() {
  const { toasts, dismiss } = useToast()

  return (
    <div className="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px] pointer-events-none">
      {toasts.map(function ({ id, title, description, action, variant, open, ...props }) {
        return (
          <div
            key={id}
            data-state={open ? "open" : "closed"}
            className={`group pointer-events-auto relative flex w-full items-start justify-between space-x-4 overflow-hidden rounded-lg border p-4 pr-12 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full ${
              variant === 'destructive'
                ? 'bg-red-50 border-red-200 text-red-900 dark:bg-red-900/20 dark:border-red-800 dark:text-red-100'
                : 'bg-white border-slate-200 text-slate-900 dark:bg-slate-800 dark:border-slate-700 dark:text-slate-100'
            }`}
            {...props}
          >
            <div className="grid gap-1 flex-1">
              {title && (
                <div className={`text-sm font-semibold ${
                  variant === 'destructive' ? 'text-red-900 dark:text-red-100' : 'text-slate-900 dark:text-slate-100'
                }`}>
                  {title}
                </div>
              )}
              {description && (
                <div className={`text-sm ${
                  variant === 'destructive'
                    ? 'text-red-700 dark:text-red-200'
                    : 'text-slate-600 dark:text-slate-300'
                }`}>
                  {description}
                </div>
              )}
            </div>

            {/* Close Button */}
            <Button
              variant="ghost"
              className={`absolute top-2 right-2 h-6 w-6 p-0 rounded-full opacity-70 hover:opacity-100 transition-opacity cursor-pointer ${
                variant === 'destructive'
                  ? 'text-red-600 hover:text-red-700 hover:bg-red-100 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30'
                  : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-slate-200 dark:hover:bg-slate-700'
              }`}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                dismiss(id);
              }}
              type="button"
            >
              <X className="h-3 w-3" />
            </Button>

            {action}
          </div>
        )
      })}
    </div>
  )
}
