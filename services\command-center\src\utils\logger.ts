import { LoggerFactory, LoggingUtils, LogContext, ContextualLogger } from '@aperion/shared/server';

// Logger configuration for command-center service
const loggerConfig = {
  serviceName: 'command-center' as const,
  logLevel: (process.env.LOG_LEVEL as any) || 'info',
  enableConsole: true,
  enableFileLogging: true,
  enablePrettyPrint: process.env.NODE_ENV !== 'production',
  enableLogRotation: true,
  maxFileSize: '10M',
  maxFiles: 7,
  enableDebugFile: process.env.NODE_ENV !== 'production',
};

// Create application logger (for non-HTTP application events)
export const logger = LoggerFactory.createLogger(loggerConfig);

// Create HTTP logger middleware (uses dedicated access logger)
export const httpLogger = LoggerFactory.createHttpLogger(logger, loggerConfig);

// Utility functions for request/correlation IDs
export const generateRequestId = LoggerFactory.generateRequestId;
export const generateCorrelationId = LoggerFactory.generateCorrelationId;

/**
 * Enhanced logging utilities for command-center service
 */

/**
 * Create a database operation logger with enhanced DB context
 */
export function createDatabaseLogger(
  operation: string,
  table?: string,
  schema?: string,
  additionalContext: Partial<LogContext> = {}
): ContextualLogger {
  const dbContext = LoggingUtils.createDatabaseContext(operation, table, schema);
  const enhancedContext: LogContext = {
    ...dbContext,
    component: 'database',
    ...additionalContext,
  };

  return (logger as any).withContext(enhancedContext);
}

/**
 * Create enhanced error logger with comprehensive error context
 */
export function createErrorLogger(
  error: Error,
  context: Partial<LogContext> = {}
): { logger: ContextualLogger; errorContext: any } {
  const errorContext = LoggingUtils.enhanceError(error, {
    component: 'command-center',
    ...context,
  });

  return {
    logger: (logger as any).withContext(errorContext),
    errorContext,
  };
}

/**
 * Create business operation logger with enhanced context
 */
export function createBusinessLogger(
  operation: string,
  step?: string,
  additionalContext: Partial<LogContext> = {}
): ContextualLogger {
  const businessContext = LoggingUtils.createBusinessContext(operation, step, 'command-center');
  const enhancedContext: LogContext = {
    ...businessContext,
    ...additionalContext,
  };

  return (logger as any).withContext(enhancedContext);
}

/**
 * Create security audit logger
 */
export function createSecurityLogger(
  action: string,
  resource?: string,
  resourceId?: string,
  additionalContext: Partial<LogContext> = {}
): ContextualLogger {
  const securityContext = LoggingUtils.createSecurityContext(action, resource, resourceId);
  const enhancedContext: LogContext = {
    ...securityContext,
    component: 'command-center',
    ...additionalContext,
  };

  return (logger as any).withContext(enhancedContext);
}

// Legacy compatibility - create a stream object for any existing Morgan usage
export const loggerStream = {
  write: (message: string) => {
    logger.info(message.trim());
  },
};
