import { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from "@/components/ui/button";
import {
  Users,
  Calendar,
  MessageSquare,
  FileText,
  Settings,
  Menu,
  X,
  ChevronLeft,
  LogOut,
  Home,
  UserPlus,
  BookOpen,
  BarChart2,
  Shield,
  Bell
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useToast } from '@/hooks/use-toast';

export function WellnessLayout() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { toast } = useToast();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [notifications] = useState(3); // Number of unread notifications

  const primaryNavigation = [
    {
      name: 'Dashboard',
      href: '/wellness/dashboard',
      icon: Home,
      description: 'Wellness overview and key metrics'
    },
    {
      name: 'Members',
      href: '/wellness/members',
      icon: Users,
      description: 'Member profiles and management'
    },
    {
      name: 'Sessions',
      href: '/wellness/sessions',
      icon: Calendar,
      description: 'Schedule and manage coaching sessions'
    },
    {
      name: 'Care Plans',
      href: '/wellness/plans',
      icon: BookOpen,
      description: 'Create and manage care plans'
    },
    {
      name: 'Messages',
      href: '/wellness/messages',
      icon: MessageSquare,
      description: 'Secure messaging with members',
      badge: notifications > 0 ? notifications.toString() : undefined
    },
  ];

  const secondaryNavigation = [
    {
      name: 'Reports',
      href: '/wellness/reports',
      icon: BarChart2,
      description: 'Analytics and reporting'
    },
    {
      name: 'Documents',
      href: '/wellness/documents',
      icon: FileText,
      description: 'Document management'
    },
    {
      name: 'Admin',
      href: '/wellness/admin',
      icon: Settings,
      description: 'System administration'
    },
  ];

  const getPageTitle = (path: string): string => {
    if (path === '/wellness/dashboard') return 'Dashboard';
    if (path === '/wellness/members') return 'Members';
    if (path === '/wellness/sessions') return 'Coaching Sessions';
    if (path === '/wellness/plans') return 'Care Plans';
    if (path === '/wellness/messages') return 'Secure Messages';
    if (path === '/wellness/reports') return 'Reports & Analytics';
    if (path === '/wellness/documents') return 'Documents';
    if (path === '/wellness/admin') return 'Administration';
    return 'Wellness Center';
  };

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  const handleLogout = async () => {
    toast({
      title: "Logout Successful",
      description: "You have been successfully logged out.",
    });
    try {
      await logout();
      navigate('/auth/send-code');
    } catch (error) {
      console.error('Logout failed:', error);
      // Force navigation even if logout fails
      navigate('/auth/send-code');
    }
  };

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar for desktop */}
      <div className={`hidden md:flex md:flex-col md:fixed md:inset-y-0 ${collapsed ? 'md:w-20' : 'md:w-72'} transition-all duration-300 ease-in-out z-30`}>
        <div className="flex flex-col h-full border-r border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm">
          {/* Sidebar header with logo and toggle */}
          <div className="flex items-center h-16 px-6 border-b border-gray-200 dark:border-gray-800 bg-gradient-to-r from-teal-600 to-emerald-600 flex-shrink-0">
            {collapsed ? (
              // When collapsed: show hamburger menu centered
              <div className="flex items-center justify-center w-full">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleCollapse}
                  className="text-white hover:bg-white/20 h-8 w-8"
                >
                  <Menu className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              // When expanded: show logo and close button
              <>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-md bg-white flex items-center justify-center">
                    <Shield className="h-5 w-5 text-teal-600" />
                  </div>
                  <AnimatePresence mode="wait">
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-white text-lg font-semibold"
                    >
                      Wellness Center
                    </motion.span>
                  </AnimatePresence>
                </div>
                <div className="flex ml-auto">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleCollapse}
                    className="text-white hover:bg-white/20 h-8 w-8"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </div>
              </>
            )}
          </div>

          {/* Scrollable content area */}
          <div className="flex-1 overflow-y-auto min-h-0 scrollbar-hide">
            {/* Coach profile summary */}
            <div className={`border-b border-gray-200 dark:border-gray-800 p-4`}>
              {user && !collapsed && (
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="/coach-avatar.png" alt="Coach" />
                    <AvatarFallback className="bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300">
                      {user?.firstName?.[0]}{user?.lastName?.[0] || 'SW'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : 'Dr. Sarah Wilson'}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                      Wellness Coach
                    </p>
                  </div>
                </div>
              )}

              {user && collapsed && (
                <div className="flex flex-col items-center justify-center">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="/coach-avatar.png" alt="Coach" />
                    <AvatarFallback className="bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300">
                      {user?.firstName?.[0]}{user?.lastName?.[0] || 'SW'}
                    </AvatarFallback>
                  </Avatar>
                  {notifications > 0 && (
                    <Badge className="mt-2 bg-teal-600 text-white">{notifications}</Badge>
                  )}
                </div>
              )}
            </div>


            {/* Main navigation section */}
            <div className={collapsed ? "px-2 py-2" : "px-4 py-4"}>
              <AnimatePresence mode="wait">
                {!collapsed && (
                  <motion.h3
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Main
                  </motion.h3>
                )}
              </AnimatePresence>
              <nav className={collapsed ? "space-y-1" : "mt-2 space-y-1"}>
              {primaryNavigation.map((item) => {
                const isActive = location.pathname === item.href;
                const IconComponent = item.icon;

                return (
                  <Button
                    key={item.name}
                    variant={isActive ? "secondary" : "ghost"}
                    className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} py-6 ${
                      isActive
                        ? 'bg-teal-50 text-teal-700 dark:bg-teal-900/40 dark:text-teal-200'
                        : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                    }`}
                    onClick={() => navigate(item.href)}
                    title={collapsed ? item.name : ''}
                  >
                    <IconComponent className={`${collapsed ? '' : 'mr-3'} h-5 w-5 ${isActive ? 'text-teal-600 dark:text-teal-400' : ''}`} aria-hidden="true" />
                    <AnimatePresence mode="wait">
                      {!collapsed && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="flex items-center justify-between flex-1"
                        >
                          <span className="text-sm font-medium">{item.name}</span>
                          {item.badge && (
                            <Badge className="ml-auto bg-teal-600 text-white hover:bg-teal-700">
                              {item.badge}
                            </Badge>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Button>
                );
              })}
              </nav>
            </div>

            {/* Administration section */}
            <div className={collapsed ? "px-2 py-2" : "px-4 py-4"}>
              <AnimatePresence mode="wait">
                {!collapsed && (
                  <motion.h3
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Administration
                  </motion.h3>
                )}
              </AnimatePresence>
              <nav className={collapsed ? "space-y-1" : "mt-2 space-y-1"}>
              {secondaryNavigation.map((item) => {
                const isActive = location.pathname === item.href;
                const IconComponent = item.icon;

                return (
                  <Button
                    key={item.name}
                    variant="ghost"
                    className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} py-6 ${
                      isActive
                        ? 'bg-teal-50 text-teal-700 dark:bg-teal-900/40 dark:text-teal-200'
                        : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                    }`}
                    onClick={() => navigate(item.href)}
                    title={collapsed ? item.name : ''}
                  >
                    <IconComponent className={`${collapsed ? '' : 'mr-3'} h-5 w-5 ${isActive ? 'text-teal-600 dark:text-teal-400' : ''}`} aria-hidden="true" />
                    <AnimatePresence mode="wait">
                      {!collapsed && (
                        <motion.span
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="text-sm font-medium"
                        >
                          {item.name}
                        </motion.span>
                      )}
                    </AnimatePresence>
                  </Button>
                );
              })}
              </nav>
            </div>
          </div>

          {/* Footer with actions - positioned at bottom */}
          {user && (
            <div className="flex-shrink-0 p-3 border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 space-y-1">
              <Button
                variant="ghost"
                className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60 py-2.5`}
                onClick={() => navigate('/')}
                title={collapsed ? 'Back to Home' : ''}
              >
                <Home className={`${collapsed ? '' : 'mr-3'} h-4 w-4`} />
                <AnimatePresence mode="wait">
                  {!collapsed && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-sm font-medium"
                    >
                      Back to Home
                    </motion.span>
                  )}
                </AnimatePresence>
              </Button>

              <Button
                variant="ghost"
                className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60 py-2.5`}
                onClick={handleLogout}
                title={collapsed ? 'Logout' : ''}
              >
                <LogOut className={`${collapsed ? '' : 'mr-3'} h-4 w-4`} />
                <AnimatePresence mode="wait">
                  {!collapsed && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-sm font-medium"
                    >
                      Logout
                    </motion.span>
                  )}
                </AnimatePresence>
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile sidebar */}
      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-75" onClick={() => setSidebarOpen(false)}></div>
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white dark:bg-gray-900 shadow-xl">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <Button
              variant="ghost"
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white text-white"
              onClick={() => setSidebarOpen(false)}
            >
              <span className="sr-only">Close sidebar</span>
              <X className="h-6 w-6" aria-hidden="true" />
            </Button>
          </div>

          {/* Mobile sidebar header with logo */}
          <div className="flex items-center h-16 px-4 border-b border-gray-200 dark:border-gray-800 bg-gradient-to-r from-teal-600 to-emerald-600">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-md bg-white flex items-center justify-center">
                <Shield className="h-5 w-5 text-teal-600" />
              </div>
              <span className="text-white text-lg font-semibold">Wellness Center</span>
            </div>
          </div>

          {/* Coach profile for mobile */}
          <div className="flex items-center p-4 border-b border-gray-200 dark:border-gray-800">
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage src="/coach-avatar.png" alt="Coach" />
              <AvatarFallback className="bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300">
                {user?.firstName?.[0]}{user?.lastName?.[0] || 'SW'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : 'Dr. Sarah Wilson'}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">Wellness Coach</p>
            </div>
          </div>

          {/* Mobile navigation */}
          <div className="flex-1 h-0 overflow-y-auto scrollbar-hide">
            <div className="px-2 py-4 space-y-6">
              <div>
                <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Main
                </h3>
                <nav className="mt-2 space-y-1">
                  {primaryNavigation.map((item) => {
                    const isActive = location.pathname === item.href;
                    return (
                      <Button
                        key={item.name}
                        variant={isActive ? "secondary" : "ghost"}
                        className={`w-full justify-start px-3 py-6 ${
                          isActive
                            ? 'bg-teal-50 text-teal-700 dark:bg-teal-900/40 dark:text-teal-200'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                        }`}
                        onClick={() => {
                          navigate(item.href);
                          setSidebarOpen(false);
                        }}
                      >
                        <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-teal-600 dark:text-teal-400' : ''}`} aria-hidden="true" />
                        <span className="text-sm font-medium">{item.name}</span>
                        {item.badge && (
                          <Badge className="ml-auto bg-teal-600 text-white hover:bg-teal-700">
                            {item.badge}
                          </Badge>
                        )}
                      </Button>
                    );
                  })}
                </nav>
              </div>

              <div>
                <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Administration
                </h3>
                <nav className="mt-2 space-y-1">
                  {secondaryNavigation.map((item) => {
                    const isActive = location.pathname === item.href;
                    return (
                      <Button
                        key={item.name}
                        variant="ghost"
                        className={`w-full justify-start px-3 py-6 ${
                          isActive
                            ? 'bg-teal-50 text-teal-700 dark:bg-teal-900/40 dark:text-teal-200'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                        }`}
                        onClick={() => {
                          navigate(item.href);
                          setSidebarOpen(false);
                        }}
                      >
                        <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-teal-600 dark:text-teal-400' : ''}`} aria-hidden="true" />
                        <span className="text-sm font-medium">{item.name}</span>
                      </Button>
                    );
                  })}
                </nav>
              </div>
            </div>
          </div>

          {/* Mobile footer with actions */}
          {user && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-800 space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-700"
                onClick={() => {
                  navigate('/');
                  setSidebarOpen(false);
                }}
              >
                <Home className="mr-3 h-4 w-4" />
                <span className="text-sm">Back to Home</span>
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                onClick={() => {
                  handleLogout();
                  setSidebarOpen(false);
                }}
              >
                <LogOut className="mr-3 h-4 w-4" />
                <span className="text-sm">Logout</span>
              </Button>
            </div>
          )}
        </div>
        <div className="flex-shrink-0 w-14"></div>
      </div>

      {/* Main content wrapper */}
      <div className={`flex flex-col h-screen ${collapsed ? 'md:pl-20' : 'md:pl-72'} transition-all duration-300 ease-in-out`}>
        {/* Mobile header */}
        <div className="md:hidden sticky top-0 z-20 flex items-center justify-between bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 h-16 px-4">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(true)}
              className="text-gray-700 dark:text-gray-300"
            >
              <span className="sr-only">Open sidebar</span>
              <Menu className="h-6 w-6" />
            </Button>
          </div>
          <div className="flex items-center">
            <div className="bg-gradient-to-r from-teal-600 to-emerald-600 rounded-md p-1 mr-2">
              <Shield className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">{getPageTitle(location.pathname)}</h1>
          </div>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-700 dark:text-gray-300 relative"
            >
              <Bell className="h-5 w-5" />
              {notifications > 0 && (
                <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center text-white">
                  {notifications}
                </span>
              )}
            </Button>
          </div>
        </div>
        {/* Desktop header */}
        <div className="hidden md:flex fixed top-0 right-0 left-0 z-10 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 h-16 items-center justify-between px-6" style={{left: collapsed ? '5rem' : '18rem'}}>
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">{getPageTitle(location.pathname)}</h1>
            <Badge variant="outline" className="ml-4 px-2 py-1 text-xs bg-teal-50 text-teal-700 border-teal-200 dark:bg-teal-900/30 dark:text-teal-300 dark:border-teal-800">
              HIPAA Compliant
            </Badge>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-700 dark:text-gray-300 relative"
              onClick={() => navigate('/wellness/messages')}
            >
              <Bell className="h-5 w-5" />
              {notifications > 0 && (
                <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center text-white">
                  {notifications}
                </span>
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={() => navigate('/wellness/members')}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              New Member
            </Button>

            <Avatar className="h-8 w-8">
              <AvatarImage src="/coach-avatar.png" alt="Coach" />
              <AvatarFallback className="bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300">
                {user?.firstName?.[0]}{user?.lastName?.[0] || 'SW'}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 custom-scrollbar pt-16 md:pt-20">
          <div className="px-6 py-6 md:px-8 md:py-8 max-w-7xl mx-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
}

export default WellnessLayout;