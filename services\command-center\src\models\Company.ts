import { Pool } from 'pg';
import { config } from '../config';
import { logger } from '../../../utils/logger';
import {
  Company,
  CreateCompanyRequest,
  UpdateCompanyRequest,
  CompanyQueryParams,
  CompanyStatistics
} from '../types/company';
import { ApiMeta } from '@aperion/shared';

export class CompanyModel {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.user,
      password: config.database.password,
      ssl: config.database.ssl,
      max: config.database.maxConnections,
      idleTimeoutMillis: config.database.idleTimeoutMillis,
      connectionTimeoutMillis: config.database.connectionTimeoutMillis,
    });

    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
  }

  /**
   * Execute a database query
   */
  private async query(text: string, params?: any[]) {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      logger.debug('Executed query', { text, duration, rows: result.rowCount });
      return result;
    } catch (error) {
      logger.error('Query error', { text, params, error });
      throw error;
    }
  }

  /**
   * Get all companies with filtering and pagination
   */
  async getCompanies(params: CompanyQueryParams): Promise<{ companies: Company[]; meta: ApiMeta }> {
    const { page, limit, search, industry, companySize, status, sortBy, sortOrder } = params;
    const offset = (page - 1) * limit;

    // Build WHERE clause
    const conditions: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (search) {
      conditions.push(`(name ILIKE $${paramIndex} OR code ILIKE $${paramIndex} OR industry ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (industry) {
      conditions.push(`industry = $${paramIndex}`);
      queryParams.push(industry);
      paramIndex++;
    }

    if (companySize) {
      conditions.push(`company_size = $${paramIndex}`);
      queryParams.push(companySize);
      paramIndex++;
    }

    if (status) {
      conditions.push(`status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    const orderByClause = `ORDER BY ${sortBy === 'companySize' ? 'company_size' : sortBy} ${sortOrder}`;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM command_center.companies
      ${whereClause}
    `;
    const countResult = await this.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get companies
    const companiesQuery = `
      SELECT
        id,
        name,
        code,
        industry,
        company_size as "companySize",
        address,
        contact_email as "contactEmail",
        contact_phone as "contactPhone",
        website,
        logo_url as "logoUrl",
        status,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM command_center.companies
      ${whereClause}
      ${orderByClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const result = await this.query(companiesQuery, queryParams);

    const meta: ApiMeta = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    };

    return {
      companies: result.rows,
      meta,
    };
  }

  /**
   * Get company by ID
   */
  async getCompanyById(id: string): Promise<Company | null> {
    const query = `
      SELECT
        id,
        name,
        code,
        industry,
        company_size as "companySize",
        address,
        contact_email as "contactEmail",
        contact_phone as "contactPhone",
        website,
        logo_url as "logoUrl",
        status,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM command_center.companies
      WHERE id = $1
    `;

    try {
      const result = await this.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error getting company by ID:', error);
      throw new Error('Failed to get company');
    }
  }

  /**
   * Create a new company
   */
  async createCompany(companyData: CreateCompanyRequest): Promise<Company> {
    const query = `
      INSERT INTO command_center.companies (
        name,
        code,
        industry,
        company_size,
        address,
        contact_email,
        contact_phone,
        website,
        logo_url,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING
        id,
        name,
        code,
        industry,
        company_size as "companySize",
        address,
        contact_email as "contactEmail",
        contact_phone as "contactPhone",
        website,
        logo_url as "logoUrl",
        status,
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, [
        companyData.name,
        companyData.code || null,
        companyData.industry || null,
        companyData.companySize || null,
        companyData.address ? JSON.stringify(companyData.address) : null,
        companyData.contactEmail || null,
        companyData.contactPhone || null,
        companyData.website || null,
        companyData.logoUrl || null,
        companyData.status || 'active'
      ]);

      return result.rows[0];
    } catch (error) {
      logger.error('Error creating company:', error);
      if ((error as any).code === '23505') {
        throw new Error('Company name or code already exists');
      }
      throw new Error('Failed to create company');
    }
  }

  /**
   * Update a company
   */
  async updateCompany(id: string, companyData: UpdateCompanyRequest): Promise<Company | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(companyData).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = key === 'companySize' ? 'company_size' :
                       key === 'contactEmail' ? 'contact_email' :
                       key === 'contactPhone' ? 'contact_phone' :
                       key === 'logoUrl' ? 'logo_url' : key;
        
        fields.push(`${dbField} = $${paramIndex}`);
        values.push(key === 'address' && value ? JSON.stringify(value) : value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    // Add updated_at
    fields.push(`updated_at = NOW()`);
    values.push(id);

    const query = `
      UPDATE command_center.companies
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING
        id,
        name,
        code,
        industry,
        company_size as "companySize",
        address,
        contact_email as "contactEmail",
        contact_phone as "contactPhone",
        website,
        logo_url as "logoUrl",
        status,
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error updating company:', error);
      if ((error as any).code === '23505') {
        throw new Error('Company name or code already exists');
      }
      throw new Error('Failed to update company');
    }
  }

  /**
   * Delete a company
   */
  async deleteCompany(id: string): Promise<boolean> {
    const query = 'DELETE FROM command_center.companies WHERE id = $1';

    try {
      const result = await this.query(query, [id]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      logger.error('Error deleting company:', error);
      throw new Error('Failed to delete company');
    }
  }

  /**
   * Get company statistics
   */
  async getCompanyStatistics(): Promise<CompanyStatistics> {
    const queries = [
      // Total counts by status
      `SELECT 
         COUNT(*) as total,
         COUNT(*) FILTER (WHERE status = 'active') as active,
         COUNT(*) FILTER (WHERE status = 'inactive') as inactive,
         COUNT(*) FILTER (WHERE status = 'suspended') as suspended
       FROM command_center.companies`,
      
      // Companies by industry
      `SELECT industry, COUNT(*) as count
       FROM command_center.companies
       WHERE industry IS NOT NULL
       GROUP BY industry
       ORDER BY count DESC`,
      
      // Companies by size
      `SELECT company_size as size, COUNT(*) as count
       FROM command_center.companies
       WHERE company_size IS NOT NULL
       GROUP BY company_size
       ORDER BY count DESC`
    ];

    try {
      const [statusResult, industryResult, sizeResult] = await Promise.all(
        queries.map(query => this.query(query))
      );

      const statusData = statusResult.rows[0];

      return {
        totalCompanies: parseInt(statusData.total),
        activeCompanies: parseInt(statusData.active),
        inactiveCompanies: parseInt(statusData.inactive),
        suspendedCompanies: parseInt(statusData.suspended),
        companiesByIndustry: industryResult.rows,
        companiesBySize: sizeResult.rows,
      };
    } catch (error) {
      logger.error('Error getting company statistics:', error);
      throw new Error('Failed to get company statistics');
    }
  }
}
