import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Loader2, Save, X, Calendar } from 'lucide-react'
import { CreateDependentData, UpdateDependentData, Dependent } from '@/lib/api'

// Form validation schema
const dependentFormSchema = z.object({
  firstName: z.string().min(1, { message: "First name is required." }).max(100, { message: "First name must be less than 100 characters." }),
  lastName: z.string().min(1, { message: "Last name is required." }).max(100, { message: "Last name must be less than 100 characters." }),
  relationship: z.enum(['spouse', 'child', 'parent', 'sibling', 'other'], { 
    errorMap: () => ({ message: "Please select a valid relationship." })
  }),
  dateOfBirth: z.string().min(1, { message: "Date of birth is required." }),
  gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
})

type DependentFormValues = z.infer<typeof dependentFormSchema>

interface DependentFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateDependentData | UpdateDependentData) => Promise<void>
  dependent?: Dependent | null
  isLoading?: boolean
  mode: 'create' | 'edit'
}

const DependentForm: React.FC<DependentFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  dependent,
  isLoading = false,
  mode
}) => {
  const form = useForm<DependentFormValues>({
    resolver: zodResolver(dependentFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      relationship: undefined,
      dateOfBirth: '',
      gender: undefined,
    },
  })

  // Reset form when dependent changes or dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && dependent) {
        form.reset({
          firstName: dependent.firstName || '',
          lastName: dependent.lastName || '',
          relationship: dependent.relationship as any,
          dateOfBirth: dependent.dateOfBirth || '',
          gender: dependent.gender as any || undefined,
        })
      } else {
        form.reset({
          firstName: '',
          lastName: '',
          relationship: undefined,
          dateOfBirth: '',
          gender: undefined,
        })
      }
    }
  }, [isOpen, dependent, mode, form])

  const handleSubmit = async (values: DependentFormValues) => {
    try {
      await onSubmit(values)
      form.reset()
      onClose()
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Form submission error:', error)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Add New Dependent' : 'Edit Dependent'}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">First Name *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter first name" disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Last Name *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter last name" disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="relationship"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Relationship *</FormLabel>
                  <FormControl>
                    <select
                      {...field}
                      disabled={isLoading}
                      className="w-full border border-neutral-300 rounded-md px-3 py-2 text-sm disabled:bg-neutral-50 disabled:text-neutral-500"
                    >
                      <option value="">Select relationship</option>
                      <option value="spouse">Spouse</option>
                      <option value="child">Child</option>
                      <option value="parent">Parent</option>
                      <option value="sibling">Sibling</option>
                      <option value="other">Other</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="dateOfBirth"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Date of Birth *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          type="date"
                          disabled={isLoading}
                          className="pr-12 [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden"
                          style={{ colorScheme: 'light' }}
                        />
                        <div
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-auto cursor-pointer"
                          onClick={(e) => {
                            if (isLoading) return;
                            e.preventDefault();
                            const input = e.currentTarget.parentElement?.querySelector('input[type="date"]') as HTMLInputElement;
                            input?.showPicker?.();
                          }}
                        >
                          <Calendar className="w-4 h-4 text-primary" />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Gender</FormLabel>
                    <FormControl>
                      <select
                        {...field}
                        disabled={isLoading}
                        className="w-full border border-neutral-300 rounded-md px-3 py-2 text-sm disabled:bg-neutral-50 disabled:text-neutral-500"
                      >
                        <option value="">Select gender (optional)</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                        <option value="prefer_not_to_say">Prefer not to say</option>
                      </select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
                className="bg-neutral-200 text-neutral-700 hover:bg-neutral-300"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-primary text-white hover:bg-primary/90"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {mode === 'create' ? 'Adding...' : 'Saving...'}
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {mode === 'create' ? 'Add Dependent' : 'Save Changes'}
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default DependentForm
