import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem, 
  CarouselNext, 
  CarouselPrevious 
} from "@/components/ui/carousel";
import { RecommendationCard, RecommendationCardProps } from "./RecommendationCard";
import { useMobileDetect } from "@/hooks/useMobileDetect";

interface RecommendationsSectionProps {
  maxItems?: number;
  title?: string;
  showViewAll?: boolean;
  className?: string;
}

// Sample recommendations data - This would be fetched from an API in a real implementation
const sampleRecommendations: RecommendationCardProps[] = [
  {
    id: "1",
    type: "article",
    title: "10 Ways to Improve Your Heart Health",
    description: "Learn simple lifestyle changes that can significantly improve your cardiovascular health and reduce the risk of heart disease.",
    imageUrl: "https://images.unsplash.com/photo-1505576399279-565b52d4ac71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    category: "Heart Health",
    readTime: "5",
    onClick: () => {}
  },
  {
    id: "2",
    type: "video",
    title: "Easy 15-Minute Morning Yoga Routine",
    description: "Start your day with this beginner-friendly yoga routine designed to boost your energy and improve flexibility.",
    imageUrl: "https://images.unsplash.com/photo-1575052814086-f385e2e2ad1b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    category: "Fitness",
    watchTime: "15",
    onClick: () => {}
  },
  {
    id: "3",
    type: "blog",
    title: "Understanding Nutrition Labels: A Complete Guide",
    description: "Learn how to decode nutrition facts labels to make healthier food choices for you and your family.",
    imageUrl: "https://images.unsplash.com/photo-1490818387583-1baba5e638af?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    category: "Nutrition",
    readTime: "8",
    onClick: () => {}
  },
  {
    id: "4",
    type: "news",
    title: "New Study Links Regular Exercise to Improved Mental Health",
    description: "Recent research finds that consistent physical activity can significantly reduce symptoms of anxiety and depression.",
    imageUrl: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    category: "Mental Health",
    readTime: "4",
    onClick: () => {}
  }
];

export function RecommendationsSection({ 
  maxItems = 3, 
  title = "Personalized Recommendations", 
  showViewAll = true,
  className = ""
}: RecommendationsSectionProps) {
  const navigate = useNavigate();
  const [recommendations, setRecommendations] = useState<RecommendationCardProps[]>([]);
  const [loading, setLoading] = useState(true);
  const { isMobile } = useMobileDetect();

  // Navigate to recommendation detail
  const handleCardClick = (id: string) => {
    navigate(`/member/recommendations/${id}`);
  };

  // Navigate to all recommendations
  const handleViewAll = () => {
    navigate("/member/recommendations");
  };

  // In a real implementation, this would be an API call
  useEffect(() => {
    // Simulate loading delay
    const timer = setTimeout(() => {
      const limitedRecommendations = sampleRecommendations
        .slice(0, maxItems)
        .map(rec => ({
          ...rec,
          onClick: () => handleCardClick(rec.id)
        }));
      
      setRecommendations(limitedRecommendations);
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, [maxItems]);

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <Skeleton className="h-6 w-48" />
          {showViewAll && <Skeleton className="h-8 w-24" />}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array(isMobile ? 1 : Math.min(maxItems, 3)).fill(0).map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="h-40 w-full rounded-md" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // For mobile devices, use a carousel for a better experience
  if (isMobile) {
    return (
      <div className={`${className}`}>
        {title && (
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-medium text-[#1e293b]">{title}</h3>
            {showViewAll && (
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-xs text-[#7c3aed] p-0 h-auto hover:bg-transparent"
                onClick={handleViewAll}
              >
                View All
              </Button>
            )}
          </div>
        )}
        
        <Carousel className="w-full">
          <CarouselContent>
            {recommendations.map((recommendation) => (
              <CarouselItem key={recommendation.id}>
                <RecommendationCard {...recommendation} />
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="flex items-center justify-center mt-4">
            <CarouselPrevious className="static transform-none h-8 w-8 mr-2 bg-white border-[#e2e8f0] hover:bg-[#f8fafc]" />
            <CarouselNext className="static transform-none h-8 w-8 bg-white border-[#e2e8f0] hover:bg-[#f8fafc]" />
          </div>
        </Carousel>
      </div>
    );
  }

  // For tablet and desktop
  return (
    <div className={`${className}`}>
      {title && (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-[#1e293b]">{title}</h3>
          {showViewAll && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-sm text-[#7c3aed] hover:bg-[#f8fafc]"
              onClick={handleViewAll}
            >
              View All
            </Button>
          )}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {recommendations.map((recommendation) => (
          <RecommendationCard 
            key={recommendation.id}
            {...recommendation}
          />
        ))}
      </div>
    </div>
  );
}
