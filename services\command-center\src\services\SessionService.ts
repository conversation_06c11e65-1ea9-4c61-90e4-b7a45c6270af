import { UserSessionModel } from '../models/UserSession';
import { logger } from '../../../utils/logger';
import {
  UserSession,
  CreateUserSessionRequest,
  UpdateUserSessionRequest,
  UserSessionQueryParams,
  UserSessionStatistics
} from '../types/session';
import { ApiResponse, ApiMeta } from '@aperion/shared';
import { createSuccessResponse, createErrorResponse } from '../utils/apiResponse';

export class SessionService {
  private userSessionModel: UserSessionModel;

  constructor() {
    this.userSessionModel = new UserSessionModel();
  }

  /**
   * Get all user sessions with filtering and pagination
   */
  async getUserSessions(params: UserSessionQueryParams): Promise<ApiResponse<{ sessions: UserSession[]; meta: ApiMeta }>> {
    try {
      logger.info('Getting user sessions with params:', params);
      
      const result = await this.userSessionModel.getUserSessions(params);
      
      logger.info('Successfully retrieved user sessions', {
        count: result.sessions.length,
        total: result.meta.total
      });

      return createSuccessResponse(result);
    } catch (error) {
      logger.error('Error getting user sessions:', error);
      return createErrorResponse(
        'USER_SESSIONS_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get user sessions'
      );
    }
  }

  /**
   * Get user session by ID
   */
  async getUserSessionById(id: string): Promise<ApiResponse<UserSession>> {
    try {
      logger.info('Getting user session by ID:', { id });
      
      const session = await this.userSessionModel.getUserSessionById(id);
      
      if (!session) {
        return createErrorResponse('USER_SESSION_NOT_FOUND', 'User session not found');
      }

      logger.info('Successfully retrieved user session:', { id, userId: session.userId });

      return createSuccessResponse(session);
    } catch (error) {
      logger.error('Error getting user session by ID:', error);
      return createErrorResponse(
        'USER_SESSION_FETCH_ERROR',
        error instanceof Error ? error.message : 'Failed to get user session'
      );
    }
  }

  /**
   * Create a new user session
   */
  async createUserSession(sessionData: CreateUserSessionRequest): Promise<ApiResponse<UserSession>> {
    try {
      logger.info('Creating new user session:', { userId: sessionData.userId });
      
      const session = await this.userSessionModel.createUserSession(sessionData);
      
      logger.info('Successfully created user session:', { id: session.id, userId: session.userId });

      return createSuccessResponse(session);
    } catch (error) {
      logger.error('Error creating user session:', error);
      return createErrorResponse(
        'USER_SESSION_CREATE_ERROR',
        error instanceof Error ? error.message : 'Failed to create user session'
      );
    }
  }

  /**
   * Update a user session
   */
  async updateUserSession(id: string, sessionData: UpdateUserSessionRequest): Promise<ApiResponse<UserSession>> {
    try {
      logger.info('Updating user session:', { id, data: sessionData });
      
      const session = await this.userSessionModel.updateUserSession(id, sessionData);
      
      if (!session) {
        return createErrorResponse('USER_SESSION_NOT_FOUND', 'User session not found');
      }

      logger.info('Successfully updated user session:', { id, userId: session.userId });

      return createSuccessResponse(session);
    } catch (error) {
      logger.error('Error updating user session:', error);
      return createErrorResponse(
        'USER_SESSION_UPDATE_ERROR',
        error instanceof Error ? error.message : 'Failed to update user session'
      );
    }
  }

  /**
   * Delete a user session
   */
  async deleteUserSession(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    try {
      logger.info('Deleting user session:', { id });
      
      const deleted = await this.userSessionModel.deleteUserSession(id);
      
      if (!deleted) {
        return createErrorResponse('USER_SESSION_NOT_FOUND', 'User session not found');
      }

      logger.info('Successfully deleted user session:', { id });

      return createSuccessResponse({ deleted: true });
    } catch (error) {
      logger.error('Error deleting user session:', error);
      return createErrorResponse(
        'USER_SESSION_DELETE_ERROR',
        error instanceof Error ? error.message : 'Failed to delete user session'
      );
    }
  }

  /**
   * Terminate a user session
   */
  async terminateUserSession(id: string): Promise<ApiResponse<UserSession>> {
    try {
      logger.info('Terminating user session:', { id });

      const session = await this.userSessionModel.terminateUserSession(id);

      if (!session) {
        return createErrorResponse('USER_SESSION_NOT_FOUND', 'User session not found');
      }

      logger.info('Successfully terminated user session:', { id, userId: session.userId });

      return createSuccessResponse(session);
    } catch (error) {
      logger.error('Error terminating user session:', error);
      return createErrorResponse(
        'USER_SESSION_TERMINATE_ERROR',
        error instanceof Error ? error.message : 'Failed to terminate user session'
      );
    }
  }

  /**
   * Update session activity
   */
  async updateSessionActivity(sessionId: string): Promise<ApiResponse<{ updated: boolean }>> {
    try {
      logger.debug('Updating session activity:', { sessionId });

      const updated = await this.userSessionModel.updateSessionActivity(sessionId);

      if (!updated) {
        return createErrorResponse('USER_SESSION_NOT_FOUND', 'Active user session not found');
      }

      logger.debug('Successfully updated session activity:', { sessionId });

      return createSuccessResponse({ updated: true });
    } catch (error) {
      logger.error('Error updating session activity:', error);
      return createErrorResponse(
        'SESSION_ACTIVITY_UPDATE_ERROR',
        error instanceof Error ? error.message : 'Failed to update session activity'
      );
    }
  }

  /**
   * Get user session statistics
   */
  async getUserSessionStatistics(): Promise<ApiResponse<UserSessionStatistics>> {
    try {
      logger.info('Getting user session statistics');

      const statistics = await this.userSessionModel.getUserSessionStatistics();

      logger.info('Successfully retrieved user session statistics:', {
        totalSessions: statistics.totalSessions,
        activeSessions: statistics.activeSessions
      });

      return createSuccessResponse(statistics);
    } catch (error) {
      logger.error('Error getting user session statistics:', error);
      return createErrorResponse(
        'USER_SESSION_STATISTICS_ERROR',
        error instanceof Error ? error.message : 'Failed to get user session statistics'
      );
    }
  }

  /**
   * Get active sessions for a specific user
   */
  async getActiveUserSessions(userId: number): Promise<ApiResponse<UserSession[]>> {
    try {
      logger.info('Getting active sessions for user:', { userId });

      const params: UserSessionQueryParams = {
        page: 1,
        limit: 100,
        userId,
        isActive: true,
        sortBy: 'lastActivityAt',
        sortOrder: 'desc'
      };

      const result = await this.userSessionModel.getUserSessions(params);

      logger.info('Successfully retrieved active user sessions:', {
        userId,
        count: result.sessions.length
      });

      return createSuccessResponse(result.sessions);
    } catch (error) {
      logger.error('Error getting active user sessions:', error);
      return createErrorResponse(
        'ACTIVE_USER_SESSIONS_ERROR',
        error instanceof Error ? error.message : 'Failed to get active user sessions'
      );
    }
  }

  /**
   * Terminate all sessions for a specific user
   */
  async terminateAllUserSessions(userId: number): Promise<ApiResponse<{ terminatedCount: number }>> {
    try {
      logger.info('Terminating all sessions for user:', { userId });

      // Get all active sessions for the user
      const activeSessionsResult = await this.getActiveUserSessions(userId);

      if (!activeSessionsResult.success || !activeSessionsResult.data) {
        return createErrorResponse('USER_SESSIONS_FETCH_ERROR', 'Failed to get user sessions');
      }

      const activeSessions = activeSessionsResult.data;
      let terminatedCount = 0;

      // Terminate each active session
      for (const session of activeSessions) {
        const terminateResult = await this.terminateUserSession(session.id);
        if (terminateResult.success) {
          terminatedCount++;
        }
      }

      logger.info('Successfully terminated user sessions:', {
        userId,
        terminatedCount,
        totalSessions: activeSessions.length
      });

      return createSuccessResponse({ terminatedCount });
    } catch (error) {
      logger.error('Error terminating all user sessions:', error);
      return createErrorResponse(
        'USER_SESSIONS_TERMINATE_ERROR',
        error instanceof Error ? error.message : 'Failed to terminate user sessions'
      );
    }
  }
}
