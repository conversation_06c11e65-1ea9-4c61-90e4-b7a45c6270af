import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { TokenUtils } from '@aperion/shared/server';
import { JWTPayload, UserRole, roleServiceMapping } from '@aperion/shared';
import { config } from '../config';
import { logger } from '../utils/logger';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
    }
  }
}

/**
 * Authentication middleware to verify JWT tokens
 */
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        error: {
          code: 'MISSING_TOKEN',
          message: 'Authorization token is required',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Log token verification attempt
    logger.info('Attempting token verification', {
      requestId: req.requestId,
      tokenLength: token.length,
      tokenPrefix: token.substring(0, 20) + '...',
    });

    // Verify and decode token using jwt.verify directly for compatibility
    // This handles tokens generated by both TokenUtils.generateToken and jwt.sign
    let decoded: any;
    try {
      decoded = jwt.verify(token, config.jwtSecret) as any;
    } catch (jwtError) {
      logger.warn('JWT verification failed', {
        requestId: req.requestId,
        error: jwtError instanceof Error ? jwtError.message : 'Unknown JWT error',
        tokenPrefix: token.substring(0, 20) + '...',
      });
      throw jwtError;
    }

    // Ensure the decoded token has the required structure
    if (!decoded.sub) {
      throw new Error('Token missing required subject (sub) field');
    }

    // Normalize token structure for compatibility
    const normalizedUser = {
      sub: decoded.sub,
      email: decoded.email,
      'custom:role': decoded['custom:role'],
      'custom:service': decoded['custom:service'],
      'custom:permissions': decoded['custom:permissions'] || [],
      'cognito:groups': decoded['cognito:groups'] || [],
      iss: decoded.iss,
      aud: decoded.aud,
      exp: decoded.exp,
      iat: decoded.iat,
    };

    req.user = normalizedUser;

    // Log successful authentication
    logger.info('User authenticated', {
      requestId: req.requestId,
      userId: decoded.sub,
      role: decoded['custom:role'],
      service: decoded['custom:service'],
      tokenIssuer: decoded.iss,
      tokenAudience: decoded.aud,
    });

    next();
  } catch (error) {
    logger.warn('Authentication failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    if (error instanceof Error && error.name === 'TokenExpiredError') {
      res.status(401).json({
        error: {
          code: 'TOKEN_EXPIRED',
          message: 'Token has expired',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    res.status(403).json({
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid or malformed token',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      },
    });
  }
};

/**
 * Authorization middleware to check user permissions
 */
export const authorize = (requiredRoles: UserRole[] = [], requiredPermissions: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'User must be authenticated',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    const userRole = req.user['custom:role'] as UserRole;
    const userPermissions = req.user['custom:permissions'] || [];

    // Check role authorization
    if (requiredRoles.length > 0 && !requiredRoles.includes(userRole)) {
      logger.warn('Authorization failed - insufficient role', {
        requestId: req.requestId,
        userId: req.user.sub,
        userRole,
        requiredRoles,
      });

      res.status(403).json({
        error: {
          code: 'INSUFFICIENT_ROLE',
          message: 'User role does not have access to this resource',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    // Check permission authorization
    const hasAllPermissions = requiredPermissions.every(permission =>
      userPermissions.includes(permission)
    );

    if (requiredPermissions.length > 0 && !hasAllPermissions) {
      logger.warn('Authorization failed - insufficient permissions', {
        requestId: req.requestId,
        userId: req.user.sub,
        userPermissions,
        requiredPermissions,
      });

      res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'User does not have required permissions',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    logger.info('User authorized', {
      requestId: req.requestId,
      userId: req.user.sub,
      userRole,
      requiredRoles,
      requiredPermissions,
    });

    next();
  };
};

/**
 * Service-specific authorization middleware
 */
export const authorizeService = (serviceName: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'User must be authenticated',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    const userService = req.user['custom:service'];
    const userRole = req.user['custom:role'] as UserRole;

    // Allow access if user's service matches or user is system admin
    if (userService === serviceName || userService === 'command-center') {
      next();
      return;
    }

    // Check if user's role is allowed for this service
    const allowedService = roleServiceMapping[userRole];
    if (allowedService === serviceName) {
      next();
      return;
    }

    logger.warn('Service authorization failed', {
      requestId: req.requestId,
      userId: req.user.sub,
      userService,
      userRole,
      requestedService: serviceName,
    });

    res.status(403).json({
      error: {
        code: 'SERVICE_ACCESS_DENIED',
        message: `Access denied to ${serviceName}`,
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      },
    });
  };
};

/**
 * Service-to-service authentication middleware
 */
export const serviceAuthMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Check for service token in headers
    const serviceToken = req.headers['x-service-token'] as string;
    if (!serviceToken) {
      res.status(401).json({
        error: {
          code: 'MISSING_SERVICE_TOKEN',
          message: 'Service token is required for internal API access',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        },
      });
      return;
    }

    // Verify service token
    const decoded = TokenUtils.verifyServiceToken(serviceToken, config.serviceJwtSecret);

    // Add service info to request
    req.headers['x-source-service'] = decoded.sub;
    req.headers['x-target-service'] = decoded.aud;

    logger.info('Service authenticated', {
      requestId: req.requestId,
      sourceService: decoded.sub,
      targetService: decoded.aud,
    });

    next();
  } catch (error) {
    logger.warn('Service authentication failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    res.status(403).json({
      error: {
        code: 'INVALID_SERVICE_TOKEN',
        message: 'Invalid service token',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      },
    });
  }
};
