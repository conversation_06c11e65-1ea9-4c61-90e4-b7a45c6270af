# Aperion Health Database Schema

## Overview

This directory contains the complete database schema design for the Aperion Health platform using a **single PostgreSQL database with schema-based organization**.

## Architecture Decision

Instead of multiple separate databases, we use **one database with logical schemas** for:
- ✅ Simplified management and deployment
- ✅ Better performance with cross-service queries
- ✅ ACID transactions across services
- ✅ Reduced infrastructure complexity
- ✅ Cost-effective scaling

## Database Structure

```
AperionHealth-Dev (PostgreSQL Database)
├── shared_data (Cross-service data)
├── member_service (Member management)
├── employer_service (Employer & employee management)
├── wellness_service (Coaches & wellness programs)
├── lms_service (Learning management)
└── command_center (System administration)
```

## Database Connection

**Host:** **************  
**Database:** AperionHealth-Dev  
**Username:** postgres  
**Password:** Cloud@2025  
**Port:** 5432

## Quick Start

### 1. Install Dependencies
```bash
cd database
npm install
```

### 2. Test Database Connection
```bash
npm run test:connection
```

### 3. Run Migrations
```bash
npm run migrate
```

## Schema Files

| File | Description |
|------|-------------|
| `00_initial_setup.sql` | Extensions, schemas, and shared functions |
| `01_member_service.sql` | Member profiles, health metrics, goals |
| `02_employer_service.sql` | Employers, invitations, employee management |
| `03_wellness_service.sql` | Coaches, assignments, programs |
| `04_lms_service.sql` | Learning modules, enrollments, assessments |
| `05_command_center.sql` | System admin, analytics, maintenance |
| `06_sample_data.sql` | Demo data for testing |

## Key Features

### 🔐 Security
- Row-level security policies
- Encrypted sensitive data
- Audit logging for all changes
- Role-based access control

### 📊 Performance
- Optimized indexes for common queries
- JSONB for flexible data storage
- Partitioning for large tables
- Connection pooling support

### 🔄 Scalability
- Schema-based service separation
- Event sourcing for cross-service communication
- Horizontal scaling ready
- Easy migration to separate databases if needed

### 📈 Analytics
- Built-in usage tracking
- Performance metrics
- System health monitoring
- Business intelligence ready

## Schema Relationships

### Core Entities
- **Users** → Central registry in `shared_data.user_references`
- **Members** → Linked to employers and coaches
- **Employers** → Manage employee invitations and wellness programs
- **Coaches** → Assigned to members, run programs
- **Learning** → Modules and paths for all user types

### Cross-Service References
- Foreign keys reference across schemas
- Event-driven updates via `cross_service_events`
- Audit trail in `audit_logs`

## Migration Management

### Running Migrations
```bash
# Run all pending migrations
npm run migrate

# Development environment
npm run migrate:dev

# Production environment  
npm run migrate:prod
```

### Migration Tracking
- Migrations are tracked in `public.schema_migrations`
- Each migration runs only once
- Rollback support for schema changes
- Checksum verification for integrity

## Backup & Recovery

### Create Backup
```bash
# Full backup
npm run backup

# Schema only
npm run schema:dump

# Data only
npm run data:dump
```

### Restore Database
```bash
# Restore from backup file
psql -h ************** -U postgres -d AperionHealth-Dev < backup_file.sql
```

## Development Guidelines

### Adding New Tables
1. Create migration file in `schema/` directory
2. Follow naming convention: `XX_description.sql`
3. Add to `migrationFiles` array in `migrate.js`
4. Include proper indexes and constraints
5. Add sample data if needed

### Schema Naming
- Use `snake_case` for all identifiers
- Prefix tables with service context when needed
- Use descriptive column names
- Include `created_at` and `updated_at` timestamps

### Data Types
- Use `UUID` for primary keys
- Use `JSONB` for flexible data
- Use `TEXT[]` for arrays
- Use `DECIMAL` for monetary values
- Use `TIMESTAMP` for dates/times

## Monitoring

### Health Checks
```sql
-- Check schema existence
SELECT schema_name FROM information_schema.schemata 
WHERE schema_name IN ('shared_data', 'member_service', 'employer_service', 'wellness_service', 'lms_service', 'command_center');

-- Table counts by schema
SELECT schemaname, COUNT(*) as table_count 
FROM pg_tables 
WHERE schemaname IN ('shared_data', 'member_service', 'employer_service', 'wellness_service', 'lms_service', 'command_center')
GROUP BY schemaname;

-- Recent activity
SELECT * FROM shared_data.audit_logs ORDER BY created_at DESC LIMIT 10;
```

### Performance Monitoring
- Query execution times
- Index usage statistics
- Connection pool metrics
- Storage utilization

## Support

For database-related issues:
1. Check connection with `npm run test:connection`
2. Verify schema with migration verification
3. Review audit logs for data issues
4. Contact database administrator

## Future Considerations

### Scaling Options
1. **Read Replicas** - For analytics and reporting
2. **Partitioning** - For large tables (users, events)
3. **Separate Databases** - If services need independent scaling
4. **Sharding** - For massive scale (millions of users)

The current single-database design supports growth to 100K+ users efficiently while maintaining simplicity and performance.
