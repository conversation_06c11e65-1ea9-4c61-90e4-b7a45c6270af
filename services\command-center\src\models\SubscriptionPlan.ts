import { Pool } from 'pg';
import { config } from '../config';
import { logger } from '../../../utils/logger';
import {
  SubscriptionPlan,
  CreateSubscriptionPlanRequest,
  UpdateSubscriptionPlanRequest,
  SubscriptionPlanQueryParams,
  SubscriptionPlanStatistics,

} from '../types/subscription';
import { ApiMeta } from '@aperion/shared';

export class SubscriptionPlanModel {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.user,
      password: config.database.password,
      ssl: config.database.ssl,
      max: config.database.maxConnections,
      idleTimeoutMillis: config.database.idleTimeoutMillis,
      connectionTimeoutMillis: config.database.connectionTimeoutMillis,
    });

    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
  }

  /**
   * Execute a database query
   */
  private async query(text: string, params?: any[]) {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      logger.debug('Executed query', { text, duration, rows: result.rowCount });
      return result;
    } catch (error) {
      logger.error('Query error', { text, params, error });
      throw error;
    }
  }

  /**
   * Get all subscription plans with filtering and pagination
   */
  async getSubscriptionPlans(params: SubscriptionPlanQueryParams): Promise<{ plans: SubscriptionPlan[]; meta: ApiMeta }> {
    const { page, limit, search, planType, isActive, sortBy, sortOrder } = params;
    const offset = (page - 1) * limit;

    // Build WHERE clause
    const conditions: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (search) {
      conditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (planType) {
      conditions.push(`plan_type = $${paramIndex}`);
      queryParams.push(planType);
      paramIndex++;
    }

    if (isActive !== undefined) {
      conditions.push(`is_active = $${paramIndex}`);
      queryParams.push(isActive);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    const orderByClause = `ORDER BY ${sortBy === 'planType' ? 'plan_type' : 
                                     sortBy === 'priceMonthly' ? 'price_monthly' :
                                     sortBy === 'priceYearly' ? 'price_yearly' :
                                     sortBy === 'createdAt' ? 'created_at' :
                                     sortBy === 'updatedAt' ? 'updated_at' : sortBy} ${sortOrder}`;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM command_center.subscription_plans
      ${whereClause}
    `;
    const countResult = await this.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get subscription plans
    const plansQuery = `
      SELECT
        id,
        name,
        description,
        plan_type as "planType",
        price_monthly as "priceMonthly",
        price_yearly as "priceYearly",
        max_users as "maxUsers",
        features,
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM command_center.subscription_plans
      ${whereClause}
      ${orderByClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const result = await this.query(plansQuery, queryParams);

    const meta: ApiMeta = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    };

    return {
      plans: result.rows,
      meta,
    };
  }

  /**
   * Get subscription plan by ID
   */
  async getSubscriptionPlanById(id: string): Promise<SubscriptionPlan | null> {
    const query = `
      SELECT
        id,
        name,
        description,
        plan_type as "planType",
        price_monthly as "priceMonthly",
        price_yearly as "priceYearly",
        max_users as "maxUsers",
        features,
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM command_center.subscription_plans
      WHERE id = $1
    `;

    try {
      const result = await this.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error getting subscription plan by ID:', error);
      throw new Error('Failed to get subscription plan');
    }
  }

  /**
   * Create a new subscription plan
   */
  async createSubscriptionPlan(planData: CreateSubscriptionPlanRequest): Promise<SubscriptionPlan> {
    const query = `
      INSERT INTO command_center.subscription_plans (
        name,
        description,
        plan_type,
        price_monthly,
        price_yearly,
        max_users,
        features,
        is_active
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING
        id,
        name,
        description,
        plan_type as "planType",
        price_monthly as "priceMonthly",
        price_yearly as "priceYearly",
        max_users as "maxUsers",
        features,
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, [
        planData.name,
        planData.description || null,
        planData.planType,
        planData.priceMonthly || null,
        planData.priceYearly || null,
        planData.maxUsers || null,
        JSON.stringify(planData.features),
        planData.isActive
      ]);

      return result.rows[0];
    } catch (error) {
      logger.error('Error creating subscription plan:', error);
      if ((error as any).code === '23505') {
        throw new Error('Subscription plan name already exists');
      }
      throw new Error('Failed to create subscription plan');
    }
  }

  /**
   * Update a subscription plan
   */
  async updateSubscriptionPlan(id: string, planData: UpdateSubscriptionPlanRequest): Promise<SubscriptionPlan | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(planData).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = key === 'planType' ? 'plan_type' :
                       key === 'priceMonthly' ? 'price_monthly' :
                       key === 'priceYearly' ? 'price_yearly' :
                       key === 'maxUsers' ? 'max_users' :
                       key === 'isActive' ? 'is_active' : key;
        
        fields.push(`${dbField} = $${paramIndex}`);
        values.push(key === 'features' && value ? JSON.stringify(value) : value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    // Add updated_at
    fields.push(`updated_at = NOW()`);
    values.push(id);

    const query = `
      UPDATE command_center.subscription_plans
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING
        id,
        name,
        description,
        plan_type as "planType",
        price_monthly as "priceMonthly",
        price_yearly as "priceYearly",
        max_users as "maxUsers",
        features,
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error updating subscription plan:', error);
      if ((error as any).code === '23505') {
        throw new Error('Subscription plan name already exists');
      }
      throw new Error('Failed to update subscription plan');
    }
  }

  /**
   * Delete a subscription plan
   */
  async deleteSubscriptionPlan(id: string): Promise<boolean> {
    const query = 'DELETE FROM command_center.subscription_plans WHERE id = $1';

    try {
      const result = await this.query(query, [id]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      logger.error('Error deleting subscription plan:', error);
      throw new Error('Failed to delete subscription plan');
    }
  }

  /**
   * Get subscription plan statistics
   */
  async getSubscriptionPlanStatistics(): Promise<SubscriptionPlanStatistics> {
    const queries = [
      // Total counts by status
      `SELECT 
         COUNT(*) as total,
         COUNT(*) FILTER (WHERE is_active = true) as active,
         COUNT(*) FILTER (WHERE is_active = false) as inactive
       FROM command_center.subscription_plans`,
      
      // Plans by type
      `SELECT plan_type as type, COUNT(*) as count
       FROM command_center.subscription_plans
       GROUP BY plan_type
       ORDER BY count DESC`,
      
      // Average prices
      `SELECT 
         AVG(price_monthly) as avg_monthly,
         AVG(price_yearly) as avg_yearly
       FROM command_center.subscription_plans
       WHERE price_monthly IS NOT NULL OR price_yearly IS NOT NULL`
    ];

    try {
      const [statusResult, typeResult, priceResult] = await Promise.all(
        queries.map(query => this.query(query))
      );

      const statusData = statusResult.rows[0];
      const priceData = priceResult.rows[0];

      return {
        totalPlans: parseInt(statusData.total),
        activePlans: parseInt(statusData.active),
        inactivePlans: parseInt(statusData.inactive),
        plansByType: typeResult.rows,
        averagePriceMonthly: parseFloat(priceData.avg_monthly) || 0,
        averagePriceYearly: parseFloat(priceData.avg_yearly) || 0,
      };
    } catch (error) {
      logger.error('Error getting subscription plan statistics:', error);
      throw new Error('Failed to get subscription plan statistics');
    }
  }
}
