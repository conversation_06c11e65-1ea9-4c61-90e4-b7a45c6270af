import { Request, Response } from 'express';
import { UserService } from '../services/UserService';
import { logger } from '../../../utils/logger';
import { 
  createUserSchema, 
  updateUserSchema, 
  userQuerySchema,
  UserQueryParams 
} from '../types/user';
import { HttpStatus } from '@aperion/shared';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * GET /admin/users
   * Get all users with pagination and filtering
   */
  getUsers = async (req: Request, res: Response): Promise<void> => {
    try {
      // Parse and validate query parameters
      const queryParams: UserQueryParams = {
        page: req.query.page ? parseInt(req.query.page as string, 10) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string, 10) : 20,
        search: req.query.search as string,
        role: req.query.role as any,
        status: req.query.status as any,
        company: req.query.company as string,
        sortBy: req.query.sortBy as string || 'createdAt',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc'
      };

      // Validate query parameters
      const validationResult = userQuerySchema.safeParse(queryParams);
      if (!validationResult.success) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'INVALID_QUERY_PARAMS',
            message: 'Invalid query parameters',
            details: validationResult.error.errors,
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      const result = await this.userService.getUsers(validationResult.data);

      if (!result.success) {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      logger.error('Error in UserController.getUsers:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * GET /admin/users/dev/filter
   * Get users filtered by role with enhanced role mapping
   * This endpoint provides dedicated role-based filtering with automatic mapping
   * between frontend role names and backend user types
   */
  getUsersByRoleFilter = async (req: Request, res: Response): Promise<void> => {
    try {
      // Parse and validate query parameters
      const queryParams: UserQueryParams = {
        page: req.query.page ? parseInt(req.query.page as string, 10) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string, 10) : 20,
        search: req.query.search as string,
        role: req.query.role as any,
        status: req.query.status as any,
        company: req.query.company as string,
        sortBy: req.query.sortBy as string || 'createdAt',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc'
      };

      // Enhanced role mapping for frontend compatibility
      // Map frontend role names to UserRole enum values (with hyphens)
      const frontendToBackendRoleMapping: Record<string, string> = {
        'member': 'member',
        'employer': 'employer',
        'coach': 'wellness-coach',
        'admin': 'system-admin'
      };

      // Apply role mapping if role is provided
      if (queryParams.role && frontendToBackendRoleMapping[queryParams.role]) {
        queryParams.role = frontendToBackendRoleMapping[queryParams.role] as any;
        logger.info(`Role mapping applied: ${req.query.role} -> ${queryParams.role}`);
      }

      // Validate query parameters
      const validationResult = userQuerySchema.safeParse(queryParams);
      if (!validationResult.success) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'INVALID_QUERY_PARAMS',
            message: 'Invalid query parameters',
            details: validationResult.error.errors,
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      // Convert UserRole enum values (with hyphens) to database user_type values (with underscores)
      const roleToUserTypeMapping: Record<string, string> = {
        'member': 'member',
        'employer': 'employer',
        'wellness-coach': 'wellness_coach',
        'system-admin': 'system_admin'
      };

      // Apply database user_type mapping for filtering
      const finalParams = { ...validationResult.data };
      if (finalParams.role && roleToUserTypeMapping[finalParams.role]) {
        finalParams.role = roleToUserTypeMapping[finalParams.role] as any;
      }

      // Log the filter request for debugging
      logger.info('Role filter request:', {
        originalRole: req.query.role,
        mappedRole: validationResult.data.role,
        finalUserType: finalParams.role,
        otherParams: {
          page: finalParams.page,
          limit: finalParams.limit,
          search: finalParams.search,
          status: finalParams.status,
          company: finalParams.company
        }
      });

      const result = await this.userService.getUsersByRoleFilter(finalParams);

      if (!result.success) {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      // Add filter metadata to response
      const responseWithFilterInfo = {
        ...result,
        filterInfo: {
          appliedRole: req.query.role,
          mappedToUserType: validationResult.data.role,
          availableRoles: ['member', 'employer', 'coach', 'admin'],
          roleMapping: frontendToBackendRoleMapping
        }
      };

      res.status(HttpStatus.OK).json(responseWithFilterInfo);
    } catch (error) {
      logger.error('Error in UserController.getUsersByRoleFilter:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * GET /admin/users/:id
   * Get user by ID
   */
  getUserById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID is required',
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      const result = await this.userService.getUserById(id);

      if (!result.success) {
        const statusCode = result.error?.code === 'USER_NOT_FOUND' 
          ? HttpStatus.NOT_FOUND 
          : HttpStatus.INTERNAL_SERVER_ERROR;

        res.status(statusCode).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      logger.error('Error in UserController.getUserById:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * POST /admin/users
   * Create a new user
   */
  createUser = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request body
      const validationResult = createUserSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'INVALID_REQUEST_DATA',
            message: 'Invalid user data',
            details: validationResult.error.errors,
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      const result = await this.userService.createUser(validationResult.data);

      if (!result.success) {
        const statusCode = result.error?.code === 'USER_ALREADY_EXISTS' 
          ? HttpStatus.CONFLICT 
          : HttpStatus.INTERNAL_SERVER_ERROR;

        res.status(statusCode).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      logger.error('Error in UserController.createUser:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * PUT /admin/users/:id
   * Update user
   */
  updateUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID is required',
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      // Validate request body
      const validationResult = updateUserSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'INVALID_REQUEST_DATA',
            message: 'Invalid user data',
            details: validationResult.error.errors,
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      const result = await this.userService.updateUser(id, validationResult.data);

      if (!result.success) {
        let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
        if (result.error?.code === 'USER_NOT_FOUND') {
          statusCode = HttpStatus.NOT_FOUND;
        } else if (result.error?.code === 'EMAIL_ALREADY_EXISTS') {
          statusCode = HttpStatus.CONFLICT;
        }

        res.status(statusCode).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      logger.error('Error in UserController.updateUser:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * DELETE /admin/users/:id
   * Delete user (soft delete)
   */
  deleteUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID is required',
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      const result = await this.userService.deleteUser(id);

      if (!result.success) {
        const statusCode = result.error?.code === 'USER_NOT_FOUND' 
          ? HttpStatus.NOT_FOUND 
          : HttpStatus.INTERNAL_SERVER_ERROR;

        res.status(statusCode).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      logger.error('Error in UserController.deleteUser:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * GET /admin/users/statistics
   * Get user statistics
   */
  getUserStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const result = await this.userService.getUserStatistics();

      if (!result.success) {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      logger.error('Error in UserController.getUserStatistics:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * PATCH /admin/users/:id/suspend
   * Suspend user
   */
  suspendUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID is required',
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      const result = await this.userService.suspendUser(id);

      if (!result.success) {
        const statusCode = result.error?.code === 'USER_NOT_FOUND' 
          ? HttpStatus.NOT_FOUND 
          : HttpStatus.INTERNAL_SERVER_ERROR;

        res.status(statusCode).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      logger.error('Error in UserController.suspendUser:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * PATCH /admin/users/:id/activate
   * Activate user
   */
  activateUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'User ID is required',
            timestamp: new Date().toISOString(),
            requestId: req.requestId,
          }
        });
        return;
      }

      const result = await this.userService.activateUser(id);

      if (!result.success) {
        const statusCode = result.error?.code === 'USER_NOT_FOUND' 
          ? HttpStatus.NOT_FOUND 
          : HttpStatus.INTERNAL_SERVER_ERROR;

        res.status(statusCode).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      logger.error('Error in UserController.activateUser:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };

  /**
   * GET /admin/users/companies
   * Get list of companies for filtering
   */
  getCompanies = async (req: Request, res: Response): Promise<void> => {
    try {
      const result = await this.userService.getCompanies();

      if (!result.success) {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          ...result,
          error: {
            ...result.error,
            requestId: req.requestId,
          }
        });
        return;
      }

      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      logger.error('Error in UserController.getCompanies:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      });
    }
  };
}



//