import { randomBytes } from 'crypto';

export interface PasskeyRegistrationOptions {
  challenge: string;
  rp: {
    name: string;
    id: string;
  };
  user: {
    id: string;
    name: string;
    displayName: string;
  };
  pubKeyCredParams: Array<{
    alg: number;
    type: string;
  }>;
  authenticatorSelection: {
    authenticatorAttachment?: 'platform' | 'cross-platform';
    userVerification: 'required' | 'preferred' | 'discouraged';
    residentKey?: 'required' | 'preferred' | 'discouraged';
  };
  timeout: number;
  attestation: 'none' | 'indirect' | 'direct';
}

export interface PasskeyAuthenticationOptions {
  challenge: string;
  timeout: number;
  rpId: string;
  allowCredentials?: Array<{
    id: string;
    type: string;
    transports?: string[];
  }>;
  userVerification: 'required' | 'preferred' | 'discouraged';
}

export interface PasskeyCredential {
  id: string;
  rawId: string;
  type: 'public-key';
  response: {
    clientDataJSON: string;
    attestationObject?: string;
    authenticatorData?: string;
    signature?: string;
    userHandle?: string;
  };
}

export class PasskeyService {
  private rpName: string;
  private rpId: string;
  private origin: string;

  constructor(rpName: string, rpId: string, origin: string) {
    this.rpName = rpName;
    this.rpId = rpId;
    this.origin = origin;
  }

  /**
   * Generate registration options for passkey creation
   */
  generateRegistrationOptions(userId: string, email: string): PasskeyRegistrationOptions {
    const challenge = randomBytes(32).toString('base64url');

    return {
      challenge,
      rp: {
        name: this.rpName,
        id: this.rpId,
      },
      user: {
        id: Buffer.from(userId).toString('base64url'),
        name: email,
        displayName: email,
      },
      pubKeyCredParams: [
        { alg: -7, type: 'public-key' }, // ES256
        { alg: -257, type: 'public-key' }, // RS256
      ],
      authenticatorSelection: {
        authenticatorAttachment: 'platform',
        userVerification: 'required',
        residentKey: 'preferred',
      },
      timeout: 60000,
      attestation: 'none',
    };
  }

  /**
   * Generate authentication options for passkey login
   */
  generateAuthenticationOptions(allowedCredentials?: string[]): PasskeyAuthenticationOptions {
    const challenge = randomBytes(32).toString('base64url');

    return {
      challenge,
      timeout: 60000,
      rpId: this.rpId,
      allowCredentials: allowedCredentials?.map(id => ({
        id,
        type: 'public-key',
        transports: ['internal', 'hybrid'],
      })),
      userVerification: 'required',
    };
  }

  /**
   * Verify passkey registration response
   */
  async verifyRegistration(
    credential: PasskeyCredential,
    expectedChallenge: string,
    expectedOrigin: string = this.origin
  ): Promise<{
    verified: boolean;
    credentialId?: string;
    publicKey?: string;
    counter?: number;
  }> {
    try {
      // Parse client data JSON
      const clientDataJSON = JSON.parse(
        Buffer.from(credential.response.clientDataJSON, 'base64').toString()
      );

      // Verify challenge
      if (clientDataJSON.challenge !== expectedChallenge) {
        return { verified: false };
      }

      // Verify origin
      if (clientDataJSON.origin !== expectedOrigin) {
        return { verified: false };
      }

      // Verify type
      if (clientDataJSON.type !== 'webauthn.create') {
        return { verified: false };
      }

      // TODO: Implement full WebAuthn verification
      // This would include:
      // - Parsing attestation object
      // - Verifying authenticator data
      // - Extracting public key
      // - Verifying attestation signature

      return {
        verified: true,
        credentialId: credential.id,
        publicKey: 'mock-public-key', // Would be extracted from attestation
        counter: 0,
      };
    } catch (error) {
      return { verified: false };
    }
  }

  /**
   * Verify passkey authentication response
   */
  async verifyAuthentication(
    credential: PasskeyCredential,
    expectedChallenge: string,
    storedPublicKey: string,
    storedCounter: number,
    expectedOrigin: string = this.origin
  ): Promise<{
    verified: boolean;
    counter?: number;
  }> {
    try {
      // Parse client data JSON
      const clientDataJSON = JSON.parse(
        Buffer.from(credential.response.clientDataJSON, 'base64').toString()
      );

      // Verify challenge
      if (clientDataJSON.challenge !== expectedChallenge) {
        return { verified: false };
      }

      // Verify origin
      if (clientDataJSON.origin !== expectedOrigin) {
        return { verified: false };
      }

      // Verify type
      if (clientDataJSON.type !== 'webauthn.get') {
        return { verified: false };
      }

      // TODO: Implement full WebAuthn verification
      // This would include:
      // - Parsing authenticator data
      // - Verifying signature
      // - Checking counter
      // - Validating public key

      return {
        verified: true,
        counter: storedCounter + 1,
      };
    } catch (error) {
      return { verified: false };
    }
  }

  /**
   * Generate a challenge for passkey operations
   */
  generateChallenge(): string {
    return randomBytes(32).toString('base64url');
  }

  /**
   * Validate challenge format
   */
  validateChallenge(challenge: string): boolean {
    try {
      // Check if it's valid base64url
      const decoded = Buffer.from(challenge, 'base64url');
      return decoded.length === 32;
    } catch {
      return false;
    }
  }
}

/**
 * Utility functions for passkey support detection
 */
export const PasskeyUtils = {
  /**
   * Check if the current environment supports passkeys
   */
  isSupported(): boolean {
    if (typeof window === 'undefined') {
      return false;
    }

    return !!(
      window.navigator &&
      window.navigator.credentials &&
      window.navigator.credentials.create &&
      window.navigator.credentials.get &&
      window.PublicKeyCredential
    );
  },

  /**
   * Check if platform authenticator is available
   */
  async isPlatformAuthenticatorAvailable(): Promise<boolean> {
    if (!this.isSupported()) {
      return false;
    }

    try {
      return await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
    } catch {
      return false;
    }
  },

  /**
   * Get supported transports
   */
  getSupportedTransports(): string[] {
    const transports = ['internal'];
    
    if (typeof window !== 'undefined' && window.navigator.userAgent) {
      const userAgent = window.navigator.userAgent.toLowerCase();
      
      if (userAgent.includes('android')) {
        transports.push('hybrid');
      }
      
      if (userAgent.includes('chrome') || userAgent.includes('edge')) {
        transports.push('usb', 'ble', 'nfc');
      }
    }
    
    return transports;
  },
};
