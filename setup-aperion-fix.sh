#!/bin/bash

# Aperion Health - UserRole Import Fix Setup Script
# This script fixes the UserRole import/export issue by setting up dual-format builds

set -e  # Exit on any error

echo "🚀 Aperion Health - UserRole Import Fix Setup"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}📋 Step $1: $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "packages/shared" ]; then
    print_error "Please run this script from the Aperion Health project root directory"
    exit 1
fi

print_step "1" "Checking Node.js version"
NODE_VERSION=$(node --version)
echo "Node.js version: $NODE_VERSION"
if [[ "$NODE_VERSION" < "v16" ]]; then
    print_warning "Node.js 16+ recommended for best ES modules support"
else
    print_success "Node.js version is compatible"
fi
echo ""

print_step "2" "Cleaning existing installations"
echo "Removing node_modules and package-lock files..."
rm -rf node_modules
rm -rf packages/*/node_modules
rm -rf services/*/node_modules
rm -rf apps/*/node_modules
rm -f package-lock.json
rm -f packages/*/package-lock.json
rm -f services/*/package-lock.json
rm -f apps/*/package-lock.json
print_success "Cleaned existing installations"
echo ""

print_step "3" "Installing dependencies"
echo "Running npm install..."
npm install
print_success "Dependencies installed"
echo ""

print_step "4" "Setting up shared package dual-format build"
cd packages/shared

# Clean existing build
echo "Cleaning existing shared package build..."
npm run clean 2>/dev/null || rm -rf dist

# Build the shared package
echo "Building shared package with dual-format support..."
npm run build

# Verify build
if [ -d "dist/cjs" ] && [ -d "dist/esm" ] && [ -d "dist/types" ]; then
    print_success "Shared package dual-format build completed"
else
    print_error "Shared package build failed"
    exit 1
fi

cd ../..
echo ""

print_step "5" "Verifying build outputs"
echo "Checking CommonJS format..."
if [ -f "packages/shared/dist/cjs/index.js" ]; then
    echo "CJS index.js first few lines:"
    head -3 packages/shared/dist/cjs/index.js
    print_success "CommonJS build verified"
else
    print_error "CommonJS build missing"
    exit 1
fi

echo ""
echo "Checking ES modules format..."
if [ -f "packages/shared/dist/esm/index.js" ]; then
    echo "ESM index.js content:"
    cat packages/shared/dist/esm/index.js
    print_success "ES modules build verified"
else
    print_error "ES modules build missing"
    exit 1
fi

echo ""
echo "Checking TypeScript declarations..."
if [ -f "packages/shared/dist/types/index.d.ts" ]; then
    echo "Types index.d.ts content:"
    cat packages/shared/dist/types/index.d.ts
    print_success "TypeScript declarations verified"
else
    print_error "TypeScript declarations missing"
    exit 1
fi
echo ""

print_step "6" "Checking environment configuration"
if [ -f ".env" ]; then
    if grep -q "CORS_ORIGINS.*4001" .env; then
        print_success "CORS configuration includes port 4001"
    else
        print_warning "Adding port 4001 to CORS configuration"
        sed -i.bak 's/CORS_ORIGINS=\([^,]*\)/CORS_ORIGINS=\1,http:\/\/localhost:4001/' .env
        print_success "Updated CORS configuration"
    fi
else
    print_warning ".env file not found - you may need to create one"
fi
echo ""

print_step "7" "Final verification"
echo "Checking UserRole exports..."

# Check if UserRole is exported in both formats
if grep -q "UserRole" packages/shared/dist/cjs/types/user.js && grep -q "UserRole" packages/shared/dist/esm/types/user.js; then
    print_success "UserRole exported in both CJS and ESM formats"
else
    print_error "UserRole export verification failed"
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Run: ./start-services.sh (to start all services)"
echo "2. Or manually start services in separate terminals:"
echo "   - API Gateway: cd packages/api-gateway && npm run dev"
echo "   - Command Center: cd services/command-center && npm run dev"
echo "   - Member Service: cd services/member && npm run dev"
echo "   - Frontend: cd apps/aperion-portal && npm run dev"
echo ""
echo "The UserRole import issue should now be resolved!"
