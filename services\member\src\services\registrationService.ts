import { ErrorCode } from '@aperion/shared';
import { DatabaseConnection, getServiceDatabaseConfig } from '@aperion/shared/server';
import {
  logger,
  createOperationLogger,
  createDatabaseLogger,
  createBusinessLogger,
  createErrorLogger,
  createPerformanceLogger,
  createSecurityLogger
} from '../utils/logger';
import {
  CognitoIdentityProviderClient,
  SignUpCommand,
  AdminConfirmSignUpCommand,
  AdminUpdateUserAttributesCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import crypto from 'crypto';



// Initialize secure database connection using shared utilities
let dbConnection: DatabaseConnection;
try {
  // Create initialization logger with consistent context
  const initLogger = createOperationLogger('database_initialization', {
    component: 'registration-service',
    phase: 'startup'
  });

  const config = getServiceDatabaseConfig('member');
  initLogger.info('Database config retrieved', {
    config: { ...config, password: '[REDACTED]' },
    configSource: 'shared-utilities',
    serviceName: 'member'
  });

  dbConnection = new DatabaseConnection(config);
  initLogger.info('Database connection initialized successfully', {
    connectionType: 'PostgreSQL',
    poolEnabled: true,
    sslEnabled: config.ssl || false,
    database: config.database
  });
} catch (error) {
  const initLogger = createOperationLogger('database_initialization', {
    component: 'registration-service',
    phase: 'startup'
  });

  initLogger.error('Failed to initialize database connection', {
    error: error instanceof Error ? error.message : 'Unknown error',
    stack: error instanceof Error ? error.stack : undefined,
    errorName: error instanceof Error ? error.name : 'Unknown',
    errorCode: (error as any)?.code,
    configAttempted: 'member-service-config'
  });
  throw error;
}

// Create a unified query interface for database operations
const pool = {
  query: (text: string, params?: any[]) => dbConnection.query(text, params),
  connect: () => dbConnection.getClient(),
};

// Initialize Cognito client
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// Helper function to generate secure password
const generateSecurePassword = (): string => {
  const length = 12;
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
};

// Helper function to calculate secret hash for Cognito
const calculateSecretHash = (username: string): string => {
  const clientSecret = process.env.COGNITO_CLIENT_SECRET || process.env.CLIENT_SECRET;
  const clientId = process.env.COGNITO_CLIENT_ID || process.env.CLIENT_ID;

  if (!clientSecret || !clientId) {
    throw new Error('COGNITO_CLIENT_SECRET and COGNITO_CLIENT_ID must be set');
  }

  return crypto
    .createHmac('SHA256', clientSecret)
    .update(username + clientId)
    .digest('base64');
};

/**
 * Custom service error class
 */
export class ServiceError extends Error {
  public readonly code: ErrorCode;
  public readonly details?: any;

  constructor(code: ErrorCode, message: string, details?: any) {
    super(message);
    this.name = 'ServiceError';
    this.code = code;
    this.details = details;
  }
}

/**
 * Registration data interface
 */
export interface RegistrationData {
  // Basic Info
  firstName: string;
  lastName: string;
  email: string;
  phoneCountryCode: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';

  // Address Information
  streetAddress?: string;
  apartmentUnit?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;

  // System fields
  employerId?: string;
  companyReference?: string;
  activationCode?: string; // Added for activation code validation
}

/**
 * Employee invitation interface (from employer service)
 */
export interface EmployeeInvitation {
  id: string;
  employer_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  department?: string;
  job_title?: string;
  employee_id?: string;
  activation_code: string;
  invitation_sent_at: Date;
  activation_code_used_at?: Date;
  expires_at: Date;
  status: 'pending' | 'sent' | 'activated' | 'expired' | 'cancelled';
  created_by: string;
  metadata?: any;
}

/**
 * Member interface based on database schema
 */
export interface Member {
  id: string;
  cognito_user_id: string;
  employer_id: string;
  activation_code?: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  date_of_birth?: Date;
  gender?: string;
  address?: any;
  status: string;
  created_at: Date;
  updated_at: Date;
}



/**
 * Mock employer data for testing purposes
 */
interface MockEmployer {
  id: string;
  company_name: string;
  company_code: string;
  industry: string;
  company_size: string;
}

/**
 * Generate a mock employer ID and data
 */
const generateMockEmployer = (): MockEmployer => {
  const companies = [
    { name: 'TechCorp Solutions', code: 'TECHCORP', industry: 'Technology', size: '51-200' },
    { name: 'HealthFirst Medical', code: 'HEALTHFIRST', industry: 'Healthcare', size: '201-500' },
    { name: 'Global Finance Inc', code: 'GLOBALFIN', industry: 'Finance', size: '501-1000' },
    { name: 'EduTech Learning', code: 'EDUTECH', industry: 'Education', size: '11-50' },
    { name: 'GreenEnergy Corp', code: 'GREENENERGY', industry: 'Energy', size: '101-200' },
    { name: 'RetailMax Group', code: 'RETAILMAX', industry: 'Retail', size: '1000+' },
    { name: 'ManufacturePro', code: 'MANUFPRO', industry: 'Manufacturing', size: '501-1000' },
    { name: 'ConsultingPlus', code: 'CONSULTPLUS', industry: 'Consulting', size: '51-200' }
  ];

  const randomCompany = companies[Math.floor(Math.random() * companies.length)];
  const timestamp = Date.now().toString().slice(-6);
  const randomSuffix = Math.random().toString(36).substring(2, 5).toUpperCase();

  return {
    id: `mock_employer_${timestamp}_${randomSuffix}`,
    company_name: randomCompany.name,
    company_code: `${randomCompany.code}_${timestamp}`,
    industry: randomCompany.industry,
    company_size: randomCompany.size
  };
};

/**
 * Registration Service Class
 */
export class RegistrationService {

  /**
   * Validate activation code and get invitation details
   */
  static async validateActivationCode(activationCode: string, requestId: string): Promise<EmployeeInvitation> {
    // Create business operation logger for activation code validation
    const validationLogger = createBusinessLogger('activation_code_validation', 'validation_start', {
      requestId,
      activationCode,
      operation: 'activation_code_validation',
    });

    // Create performance tracking
    const { logger: perfLogger, startTracking, endTracking } = createPerformanceLogger('activation_code_validation', {
      requestId,
      activationCode,
    });

    const performanceStart = startTracking();

    // Use perfLogger for performance tracking
    perfLogger.info('Starting activation code validation', {
      step: 'validation_start',
      activationCode: activationCode.substring(0, 8) + '...' // Partial code for security
    });

    try {
      validationLogger.info('Starting activation code validation', {
        step: 'validation_start',
        activationCode,
        validationType: 'employee_invitation',
      });

      // Create database operation logger
      const dbLogger = createDatabaseLogger('SELECT', 'employee_invitations', 'employer_service', {
        requestId,
        operation: 'activation_code_lookup',
      });

      const query = `
        SELECT
          id, employer_id, email, first_name, last_name, department, job_title,
          employee_id, activation_code, invitation_sent_at, activation_code_used_at,
          expires_at, status, created_by, metadata
        FROM employer_service.employee_invitations
        WHERE activation_code = $1
      `;

      dbLogger.info('Executing activation code lookup query', {
        step: 'database_query',
        queryType: 'SELECT',
        table: 'employee_invitations',
        schema: 'employer_service',
      });

      const result = await pool.query(query, [activationCode]);
      const performanceEnd = endTracking(performanceStart);

      dbLogger.info('Database query completed', {
        step: 'database_query_complete',
        rowsReturned: result.rows?.length || 0,
        ...performanceEnd,
      });

      if (!result.rows || result.rows.length === 0) {
        // Create security audit log for invalid activation code attempt
        const securityLogger = createSecurityLogger('invalid_activation_code_attempt', 'employee_invitation', activationCode, {
          requestId,
          suspiciousActivity: true,
        });

        securityLogger.warn('Invalid activation code attempted', {
          step: 'security_validation_failed',
          activationCode,
          attemptSource: 'member_registration',
          securityRisk: 'low',
        });

        throw new ServiceError(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Invalid activation code',
          { activationCode }
        );
      }

      const invitation = result.rows![0];

      // Business rule validation with detailed logging
      validationLogger.info('Performing business rule validation', {
        step: 'business_rule_validation',
        invitationId: invitation.id,
        employerId: invitation.employer_id,
        email: invitation.email,
        status: invitation.status,
        expiresAt: invitation.expires_at,
        alreadyUsed: !!invitation.activation_code_used_at,
      });

      // Check if already used
      if (invitation.activation_code_used_at) {
        validationLogger.warn('Activation code already used', {
          step: 'validation_failed_already_used',
          activationCode,
          usedAt: invitation.activation_code_used_at,
          businessRule: 'single_use_activation_code',
        });

        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          'Activation code has already been used',
          { activationCode, usedAt: invitation.activation_code_used_at }
        );
      }

      // Check if expired
      if (new Date() > new Date(invitation.expires_at)) {
        validationLogger.warn('Activation code expired', {
          step: 'validation_failed_expired',
          activationCode,
          expiresAt: invitation.expires_at,
          currentTime: new Date().toISOString(),
          businessRule: 'activation_code_expiry',
        });

        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          'Activation code has expired',
          { activationCode, expiresAt: invitation.expires_at }
        );
      }

      // Check status
      if (invitation.status !== 'sent' && invitation.status !== 'pending') {
        validationLogger.warn('Activation code invalid status', {
          step: 'validation_failed_status',
          activationCode,
          status: invitation.status,
          allowedStatuses: ['sent', 'pending'],
          businessRule: 'invitation_status_validation',
        });

        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          `Invitation is not active (status: ${invitation.status})`,
          { activationCode, status: invitation.status }
        );
      }

      // Log successful validation with comprehensive context
      validationLogger.info('Activation code validation successful', {
        step: 'validation_success',
        activationCode,
        employerId: invitation.employer_id,
        email: invitation.email,
        invitationId: invitation.id,
        department: invitation.department,
        jobTitle: invitation.job_title,
        businessRulesValidated: [
          'activation_code_exists',
          'not_already_used',
          'not_expired',
          'valid_status'
        ],
        ...performanceEnd,
      });

      return invitation;
    } catch (error) {
      const performanceEnd = endTracking(performanceStart);

      if (error instanceof ServiceError) {
        // Log business validation errors with context
        validationLogger.warn('Business validation failed', {
          step: 'business_validation_error',
          errorCode: error.code,
          errorMessage: error.message,
          errorDetails: error.details,
          ...performanceEnd,
        });
        throw error;
      }

      // Create enhanced error logger for unexpected errors
      const { logger: errorLogger, errorContext } = createErrorLogger(error as Error, {
        requestId,
        operation: 'activation_code_validation',
        activationCode,
        step: 'unexpected_error',
        ...performanceEnd,
      });

      errorLogger.error('Unexpected error during activation code validation', errorContext);

      throw new ServiceError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to validate activation code',
        { activationCode, originalError: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
  }

  /**
   * Mark activation code as used
   */
  static async markActivationCodeUsed(activationCode: string, requestId: string): Promise<void> {
    try {
      const query = `
        UPDATE employer_service.employee_invitations
        SET
          activation_code_used_at = NOW(),
          status = 'activated'
        WHERE activation_code = $1
      `;

      const result = await pool.query(query, [activationCode]);

      if (result.rowCount === 0) {
        throw new ServiceError(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Activation code not found for update',
          { activationCode }
        );
      }

      logger.info('Activation code marked as used', { requestId, activationCode });
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }

      logger.error('Error marking activation code as used', {
        requestId,
        activationCode,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw new ServiceError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to mark activation code as used',
        { activationCode }
      );
    }
  }
  /**
   * Create and confirm Cognito user for employee invitation
   */
  static async createAndConfirmCognitoUser(
    data: RegistrationData,
    requestId: string
  ): Promise<{ cognitoUserSub: string; password: string }> {
    const cognitoLogger = createOperationLogger('cognito_user_creation', {
      requestId,
      email: data.email,
      component: 'cognito-service'
    });

    try {
      cognitoLogger.info('Starting Cognito user creation for employee', {
        step: 'cognito_signup_start',
        email: data.email,
        phoneNumber: data.phone ? `${data.phoneCountryCode}${data.phone}` : undefined
      });

      // Use phone number as username if available, otherwise email
      const phoneNumber = data.phone ? `${data.phoneCountryCode}${data.phone}` : undefined;
      const username = phoneNumber || data.email;
      const password = generateSecurePassword();
      const secretHash = calculateSecretHash(username);

      // Create SignUp command for employee (role ID 3)
      const signUpCommand = new SignUpCommand({
        ClientId: process.env.COGNITO_CLIENT_ID || process.env.CLIENT_ID!,
        SecretHash: secretHash,
        Password: password,
        Username: username,
        UserAttributes: [
          { Name: 'email', Value: data.email },
          { Name: 'custom:roleId', Value: '3' }, // Employee role ID
          ...(phoneNumber ? [{ Name: 'phone_number', Value: phoneNumber }] : []),
        ],
      });

      // Execute Cognito SignUp
      const signUpResponse = await cognitoClient.send(signUpCommand);

      if (!signUpResponse.UserSub) {
        throw new Error('Failed to get UserSub from Cognito response');
      }

      cognitoLogger.info('Cognito user created successfully', {
        step: 'cognito_signup_success',
        userSub: signUpResponse.UserSub,
        username: username
      });

      // Update user attributes to mark email/phone as verified
      const updateAttributesCommand = new AdminUpdateUserAttributesCommand({
        UserPoolId: process.env.COGNITO_USER_POOL_ID || process.env.USERPOOLID!,
        Username: username,
        UserAttributes: [
          { Name: 'email_verified', Value: 'true' },
          ...(phoneNumber ? [{ Name: 'phone_number_verified', Value: 'true' }] : []),
        ],
      });

      await cognitoClient.send(updateAttributesCommand);

      cognitoLogger.info('User attributes updated successfully', {
        step: 'cognito_attributes_updated',
        userSub: signUpResponse.UserSub
      });

      // Confirm the user account
      const confirmCommand = new AdminConfirmSignUpCommand({
        UserPoolId: process.env.COGNITO_USER_POOL_ID || process.env.USERPOOLID!,
        Username: username,
      });

      await cognitoClient.send(confirmCommand);

      cognitoLogger.info('Cognito user confirmed successfully', {
        step: 'cognito_confirmation_success',
        userSub: signUpResponse.UserSub,
        username: username,
        status: 'confirmed'
      });

      return {
        cognitoUserSub: signUpResponse.UserSub,
        password: password
      };

    } catch (error: any) {
      cognitoLogger.error('Failed to create and confirm Cognito user', {
        step: 'cognito_error',
        error: error.message,
        errorName: error.name,
        errorCode: error.code
      });

      // Handle specific Cognito errors
      if (error.name === 'UsernameExistsException') {
        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          'An account with this email or phone number already exists',
          { email: data.email, phone: data.phone }
        );
      }

      throw new ServiceError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to create user account in Cognito',
        { originalError: error.message }
      );
    }
  }

  /**
   * Register a new member/employee
   */
  static async registerMember(
    data: RegistrationData,
    requestId: string
  ): Promise<{ member: Member; message: string }> {
    // Create registration-scoped logger with comprehensive context
    const registrationLogger = createOperationLogger('user_registration', {
      requestId,
      correlationId: `corr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      userEmail: data.email,
      employerId: data.employerId,
      activationCode: data.activationCode,
      component: 'registration-service'
    });

    try {
      registrationLogger.info('Starting member registration', {
        step: 'registration_start',
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        activationCode: data.activationCode,
        hasPhoneNumber: !!data.phone,
        hasAddress: !!(data.streetAddress || data.city || data.state),
        registrationSource: 'web-portal'
      });

      // Debug: Test database connection first
      const dbTestLogger = createDatabaseLogger('connection_test', undefined, undefined, {
        requestId,
        operation: 'user_registration',
        component: 'database-connection'
      });

      dbTestLogger.info('Testing database connection', {
        step: 'db_connection_test',
        testQuery: 'SELECT NOW() as current_time'
      });

      try {
        const testResult = await dbConnection.query('SELECT NOW() as current_time');
        dbTestLogger.info('Database connection successful', {
          step: 'db_connection_success',
          currentTime: testResult.rows[0]?.current_time,
          responseTime: 'immediate',
          connectionStatus: 'healthy'
        });
      } catch (dbError) {
        dbTestLogger.error('Database connection test failed', {
          step: 'db_connection_error',
          error: dbError instanceof Error ? dbError.message : 'Unknown error',
          stack: dbError instanceof Error ? dbError.stack : undefined,
          errorCode: (dbError as any)?.code,
          errorDetail: (dbError as any)?.detail,
          errorName: dbError instanceof Error ? dbError.name : 'Unknown',
          errorConstructor: dbError?.constructor?.name,
          // Additional PostgreSQL error details
          pgErrorCode: (dbError as any)?.code,
          pgErrorDetail: (dbError as any)?.detail,
          pgErrorHint: (dbError as any)?.hint,
          pgErrorPosition: (dbError as any)?.position,
          pgErrorWhere: (dbError as any)?.where,
          pgErrorFile: (dbError as any)?.file,
          pgErrorLine: (dbError as any)?.line,
          pgErrorRoutine: (dbError as any)?.routine
        });
        throw new ServiceError(
          ErrorCode.DATABASE_ERROR,
          'Database connection failed',
          { originalError: dbError instanceof Error ? dbError.message : 'Unknown error' }
        );
      }

      // Validate required fields
      const requiredFields = ['firstName', 'lastName', 'email'];
      for (const field of requiredFields) {
        if (!data[field as keyof RegistrationData] || (data[field as keyof RegistrationData] as string).trim() === '') {
          throw new ServiceError(
            ErrorCode.MISSING_REQUIRED_FIELD,
            `${field} is required`,
            { field }
          );
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          'Invalid email format',
          { field: 'email' }
        );
      }

      // Validate activation code if provided
      let invitation: EmployeeInvitation | null = null;
      if (data.activationCode) {
        invitation = await this.validateActivationCode(data.activationCode, requestId);

        // Verify email matches invitation
        if (invitation.email !== data.email) {
          throw new ServiceError(
            ErrorCode.VALIDATION_ERROR,
            'Email does not match the invitation',
            {
              providedEmail: data.email,
              invitationEmail: invitation.email,
              activationCode: data.activationCode
            }
          );
        }
      }

      // Check if email already exists in members table
      const emailCheckQuery = `
        SELECT id, email FROM member_service.members WHERE email = $1
      `;
      const emailCheckResult = await pool.query(emailCheckQuery, [data.email]);

      if (emailCheckResult.rows && emailCheckResult.rows.length > 0) {
        throw new ServiceError(
          ErrorCode.RESOURCE_ALREADY_EXISTS,
          'An account with this email already exists',
          { field: 'email' }
        );
      }

      // Note: Password handling removed for passwordless authentication
      logger.info('Passwordless registration - no password hashing needed', { requestId });

      // Format and validate registration data
      const { formattedData, address, fullPhone } = RegistrationService.formatRegistrationDataForDB(data);

      // Additional validation
      const validation = RegistrationService.validateRegistrationData(data);
      if (!validation.isValid) {
        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          'Registration data validation failed',
          { errors: validation.errors }
        );
      }

      // Use employer_id from invitation, provided data, or generate mock employer
      let employerId: string;
      if (invitation?.employer_id) {
        employerId = invitation.employer_id;
        logger.info('Using employer ID from invitation', { requestId, employerId });
      } else if (data.employerId) {
        logger.info('Validating provided employer ID', {
          requestId,
          providedEmployerId: data.employerId
        });

        // Check if the provided employer ID is a valid UUID format
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (uuidRegex.test(data.employerId)) {
          employerId = data.employerId;
          logger.info('Valid UUID employer ID provided', { requestId, employerId });
        } else {
          // Handle simple numeric IDs by mapping to known employers
          logger.info('Non-UUID employer ID provided, mapping to default employer', {
            requestId,
            providedId: data.employerId
          });

          // Map simple IDs to actual UUIDs
          const employerMapping: { [key: string]: string } = {
            '1': '550e8400-e29b-41d4-a716-************', // Aperion Health
            '2': 'c14a6f74-a5f8-4879-9025-e3d7e1d7d2f0'  // TechCorp Solutions
          };

          employerId = employerMapping[data.employerId] || '550e8400-e29b-41d4-a716-************'; // Default to Aperion Health
          logger.info('Mapped employer ID', {
            requestId,
            originalId: data.employerId,
            mappedId: employerId
          });
        }
      } else {
        // Use default employer (Aperion Health) instead of generating mock employer
        employerId = '550e8400-e29b-41d4-a716-************';
        logger.info('No employer ID provided, using default employer (Aperion Health)', {
          requestId,
          employerId
        });
      }

      // Create Cognito user for employee invitations (if this is an employee invitation)
      let cognitoUserSub: string;
      let isEmployeeInvitation = false;

      if (invitation) {
        // This is an employee invitation - create and confirm Cognito user
        isEmployeeInvitation = true;
        registrationLogger.info('Creating Cognito user for employee invitation', {
          step: 'cognito_user_creation',
          email: formattedData.email,
          invitationId: invitation.id
        });

        const cognitoResult = await RegistrationService.createAndConfirmCognitoUser(data, requestId);
        cognitoUserSub = cognitoResult.cognitoUserSub;

        registrationLogger.info('Cognito user created and confirmed for employee', {
          step: 'cognito_user_confirmed',
          cognitoUserSub: cognitoUserSub,
          email: formattedData.email
        });
      } else {
        // Regular registration - use temporary ID (will be updated later when Cognito user is created)
        cognitoUserSub = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      }

      // Create member in database transaction using shared utilities
      const result: Member = await dbConnection.transaction(async (client) => {

          // Insert member into database
          logger.info('Step 4: Preparing member insertion', {
            requestId,
            employerId,
            cognitoUserSub,
            isEmployeeInvitation,
            memberData: {
              firstName: formattedData.firstName,
              lastName: formattedData.lastName,
              email: formattedData.email,
              phone: fullPhone,
              dateOfBirth: formattedData.dateOfBirth,
              gender: formattedData.gender,
              hasAddress: !!address,
              activationCode: formattedData.activationCode || 'none',
              status: isEmployeeInvitation ? 'active' : (formattedData.activationCode ? 'pending_activation' : 'active')
            }
          });

          const insertMemberQuery = `
            INSERT INTO member_service.members (
              cognito_user_id, employer_id, activation_code, first_name, last_name,
              email, phone, date_of_birth, gender, address, status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id, cognito_user_id, employer_id, activation_code, first_name,
                     last_name, email, phone, date_of_birth, gender, address,
                     status, created_at, updated_at
          `;

          const memberValues = [
            cognitoUserSub,
            employerId,
            formattedData.activationCode,
            formattedData.firstName,
            formattedData.lastName,
            formattedData.email,
            fullPhone,
            formattedData.dateOfBirth ? RegistrationService.normalizeDateForDB(formattedData.dateOfBirth) : null,
            formattedData.gender,
            address ? JSON.stringify(address) : null,
            isEmployeeInvitation ? 'active' : (formattedData.activationCode ? 'pending_activation' : 'active')
          ];

          logger.info('Step 4: Executing member insertion query', {
            requestId,
            query: insertMemberQuery.replace(/\s+/g, ' ').trim(),
            parameterCount: memberValues.length,
            parameters: memberValues.map((val, idx) => {
              if (val === null) return `$${idx + 1}: NULL`;
              if (typeof val === 'string' && val.length > 50) return `$${idx + 1}: "${val.substring(0, 50)}..."`;
              if (val instanceof Date) return `$${idx + 1}: ${val.toISOString()}`;
              return `$${idx + 1}: ${JSON.stringify(val)}`;
            })
          });

          const memberResult = await client.query(insertMemberQuery, memberValues);

          logger.info('Step 4 Complete: Member insertion successful', {
            requestId,
            insertedMemberId: memberResult.rows[0]?.id,
            rowCount: memberResult.rowCount,
            returningFields: Object.keys(memberResult.rows[0] || {})
          });
          const memberRow = memberResult.rows![0];

          // Mark activation code as used if provided
          if (formattedData.activationCode && invitation) {
            await client.query(`
              UPDATE employer_service.employee_invitations
              SET activation_code_used_at = NOW(), status = 'activated'
              WHERE activation_code = $1
            `, [formattedData.activationCode]);
          }

          // Create user reference in shared_data schema
          const userRefQuery = `
            INSERT INTO shared_data.user_references (
              cognito_user_id, user_type, service_user_id, email, first_name, last_name, status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
          `;

          await client.query(userRefQuery, [
            cognitoUserSub,
            'member',
            memberRow.id,
            formattedData.email,
            formattedData.firstName,
            formattedData.lastName,
            'active'
          ]);

          // Format and return result
          const result: Member = {
            id: memberRow.id,
            cognito_user_id: memberRow.cognito_user_id,
            employer_id: memberRow.employer_id,
            activation_code: memberRow.activation_code,
            first_name: memberRow.first_name,
            last_name: memberRow.last_name,
            email: memberRow.email,
            phone: memberRow.phone,
            date_of_birth: memberRow.date_of_birth,
            gender: memberRow.gender,
            address: memberRow.address,
            status: memberRow.status,
            created_at: memberRow.created_at,
            updated_at: memberRow.updated_at
          };

          logger.info('Member created successfully in database', {
            requestId,
            memberId: result.id,
            email: result.email,
            employerId: result.employer_id,
            activationCode: data.activationCode
          });

          return result;
        });

      logger.info('Member registration successful', {
        requestId,
        memberId: result.id,
        email: formattedData.email,
        employerId: result.employer_id,
        isEmployeeInvitation,
        cognitoUserConfirmed: isEmployeeInvitation
      });

      return {
        member: result,
        message: isEmployeeInvitation
          ? 'Employee registration completed successfully. Your account has been confirmed and you can now sign in.'
          : 'Registration completed successfully'
      };

    } catch (error) {
      // Re-throw ServiceError as-is
      if (error instanceof ServiceError) {
        logger.error('Registration service error occurred', {
          requestId,
          errorCode: error.code,
          errorMessage: error.message,
          errorDetails: error.details,
        });
        throw error;
      }

      // Handle database errors
      if (error instanceof Error && (
        error.message.includes('connect') ||
        error.message.includes('connection') ||
        error.message.includes('syntax') ||
        error.message.includes('relation') ||
        error.message.includes('duplicate key')
      )) {
        logger.error('Database error during registration', {
          requestId,
          error: error.message,
          stack: error.stack,
          errorCode: (error as any)?.code,
          errorDetail: (error as any)?.detail,
          errorHint: (error as any)?.hint,
          errorPosition: (error as any)?.position,
          errorInternalPosition: (error as any)?.internalPosition,
          errorInternalQuery: (error as any)?.internalQuery,
          errorWhere: (error as any)?.where,
          errorSchema: (error as any)?.schema,
          errorTable: (error as any)?.table,
          errorColumn: (error as any)?.column,
          errorConstraint: (error as any)?.constraint,
          errorFile: (error as any)?.file,
          errorLine: (error as any)?.line,
          errorRoutine: (error as any)?.routine,
        });
        throw new ServiceError(
          ErrorCode.DATABASE_ERROR,
          'Database operation failed during registration',
          { originalError: error.message }
        );
      }

      // Generic error handling with comprehensive logging
      logger.error('Failed to register member - COMPREHENSIVE ERROR DETAILS', {
        requestId,
        errorName: error instanceof Error ? error.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : undefined,
        errorType: typeof error,
        errorConstructor: error?.constructor?.name,
        // PostgreSQL specific error details
        pgErrorCode: (error as any)?.code,
        pgErrorDetail: (error as any)?.detail,
        pgErrorHint: (error as any)?.hint,
        pgErrorPosition: (error as any)?.position,
        pgErrorInternalPosition: (error as any)?.internalPosition,
        pgErrorInternalQuery: (error as any)?.internalQuery,
        pgErrorWhere: (error as any)?.where,
        pgErrorSchema: (error as any)?.schema,
        pgErrorTable: (error as any)?.table,
        pgErrorColumn: (error as any)?.column,
        pgErrorDataType: (error as any)?.dataType,
        pgErrorConstraint: (error as any)?.constraint,
        pgErrorFile: (error as any)?.file,
        pgErrorLine: (error as any)?.line,
        pgErrorRoutine: (error as any)?.routine,
        // Additional error properties
        errorSeverity: (error as any)?.severity,
        errorLength: (error as any)?.length,
        errorQuery: (error as any)?.query,
        errorParameters: (error as any)?.parameters,
        // Full error object for debugging
        fullErrorObject: JSON.stringify(error, Object.getOwnPropertyNames(error)),
      });

      throw new ServiceError(
        ErrorCode.INTERNAL_ERROR,
        'Registration failed due to an unexpected error',
        { originalError: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
  }



  /**
   * Create a mock employer in the database for testing purposes
   */
  static async createMockEmployerInDB(mockEmployer: MockEmployer, requestId?: string): Promise<string> {
    try {
      // Check if employer already exists
      const checkQuery = `
        SELECT id FROM employer_service.employers WHERE id = $1
      `;
      const existingResult = await pool.query(checkQuery, [mockEmployer.id]);

      if (existingResult.rows && existingResult.rows.length > 0) {
        logger.info('Mock employer already exists in database', {
          requestId: requestId || 'mock',
          employerId: mockEmployer.id
        });
        return mockEmployer.id;
      }

      // Create mock employer in database
      const insertEmployerQuery = `
        INSERT INTO employer_service.employers (
          id, cognito_user_id, company_name, company_code, industry, company_size,
          contact_email, subscription_plan, subscription_status, max_members, current_members
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING id
      `;

      const tempCognitoId = `temp_employer_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      const employerValues = [
        mockEmployer.id,
        tempCognitoId,
        mockEmployer.company_name,
        mockEmployer.company_code,
        mockEmployer.industry,
        mockEmployer.company_size,
        `hr@${mockEmployer.company_code.toLowerCase()}.com`,
        'basic',
        'active',
        100,
        0
      ];

      await pool.query(insertEmployerQuery, employerValues);

      // Also create user reference for the employer
      const userRefQuery = `
        INSERT INTO shared_data.user_references (
          cognito_user_id, user_type, service_user_id, email, first_name, last_name, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      `;

      await pool.query(userRefQuery, [
        tempCognitoId,
        'employer',
        mockEmployer.id,
        `hr@${mockEmployer.company_code.toLowerCase()}.com`,
        'HR',
        'Admin',
        'active'
      ]);

      logger.info('Mock employer created in database', {
        requestId: requestId || 'mock',
        employerId: mockEmployer.id,
        companyName: mockEmployer.company_name,
        companyCode: mockEmployer.company_code
      });

      return mockEmployer.id;
    } catch (error) {
      logger.error('Error creating mock employer in database', {
        requestId: requestId || 'mock',
        employerId: mockEmployer.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Return the employer ID anyway for testing purposes
      return mockEmployer.id;
    }
  }

  /**
   * Create a mock activation code for testing purposes
   */
  static async createMockActivationCode(
    email: string,
    firstName?: string,
    lastName?: string,
    requestId?: string
  ): Promise<{ activationCode: string; invitationId: string }> {
    try {
      const activationCode = `TEST-${Date.now().toString().slice(-6)}-${Math.random().toString(36).substring(2, 6).toUpperCase()}`;

      // Insert mock invitation into database
      const insertQuery = `
        INSERT INTO employer_service.employee_invitations (
          employer_id, email, first_name, last_name, department, job_title,
          activation_code, status, created_by, expires_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id, activation_code
      `;

      // Generate mock employer for invitation
      const mockEmployer = generateMockEmployer();
      const employerId = await RegistrationService.createMockEmployerInDB(mockEmployer, requestId);

      const result = await pool.query(insertQuery, [
        employerId,
        email,
        firstName || 'Test',
        lastName || 'User',
        'Testing',
        'Test Employee',
        activationCode,
        'sent',
        employerId, // created_by
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // expires in 30 days
      ]);

      logger.info('Mock activation code created', {
        requestId: requestId || 'mock',
        activationCode,
        email,
        invitationId: result.rows![0].id
      });

      return {
        activationCode,
        invitationId: result.rows![0].id
      };
    } catch (error) {
      logger.error('Error creating mock activation code', {
        requestId: requestId || 'mock',
        email,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw new ServiceError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to create mock activation code',
        { email }
      );
    }
  }

  /**
   * Format registration data for database insertion
   */
  static formatRegistrationDataForDB(data: RegistrationData): {
    formattedData: any;
    address: any;
    fullPhone: string | null;
  } {
    // Format phone number
    const fullPhone = data.phone && data.phone.trim()
      ? `${data.phoneCountryCode}${data.phone.replace(/[\s\-\(\)]/g, '')}`
      : null;

    // Format address object
    const address = data.streetAddress ? {
      streetAddress: data.streetAddress.trim(),
      apartmentUnit: data.apartmentUnit?.trim() || null,
      city: data.city?.trim() || null,
      state: data.state?.trim() || null,
      zipCode: data.zipCode?.trim() || null,
      country: data.country?.trim() || 'United States'
    } : null;

    // Format basic data
    const formattedData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      email: data.email.trim().toLowerCase(),
      phoneCountryCode: data.phoneCountryCode.trim(),
      phone: data.phone?.trim() || null,
      dateOfBirth: data.dateOfBirth?.trim() || null,
      gender: data.gender || null,
      employerId: data.employerId?.trim() || null,
      companyReference: data.companyReference?.trim() || null,
      activationCode: data.activationCode?.trim() || null
    };

    return {
      formattedData,
      address,
      fullPhone
    };
  }

  /**
   * Normalize date format for database storage
   */
  static normalizeDateForDB(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date');
      }
      // Return yyyy-MM-dd format for PostgreSQL DATE column
      return date.toISOString().split('T')[0];
    } catch (error) {
      logger.warn('Failed to normalize date format', {
        originalDate: dateString,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Return original string if normalization fails
      return dateString;
    }
  }

  /**
   * Validate registration data
   */
  static validateRegistrationData(data: RegistrationData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required field validation
    if (!data.firstName?.trim()) errors.push('First name is required');
    if (!data.lastName?.trim()) errors.push('Last name is required');
    if (!data.email?.trim()) errors.push('Email is required');

    // Name format validation
    const nameRegex = /^[a-zA-Z\s'-]+$/;
    if (data.firstName && !nameRegex.test(data.firstName.trim())) {
      errors.push('First name can only contain letters, spaces, hyphens, and apostrophes');
    }
    if (data.lastName && !nameRegex.test(data.lastName.trim())) {
      errors.push('Last name can only contain letters, spaces, hyphens, and apostrophes');
    }

    // Name length validation
    if (data.firstName && data.firstName.trim().length > 100) {
      errors.push('First name must be less than 100 characters');
    }
    if (data.lastName && data.lastName.trim().length > 100) {
      errors.push('Last name must be less than 100 characters');
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (data.email && !emailRegex.test(data.email)) {
      errors.push('Invalid email format');
    }
    if (data.email && data.email.length > 255) {
      errors.push('Email must be less than 255 characters');
    }

    // Phone country code validation
    if (data.phoneCountryCode && !/^\+\d{1,4}$/.test(data.phoneCountryCode)) {
      errors.push('Invalid phone country code format');
    }

    // Phone validation (if provided)
    if (data.phone && data.phone.trim()) {
      const phoneRegex = /^[\d\s\-\(\)]+$/;
      if (!phoneRegex.test(data.phone)) {
        errors.push('Invalid phone number format');
      }
      if (data.phone.length > 20) {
        errors.push('Phone number must be less than 20 characters');
      }
    }

    // Date of birth validation (if provided)
    if (data.dateOfBirth) {
      const dob = new Date(data.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - dob.getFullYear();

      if (isNaN(dob.getTime())) {
        errors.push('Invalid date of birth format');
      } else if (age < 13 || age > 120) {
        errors.push('Invalid date of birth - age must be between 13 and 120');
      }
    }

    // Gender validation (if provided)
    if (data.gender && !['male', 'female', 'other', 'prefer_not_to_say'].includes(data.gender)) {
      errors.push('Invalid gender selection');
    }

    // Address validation (if provided)
    if (data.streetAddress && data.streetAddress.length > 200) {
      errors.push('Street address must be less than 200 characters');
    }
    if (data.city && data.city.length > 100) {
      errors.push('City must be less than 100 characters');
    }
    if (data.state && data.state.length > 100) {
      errors.push('State must be less than 100 characters');
    }
    if (data.zipCode && data.zipCode.length > 20) {
      errors.push('ZIP code must be less than 20 characters');
    }
    if (data.country && !['United States', 'India'].includes(data.country)) {
      errors.push('Country must be either United States or India');
    }

    // System fields validation
    if (data.employerId && data.employerId.length > 50) {
      errors.push('Employer ID must be less than 50 characters');
    }
    if (data.companyReference && data.companyReference.length > 100) {
      errors.push('Company reference must be less than 100 characters');
    }
    if (data.activationCode && data.activationCode.length > 50) {
      errors.push('Activation code must be less than 50 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
