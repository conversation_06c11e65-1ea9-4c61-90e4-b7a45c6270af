import crypto from 'crypto';

const ALGORITHM = 'aes-256-cbc';
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY
    ? Buffer.from(process.env.ENCRYPTION_KEY, 'hex')
    : crypto.randomBytes(32); // fallback

export const secretHash = (username: string): string => {
    return crypto.createHmac('SHA256', process.env.CLIENT_SECRET || '')
        .update(username + process.env.CLIENT_ID)
        .digest('base64');
};

export const encryptData = (text: string, expirationHours = 24): string => {
    const iv = crypto.randomBytes(16);
    const timestamp = Date.now();
    const expirationTime = timestamp + expirationHours * 60 * 60 * 1000;

    const payload = JSON.stringify({ data: text, exp: expirationTime });

    const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
    let encrypted = cipher.update(payload, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
};

export const decryptData = (encryptedText: string): string => {
    const [ivHex, encrypted] = encryptedText.split(':');
    if (!ivHex || !encrypted) throw new Error('Invalid encrypted format');

    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv(ALGORITHM, ENCRYPTION_KEY, iv);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    const payload = JSON.parse(decrypted);
    if (Date.now() > payload.exp) {
        const error = new Error('Link has expired');
        (error as any).code = 'LINK_EXPIRED';
        throw error;
    }

    return payload.data;
};

export const isLinkExpired = (encryptedText: string): boolean => {
    try {
        const [ivHex, encrypted] = encryptedText.split(':');
        if (!ivHex || !encrypted) return true;

        const iv = Buffer.from(ivHex, 'hex');
        const decipher = crypto.createDecipheriv(ALGORITHM, ENCRYPTION_KEY, iv);

        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        const payload = JSON.parse(decrypted);
        return Date.now() > payload.exp;
    } catch {
        return true;
    }
};
