import { Router, Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';
import { TokenUtils } from '@aperion/shared/server';
import { validatePasswordStrength, HttpStatus, ErrorCode, asyncHandler } from '@aperion/shared';
import { config } from '../config';
import { logger } from '../utils/logger';
import { AppError } from '../middleware/errorHandler';

const router = Router();

// Validation middleware
const loginValidation = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 1 }).withMessage('Password is required'),
];

const signupValidation = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  body('firstName').isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName').isLength({ min: 1 }).withMessage('Last name is required'),
];

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(HttpStatus.BAD_REQUEST).json({
      success: false,
      error: {
        code: ErrorCode.VALIDATION_ERROR,
        message: 'Validation failed',
        details: errors.array(),
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      },
    });
    return;
  }
  next();
};

// Login endpoint
router.post('/login', loginValidation, handleValidationErrors, asyncHandler(async (req: Request, _res: Response) => {
  const { email } = req.body;

  logger.info('Login attempt', {
    requestId: req.requestId,
    email,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  try {
    // TODO: Integrate with AWS Cognito for real authentication
    // For now, this endpoint will return an authentication error

    // Invalid credentials
    logger.warn('Login failed - invalid credentials', {
      requestId: req.requestId,
      email,
      ip: req.ip,
    });

    throw new AppError(
      'Invalid email or password',
      HttpStatus.UNAUTHORIZED,
      ErrorCode.AUTHENTICATION_FAILED
    );

  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Login error', {
      requestId: req.requestId,
      email,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    throw new AppError(
      'Authentication service unavailable',
      HttpStatus.SERVICE_UNAVAILABLE,
      ErrorCode.EXTERNAL_SERVICE_ERROR
    );
  }
}));

// Signup endpoint
router.post('/signup', signupValidation, handleValidationErrors, asyncHandler(async (req: Request, res: Response) => {
  const { email, password, firstName, lastName, activationCode } = req.body;

  logger.info('Signup attempt', {
    requestId: req.requestId,
    email,
    firstName,
    lastName,
    hasActivationCode: !!activationCode,
    ip: req.ip,
  });

  // Validate password strength
  const passwordValidation = validatePasswordStrength(password);
  if (!passwordValidation.isValid) {
    throw new AppError(
      `Password validation failed: ${passwordValidation.errors.join(', ')}`,
      HttpStatus.BAD_REQUEST,
      ErrorCode.VALIDATION_ERROR
    );
  }

  try {
    // TODO: Integrate with AWS Cognito and activation code validation
    // For now, return a mock response

    // Mock activation code validation
    if (activationCode && activationCode !== 'MOCK1234') {
      throw new AppError(
        'Invalid or expired activation code',
        HttpStatus.BAD_REQUEST,
        ErrorCode.ACTIVATION_CODE_INVALID
      );
    }

    // Mock user creation
    const userId = 'mock-new-user-id';
    const userRole = activationCode ? 'member' : 'employer';
    const userService = activationCode ? 'member-service' : 'employer-service';

    logger.info('Signup successful', {
      requestId: req.requestId,
      userId,
      email,
      role: userRole,
      service: userService,
    });

    res.status(HttpStatus.CREATED).json({
      success: true,
      data: {
        message: 'Account created successfully',
        user: {
          id: userId,
          email,
          firstName,
          lastName,
          role: userRole,
          service: userService,
          status: 'pending_verification',
        },
        nextStep: 'Please check your email for verification instructions',
      },
    });
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Signup error', {
      requestId: req.requestId,
      email,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    throw new AppError(
      'Account creation failed',
      HttpStatus.INTERNAL_SERVER_ERROR,
      ErrorCode.EXTERNAL_SERVICE_ERROR
    );
  }
}));

// Refresh token endpoint
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new AppError(
      'Refresh token is required',
      HttpStatus.BAD_REQUEST,
      ErrorCode.MISSING_REQUIRED_FIELD
    );
  }

  try {
    // Verify refresh token
    const decoded = TokenUtils.verifyToken(refreshToken, config.jwtSecret);

    // Generate new access token
    const newTokenPayload = {
      sub: decoded.sub,
      email: decoded.email,
      'cognito:groups': decoded['cognito:groups'],
      'custom:role': decoded['custom:role'],
      'custom:service': decoded['custom:service'],
      'custom:permissions': decoded['custom:permissions'],
      iss: decoded.iss,
      aud: decoded.aud,
    };

    const newAccessToken = TokenUtils.generateToken(newTokenPayload, config.jwtSecret, '15m');

    logger.info('Token refreshed', {
      requestId: req.requestId,
      userId: decoded.sub,
    });

    res.json({
      success: true,
      data: {
        accessToken: newAccessToken,
        expiresIn: 900, // 15 minutes
      },
    });
  } catch (error) {
    logger.warn('Token refresh failed', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    throw new AppError(
      'Invalid refresh token',
      HttpStatus.UNAUTHORIZED,
      ErrorCode.INVALID_TOKEN
    );
  }
}));

// Logout endpoint
router.post('/logout', asyncHandler(async (req: Request, res: Response) => {
  // TODO: Implement token blacklisting or Cognito logout

  logger.info('User logged out', {
    requestId: req.requestId,
    ip: req.ip,
  });

  res.json({
    success: true,
    data: {
      message: 'Logged out successfully',
    },
  });
}));

// Verify token endpoint
router.get('/verify', asyncHandler(async (req: Request, res: Response) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new AppError(
      'Authorization token is required',
      HttpStatus.UNAUTHORIZED,
      ErrorCode.AUTHENTICATION_FAILED
    );
  }

  const token = authHeader.substring(7);

  try {
    const decoded = TokenUtils.verifyToken(token, config.jwtSecret);

    res.json({
      success: true,
      data: {
        valid: true,
        user: {
          id: decoded.sub,
          email: decoded.email,
          role: decoded['custom:role'],
          service: decoded['custom:service'],
          permissions: decoded['custom:permissions'],
        },
        expiresAt: new Date(decoded.exp * 1000).toISOString(),
      },
    });
  } catch (error) {
    throw new AppError(
      'Invalid token',
      HttpStatus.UNAUTHORIZED,
      ErrorCode.INVALID_TOKEN
    );
  }
}));

// Registration endpoint - proxy to member service
router.post('/register', asyncHandler(async (req: Request, res: Response) => {
  const requestId = req.requestId || 'unknown';

  try {
    logger.info('Registration request received', {
      requestId,
      email: req.body?.email,
      ip: req.ip,
    });

    // Forward request to member service
    const memberServiceUrl = config.services['member-service'].url;
    const response = await fetch(`${memberServiceUrl}/api/member/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
      },
      body: JSON.stringify(req.body),
    });

    const data: any = await response.json();

    if (!response.ok) {
      logger.warn('Registration failed at member service', {
        requestId,
        status: response.status,
        error: data,
      });

      res.status(response.status).json(data);
      return;
    }

    logger.info('Registration successful', {
      requestId,
      email: req.body?.email,
      memberId: data.data?.member?.id,
    });

    res.status(response.status).json(data);

  } catch (error) {
    logger.error('Registration proxy error', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    throw new AppError(
      'Registration service unavailable',
      HttpStatus.SERVICE_UNAVAILABLE,
      ErrorCode.SERVICE_UNAVAILABLE
    );
  }
}));



// Email availability check endpoint
router.get('/check-email', asyncHandler(async (req: Request, res: Response) => {
  const requestId = req.requestId || 'unknown';

  try {
    const { email } = req.query;

    if (!email) {
      throw new AppError(
        'Email parameter is required',
        HttpStatus.BAD_REQUEST,
        ErrorCode.MISSING_REQUIRED_FIELD
      );
    }

    logger.info('Email availability check', {
      requestId,
      email,
    });

    // Forward request to member service
    const memberServiceUrl = config.services['member-service'].url;
    const response = await fetch(`${memberServiceUrl}/api/member/check-email?email=${encodeURIComponent(email as string)}`, {
      method: 'GET',
      headers: {
        'X-Request-ID': requestId,
      },
    });

    const data: any = await response.json();
    res.status(response.status).json(data);

  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Email check proxy error', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    throw new AppError(
      'Email check service unavailable',
      HttpStatus.SERVICE_UNAVAILABLE,
      ErrorCode.SERVICE_UNAVAILABLE
    );
  }
}));

export { router as authRouter };
