// Quick test for encrypt/decrypt functions
const crypto = require('crypto');
require('dotenv').config();

const ALGORITHM = 'aes-256-cbc';
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY
    ? Buffer.from(process.env.ENCRYPTION_KEY, 'hex')
    : crypto.randomBytes(32);

const encryptData = (text, expirationHours = 24) => {
    const iv = crypto.randomBytes(16);
    const timestamp = Date.now();
    const expirationTime = timestamp + expirationHours * 60 * 60 * 1000;

    const payload = JSON.stringify({ data: text, exp: expirationTime });

    const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
    let encrypted = cipher.update(payload, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
};

const decryptData = (encryptedText) => {
    const [ivHex, encrypted] = encryptedText.split(':');
    if (!ivHex || !encrypted) throw new Error('Invalid encrypted format');

    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv(ALGORITHM, ENCRYPTION_KEY, iv);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    const payload = JSON.parse(decrypted);
    if (Date.now() > payload.exp) {
        const error = new Error('Link has expired');
        error.code = 'LINK_EXPIRED';
        throw error;
    }

    return payload.data;
};

// Test with your data
console.log('=== Crypto Test ===');
const testData = "84388428-20e1-707d-7da8-648c3e902839";
console.log('Original data:', testData);

try {
    // Encrypt
    const encrypted = encryptData(testData);
    console.log('Encrypted:', encrypted);
    
    // Decrypt
    const decrypted = decryptData(encrypted);
    console.log('Decrypted:', decrypted);
    
    // Verify
    console.log('Match:', testData === decrypted ? '✅ SUCCESS' : '❌ FAILED');
    
} catch (error) {
    console.log('Error:', error.message);
}

console.log('=== Test Complete ===');
