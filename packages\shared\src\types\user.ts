import { z } from 'zod';

// User Types
export enum UserRole {
  MEMBER = 'member',
  EMPLOYER = 'employer',
  HR_MANAGER = 'hr-manager',
  WELLNESS_COACH = 'wellness-coach',
  WELLNESS_COORDINATOR = 'wellness-coordinator',
  LEARNER = 'learner',
  CONTENT_CREATOR = 'content-creator',
  LMS_ADMIN = 'lms-admin',
  SYSTEM_ADMIN = 'system-admin',
  OPERATIONS_MANAGER = 'operations-manager',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

// Base User Interface
export interface BaseUser {
  id: string;
  cognitoUserId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: UserRole;
  status: UserStatus;
  createdAt: Date;
  updatedAt: Date;
}

// JWT Token Payload
export interface JWTPayload {
  sub: string; // User ID
  email: string;
  'cognito:groups': string[];
  'custom:role': UserRole;
  'custom:service': string;
  'custom:permissions': string[];
  'custom:employer_id'?: string;
  iss: string;
  aud: string;
  exp: number;
  iat: number;
}

// Service-to-Service Token
export interface ServiceToken {
  iss: string;
  aud: string;
  sub: string;
  scope: string;
  exp: number;
  iat: number;
}

// Validation Schemas
export const emailSchema = z.string().email('Invalid email format');

export const phoneSchema = z
  .string()
  .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
  .optional();

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/\d/, 'Password must contain at least one number');

export const userRoleSchema = z.nativeEnum(UserRole);

export const userStatusSchema = z.nativeEnum(UserStatus);

export const baseUserSchema = z.object({
  id: z.string().uuid(),
  cognitoUserId: z.string(),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: emailSchema,
  phone: phoneSchema,
  role: userRoleSchema,
  status: userStatusSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

// User Creation Schema
export const createUserSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: emailSchema,
  phone: phoneSchema,
  role: userRoleSchema,
});

// User Update Schema
export const updateUserSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phone: phoneSchema,
  status: userStatusSchema.optional(),
});

// Permission System
export const permissions = {
  // Member permissions
  'read:profile': 'Read own profile',
  'update:profile': 'Update own profile',
  'read:health-data': 'Read own health data',
  'update:health-data': 'Update own health data',

  // Employer permissions
  'read:employees': 'Read employee list',
  'create:employees': 'Create employee invitations',
  'manage:company': 'Manage company settings',
  'read:company-analytics': 'Read company analytics',

  // Wellness permissions
  'read:members': 'Read assigned members',
  'create:sessions': 'Create coaching sessions',
  'manage:programs': 'Manage wellness programs',
  'admin:wellness': 'Wellness admin privileges',

  // LMS permissions
  'read:modules': 'Read learning modules',
  'enroll:courses': 'Enroll in courses',
  'take:assessments': 'Take assessments',
  'create:modules': 'Create learning modules',
  'edit:content': 'Edit learning content',
  'manage:assessments': 'Manage assessments',
  'admin:lms': 'LMS admin privileges',
  'manage:users': 'Manage LMS users',
  'view:analytics': 'View LMS analytics',

  // System permissions
  'admin:system': 'System administration',
  'manage:all-users': 'Manage all users',
  'view:all-analytics': 'View all analytics',
  'manage:operations': 'Manage operations',
} as const;

export type Permission = keyof typeof permissions;

// Role-Permission Mapping
export const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.MEMBER]: [
    'read:profile',
    'update:profile',
    'read:health-data',
    'update:health-data',
  ],
  [UserRole.EMPLOYER]: [
    'read:employees',
    'create:employees',
    'manage:company',
    'read:company-analytics',
  ],
  [UserRole.HR_MANAGER]: ['read:employees', 'create:employees'],
  [UserRole.WELLNESS_COACH]: [
    'read:members',
    'create:sessions',
    'manage:programs',
  ],
  [UserRole.WELLNESS_COORDINATOR]: [
    'read:members',
    'create:sessions',
    'manage:programs',
    'admin:wellness',
  ],
  [UserRole.LEARNER]: ['read:modules', 'enroll:courses', 'take:assessments'],
  [UserRole.CONTENT_CREATOR]: [
    'create:modules',
    'edit:content',
    'manage:assessments',
  ],
  [UserRole.LMS_ADMIN]: [
    'admin:lms',
    'manage:users',
    'view:analytics',
    'create:modules',
    'edit:content',
    'manage:assessments',
  ],
  [UserRole.SYSTEM_ADMIN]: [
    'admin:system',
    'manage:all-users',
    'view:all-analytics',
  ],
  [UserRole.OPERATIONS_MANAGER]: ['view:analytics', 'manage:operations'],
};

// Service Mapping
export const roleServiceMapping: Record<UserRole, string> = {
  [UserRole.MEMBER]: 'member-service',
  [UserRole.EMPLOYER]: 'employer-service',
  [UserRole.HR_MANAGER]: 'employer-service',
  [UserRole.WELLNESS_COACH]: 'wellness-central',
  [UserRole.WELLNESS_COORDINATOR]: 'wellness-central',
  [UserRole.LEARNER]: 'zenx-lms',
  [UserRole.CONTENT_CREATOR]: 'zenx-lms',
  [UserRole.LMS_ADMIN]: 'zenx-lms',
  [UserRole.SYSTEM_ADMIN]: 'command-center',
  [UserRole.OPERATIONS_MANAGER]: 'command-center',
};
