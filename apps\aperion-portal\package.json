{"name": "@aperion/portal", "version": "1.0.0", "description": "Aperion Health - Unified Frontend Portal", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@aperion/shared": "*", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.0.4", "@radix-ui/react-label": "^2.0.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^1.2.0", "@radix-ui/react-slot": "^1.0.1", "@radix-ui/react-tabs": "^1.0.3", "@radix-ui/react-toast": "^1.1.3", "@tanstack/react-query": "^4.24.0", "axios": "^1.3.0", "class-variance-authority": "^0.4.0", "clsx": "^1.2.1", "framer-motion": "^10.18.0", "lucide-react": "^0.323.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.57.0", "react-router-dom": "^6.8.0", "tailwind-merge": "^1.10.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.13", "eslint": "^8.35.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "^4.9.3", "vite": "^4.1.0"}}