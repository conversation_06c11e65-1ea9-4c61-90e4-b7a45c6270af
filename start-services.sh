#!/bin/bash

# Aperion Health - Service Startup Script
# Starts all required services in the correct order

set -e

echo "🚀 Starting Aperion Health Services"
echo "===================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "packages/shared" ]; then
    print_error "Please run this script from the Aperion Health project root directory"
    exit 1
fi

# Check if shared package is built
if [ ! -d "packages/shared/dist/cjs" ] || [ ! -d "packages/shared/dist/esm" ]; then
    print_error "Shared package not built. Please run ./setup-aperion-fix.sh first"
    exit 1
fi

print_info "This script will start services in separate terminal windows/tabs"
print_info "Make sure you have multiple terminals available"
echo ""

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to wait for service to start
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_info "Waiting for $service_name to start on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if check_port $port; then
            print_success "$service_name started successfully on port $port"
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start on port $port after $max_attempts attempts"
    return 1
}

echo "Starting services in order..."
echo ""

# 1. Start API Gateway
print_info "1. Starting API Gateway (port 3000)"
if check_port 3000; then
    print_warning "Port 3000 already in use - API Gateway may already be running"
else
    echo "Run this command in a new terminal:"
    echo "cd packages/api-gateway && npm run dev"
    echo ""
    read -p "Press Enter after starting API Gateway..."
    wait_for_service 3000 "API Gateway"
fi
echo ""

# 2. Start Command Center
print_info "2. Starting Command Center (port 3005)"
if check_port 3005; then
    print_warning "Port 3005 already in use - Command Center may already be running"
else
    echo "Run this command in a new terminal:"
    echo "cd services/command-center && npm run dev"
    echo ""
    read -p "Press Enter after starting Command Center..."
    wait_for_service 3005 "Command Center"
fi
echo ""

# 3. Start Member Service
print_info "3. Starting Member Service (port 3001)"
if check_port 3001; then
    print_warning "Port 3001 already in use - Member Service may already be running"
else
    echo "Run this command in a new terminal:"
    echo "cd services/member && npm run dev"
    echo ""
    read -p "Press Enter after starting Member Service..."
    wait_for_service 3001 "Member Service"
fi
echo ""

# 4. Start Frontend
print_info "4. Starting Frontend (port 4000/4001)"
if check_port 4000 && check_port 4001; then
    print_warning "Ports 4000 and 4001 already in use - Frontend may already be running"
else
    echo "Run this command in a new terminal:"
    echo "cd apps/aperion-portal && npm run dev"
    echo ""
    read -p "Press Enter after starting Frontend..."
    
    # Check both possible ports
    if check_port 4000; then
        print_success "Frontend started on port 4000"
        echo "🌐 Access the application at: http://localhost:4000"
    elif check_port 4001; then
        print_success "Frontend started on port 4001"
        echo "🌐 Access the application at: http://localhost:4001"
    else
        print_error "Frontend failed to start on either port 4000 or 4001"
    fi
fi
echo ""

print_success "All services startup process completed!"
echo ""
echo "📋 Service Status Summary:"
echo "=========================="

# Check all services
services=(
    "3000:API Gateway"
    "3001:Member Service"
    "3005:Command Center"
)

for service in "${services[@]}"; do
    port="${service%%:*}"
    name="${service##*:}"
    if check_port $port; then
        print_success "$name (port $port) - Running"
    else
        print_error "$name (port $port) - Not running"
    fi
done

# Check frontend
if check_port 4000; then
    print_success "Frontend (port 4000) - Running"
    echo "🌐 Application URL: http://localhost:4000"
elif check_port 4001; then
    print_success "Frontend (port 4001) - Running"
    echo "🌐 Application URL: http://localhost:4001"
else
    print_error "Frontend - Not running"
fi

echo ""
echo "🎉 If all services are running, the UserRole import issue should be resolved!"
echo "You can now test the application by navigating to the frontend URL."
