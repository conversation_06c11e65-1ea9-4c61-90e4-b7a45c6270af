import { Router } from 'express';
import { CompanyController } from '../controllers/CompanyController';
import { authorize } from '../middleware/auth';
import { UserRole } from '@aperion/shared';

/**
 * Company Management Router
 *
 * Handles all company-related operations including CRUD operations,
 * statistics, and search functionality.
 *
 * Authentication: Required for all routes
 * Authorization: System Admin only
 *
 * @routes
 * GET    /test           - API health check
 * GET    /dev            - Get all companies (development endpoint)
 * GET    /dev/statistics - Get company statistics (development endpoint)
 * GET    /dev/search     - Search companies (development endpoint)
 * GET    /dev/:id        - Get company by ID (development endpoint)
 * POST   /dev            - Create new company (development endpoint)
 * PUT    /dev/:id        - Update company (development endpoint)
 * DELETE /dev/:id        - Delete company (development endpoint)
 * GET    /               - Get all companies (production endpoint)
 * GET    /statistics     - Get company statistics (production endpoint)
 * GET    /search         - Search companies (production endpoint)
 * GET    /:id            - Get company by ID (production endpoint)
 * POST   /               - Create new company (production endpoint)
 * PUT    /:id            - Update company (production endpoint)
 * DELETE /:id            - Delete company (production endpoint)
 */

const router = Router();
const companyController = new CompanyController();

// =============================================================================
// PUBLIC ROUTES (No Authorization Required)
// =============================================================================

/**
 * @route   GET /test
 * @desc    API health check endpoint
 * @access  Public
 * @returns {Object} Health status and timestamp
 */
router.get('/test', (_req, res) => {
  res.json({
    success: true,
    message: 'Company management API is working!',
    timestamp: new Date().toISOString(),
    service: 'command-center',
    module: 'companies',
  });
});

// =============================================================================
// PROTECTED ROUTES (Authentication & Authorization Required)
// =============================================================================

// Apply authorization middleware to all subsequent routes
// Only system admins can manage companies
router.use(authorize([UserRole.SYSTEM_ADMIN]));

// =============================================================================
// DEVELOPMENT ENDPOINTS
// =============================================================================

/**
 * @route   GET /dev
 * @desc    Get all companies with pagination and filtering
 * @access  Private (System Admin)
 * @query   {number} page - Page number (default: 1)
 * @query   {number} limit - Items per page (default: 20)
 * @query   {string} search - Search term
 * @query   {string} status - Filter by status
 */
router.get('/dev', companyController.getCompanies);

/**
 * @route   GET /dev/statistics
 * @desc    Get company statistics and metrics
 * @access  Private (System Admin)
 * @returns {Object} Company statistics including counts and trends
 */
router.get('/dev/statistics', companyController.getCompanyStatistics);

/**
 * @route   GET /dev/search
 * @desc    Search companies by name or other criteria
 * @access  Private (System Admin)
 * @query   {string} q - Search query
 * @query   {number} limit - Maximum results (default: 10)
 */
router.get('/dev/search', companyController.searchCompanies);

/**
 * @route   GET /dev/:id
 * @desc    Get company by ID
 * @access  Private (System Admin)
 * @param   {string} id - Company ID
 * @returns {Object} Company details
 */
router.get('/dev/:id', companyController.getCompanyById);

/**
 * @route   POST /dev
 * @desc    Create new company
 * @access  Private (System Admin)
 * @body    {Object} Company data
 * @returns {Object} Created company details
 */
router.post('/dev', companyController.createCompany);

/**
 * @route   PUT /dev/:id
 * @desc    Update company by ID
 * @access  Private (System Admin)
 * @param   {string} id - Company ID
 * @body    {Object} Updated company data
 * @returns {Object} Updated company details
 */
router.put('/dev/:id', companyController.updateCompany);

/**
 * @route   DELETE /dev/:id
 * @desc    Delete company by ID
 * @access  Private (System Admin)
 * @param   {string} id - Company ID
 * @returns {Object} Deletion confirmation
 */
router.delete('/dev/:id', companyController.deleteCompany);

// =============================================================================
// PRODUCTION ENDPOINTS
// =============================================================================

/**
 * @route   GET /
 * @desc    Get all companies with pagination and filtering
 * @access  Private (System Admin)
 */
router.get('/', companyController.getCompanies);

/**
 * @route   GET /statistics
 * @desc    Get company statistics and metrics
 * @access  Private (System Admin)
 */
router.get('/statistics', companyController.getCompanyStatistics);

/**
 * @route   GET /search
 * @desc    Search companies by name or other criteria
 * @access  Private (System Admin)
 */
router.get('/search', companyController.searchCompanies);

/**
 * @route   GET /:id
 * @desc    Get company by ID
 * @access  Private (System Admin)
 */
router.get('/:id', companyController.getCompanyById);

/**
 * @route   POST /
 * @desc    Create new company
 * @access  Private (System Admin)
 */
router.post('/', companyController.createCompany);

/**
 * @route   PUT /:id
 * @desc    Update company by ID
 * @access  Private (System Admin)
 */
router.put('/:id', companyController.updateCompany);

/**
 * @route   DELETE /:id
 * @desc    Delete company by ID
 * @access  Private (System Admin)
 */
router.delete('/:id', companyController.deleteCompany);

export { router as companiesRouter };
