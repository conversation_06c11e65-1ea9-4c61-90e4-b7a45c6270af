import { motion } from 'framer-motion';
import { Shield, Activity, Database, Monitor } from 'lucide-react';

export interface CommandCenterLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  fullScreen?: boolean;
}

export function CommandCenterLoader({ 
  size = 'md', 
  message = 'Loading command center...', 
  fullScreen = false 
}: CommandCenterLoaderProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const containerClasses = fullScreen 
    ? 'fixed inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50 flex items-center justify-center'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          {/* Outer security ring */}
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-slate-200 dark:border-slate-700"
            animate={{ rotate: 360 }}
            transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            style={{ width: size === 'lg' ? '60px' : size === 'md' ? '48px' : '36px', height: size === 'lg' ? '60px' : size === 'md' ? '48px' : '36px' }}
          />
          
          {/* Inner shield pulse */}
          <motion.div
            className="flex items-center justify-center"
            style={{ width: size === 'lg' ? '60px' : size === 'md' ? '48px' : '36px', height: size === 'lg' ? '60px' : size === 'md' ? '48px' : '36px' }}
            animate={{ 
              scale: [1, 1.15, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
          >
            <Shield className={`${sizeClasses[size]} text-slate-600 dark:text-slate-300`} />
          </motion.div>

          {/* Floating monitoring icons */}
          {[
            { icon: Database, angle: 0, delay: 0 },
            { icon: Monitor, angle: 120, delay: 0.7 },
            { icon: Activity, angle: 240, delay: 1.4 }
          ].map(({ icon: Icon, angle, delay }, i) => (
            <motion.div
              key={i}
              className="absolute"
              animate={{
                rotate: [angle, angle + 360],
                scale: [0.8, 1.2, 0.8],
                opacity: [0.4, 0.9, 0.4],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: delay,
                ease: "easeInOut"
              }}
              style={{
                top: '10%',
                left: '50%',
                transformOrigin: `0px ${size === 'lg' ? '24px' : size === 'md' ? '18px' : '14px'}`,
              }}
            >
              <Icon className="w-3 h-3 text-slate-500 dark:text-slate-400" />
            </motion.div>
          ))}
        </div>

        {message && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-center"
          >
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {message}
            </p>
            <motion.div
              className="flex items-center justify-center space-x-1"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Shield className="w-3 h-3 text-slate-500 dark:text-slate-400" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Securing platform operations
              </span>
            </motion.div>
          </motion.div>
        )}
      </div>
    </div>
  );
}

export function CommandCenterInlineLoader({ size = 'sm' }: Pick<CommandCenterLoaderProps, 'size'>) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
      className={`${sizeClasses[size]} text-slate-600 dark:text-slate-300`}
    >
      <Shield className="w-full h-full" />
    </motion.div>
  );
}