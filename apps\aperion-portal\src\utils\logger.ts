/**
 * Client-side logger for Aperion Portal
 * Provides structured logging for frontend errors and events
 */

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  url?: string;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  component?: string;
  action?: string;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  metadata?: Record<string, any>;
}

class ClientLogger {
  private sessionId: string;
  private logBuffer: LogEntry[] = [];
  private maxBufferSize = 100;
  private flushInterval = 30000; // 30 seconds
  private apiEndpoint = '/api/logs/client';

  constructor() {
    this.sessionId = this.generateSessionId();
    this.startPeriodicFlush();
    this.setupErrorHandlers();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    metadata?: Record<string, any>
  ): LogEntry {
    return {
      level,
      message,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      sessionId: this.sessionId,
      metadata,
    };
  }

  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);
    
    // Keep buffer size manageable
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer = this.logBuffer.slice(-this.maxBufferSize);
    }

    // Console output for development
    if (process.env.NODE_ENV !== 'production') {
      const consoleMethod = entry.level === LogLevel.ERROR ? 'error' :
                           entry.level === LogLevel.WARN ? 'warn' :
                           entry.level === LogLevel.DEBUG ? 'debug' : 'log';
      
      console[consoleMethod](`[${entry.level.toUpperCase()}] ${entry.message}`, entry);
    }

    // Immediate flush for errors
    if (entry.level === LogLevel.ERROR) {
      this.flush();
    }
  }

  private async flush(): Promise<void> {
    if (this.logBuffer.length === 0) return;

    const logsToSend = [...this.logBuffer];
    this.logBuffer = [];

    try {
      await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ logs: logsToSend }),
      });
    } catch (error) {
      // If sending fails, put logs back in buffer (but don't create infinite loop)
      if (this.logBuffer.length < this.maxBufferSize / 2) {
        this.logBuffer.unshift(...logsToSend);
      }
      
      // Fallback to console for critical errors
      console.error('Failed to send logs to server:', error);
    }
  }

  private startPeriodicFlush(): void {
    setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  private setupErrorHandlers(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.error('Uncaught JavaScript error', {
        error: {
          name: event.error?.name || 'Error',
          message: event.error?.message || event.message,
          stack: event.error?.stack,
        },
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.error('Unhandled promise rejection', {
        error: {
          name: 'UnhandledPromiseRejection',
          message: event.reason?.message || String(event.reason),
          stack: event.reason?.stack,
        },
      });
    });
  }

  // Public logging methods
  error(message: string, metadata?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, metadata);
    this.addToBuffer(entry);
  }

  warn(message: string, metadata?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, metadata);
    this.addToBuffer(entry);
  }

  info(message: string, metadata?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, metadata);
    this.addToBuffer(entry);
  }

  debug(message: string, metadata?: Record<string, any>): void {
    if (process.env.NODE_ENV !== 'production') {
      const entry = this.createLogEntry(LogLevel.DEBUG, message, metadata);
      this.addToBuffer(entry);
    }
  }

  // Specialized logging methods
  logUserAction(action: string, component: string, metadata?: Record<string, any>): void {
    this.info(`User action: ${action}`, {
      component,
      action,
      ...metadata,
    });
  }

  logApiCall(method: string, url: string, status: number, duration: number, metadata?: Record<string, any>): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO;
    this[level](`API call: ${method} ${url}`, {
      method,
      url,
      status,
      duration,
      ...metadata,
    });
  }

  logNavigation(from: string, to: string, metadata?: Record<string, any>): void {
    this.info(`Navigation: ${from} -> ${to}`, {
      from,
      to,
      ...metadata,
    });
  }

  logAuthEvent(event: string, metadata?: Record<string, any>): void {
    this.info(`Auth event: ${event}`, {
      event,
      ...metadata,
    });
  }

  // Manual flush method
  async flushLogs(): Promise<void> {
    await this.flush();
  }

  // Set user context
  setUserId(userId: string): void {
    this.sessionId = `${this.sessionId}_user_${userId}`;
  }
}

// Create singleton instance
export const logger = new ClientLogger();

// Export for use in React components and other modules
export default logger;
