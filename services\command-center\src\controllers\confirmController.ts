import { Request, Response } from 'express';
import { confirmEmailService } from '../services/confirmService';
import { logger } from '../../../utils/logger';

export async function confirmEmailController(req: Request, res: Response) {
  try {
    // Get token from query parameter instead of params
    const { username } = req.query;

    if (!username || typeof username !== 'string') {
      logger.warn('Account confirmation attempted without token', {
        requestId: req.requestId,
        ip: req.ip,
      });
      return res.status(400).json({
        success: false,
        message: 'Invalid or missing confirmation token.',
        error: 'MISSING_TOKEN'
      });
    }

    logger.info('Account confirmation attempt', {
      requestId: req.requestId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    console.log(username,"username");
    const result = await confirmEmailService(username);

    if (!result.success) {
      logger.warn('Account confirmation failed', {
        requestId: req.requestId,
        error: result.error,
        message: result.message,
      });

      const statusCode = result.error === 'LINK_EXPIRED' ? 410 :
                        result.error === 'USER_NOT_FOUND' ? 404 : 400;

      return res.status(statusCode).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }

    logger.info('Account confirmation successful', {
      requestId: req.requestId,
      userId: result.data?.userId,
      email: result.data?.email,
      verified: true
    });

    // Return JSON with success message and user data
    return res.status(200).json({
      success: true,
      message: 'Account confirmed successfully. You can now log in.',
      data: {
        userId: result.data?.userId,
        email: result.data?.email,
        status:true,
        verified: true
      }
    });

  } catch (error) {
    logger.error('Account confirmation error', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return res.status(500).json({
      success: false,
      message: 'Internal server error during account confirmation.',
      error: 'INTERNAL_ERROR'
    });
  }
}
