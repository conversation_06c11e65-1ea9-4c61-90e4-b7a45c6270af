import { SessionUtils, EncryptionUtils } from '@aperion/shared/server';
import { JWTPayload, UserRole } from '@aperion/shared';

export interface SessionData {
  userId: string;
  email: string;
  role: UserRole;
  service: string;
  permissions: string[];
  loginTime: number;
  lastActivity: number;
  ipAddress?: string;
  userAgent?: string;
}

export interface SessionConfig {
  secret: string;
  maxAge: number; // in milliseconds
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'strict' | 'lax' | 'none';
}

export class SessionManager {
  private config: SessionConfig;
  private activeSessions: Map<string, SessionData> = new Map();

  constructor(config: SessionConfig) {
    this.config = config;

    // Clean up expired sessions every 5 minutes
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 5 * 60 * 1000);
  }

  /**
   * Create a new session
   */
  createSession(user: JWTPayload, ipAddress?: string, userAgent?: string): string {
    const sessionId = SessionUtils.generateSessionId();
    const now = Date.now();

    const sessionData: SessionData = {
      userId: user.sub,
      email: user.email,
      role: user['custom:role'],
      service: user['custom:service'],
      permissions: user['custom:permissions'] || [],
      loginTime: now,
      lastActivity: now,
      ipAddress,
      userAgent,
    };

    this.activeSessions.set(sessionId, sessionData);
    return sessionId;
  }

  /**
   * Get session data
   */
  getSession(sessionId: string): SessionData | null {
    const session = this.activeSessions.get(sessionId);

    if (!session) {
      return null;
    }

    // Check if session is expired
    if (this.isSessionExpired(session)) {
      this.activeSessions.delete(sessionId);
      return null;
    }

    // Update last activity
    session.lastActivity = Date.now();
    this.activeSessions.set(sessionId, session);

    return session;
  }

  /**
   * Update session data
   */
  updateSession(sessionId: string, updates: Partial<SessionData>): boolean {
    const session = this.activeSessions.get(sessionId);

    if (!session || this.isSessionExpired(session)) {
      return false;
    }

    const updatedSession = {
      ...session,
      ...updates,
      lastActivity: Date.now(),
    };

    this.activeSessions.set(sessionId, updatedSession);
    return true;
  }

  /**
   * Destroy a session
   */
  destroySession(sessionId: string): boolean {
    return this.activeSessions.delete(sessionId);
  }

  /**
   * Destroy all sessions for a user
   */
  destroyUserSessions(userId: string): number {
    let destroyedCount = 0;

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.userId === userId) {
        this.activeSessions.delete(sessionId);
        destroyedCount++;
      }
    }

    return destroyedCount;
  }

  /**
   * Get all active sessions for a user
   */
  getUserSessions(userId: string): Array<{ sessionId: string; session: SessionData }> {
    const userSessions: Array<{ sessionId: string; session: SessionData }> = [];

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.userId === userId && !this.isSessionExpired(session)) {
        userSessions.push({ sessionId, session });
      }
    }

    return userSessions;
  }

  /**
   * Create secure session cookie value
   */
  createSessionCookie(sessionId: string): string {
    const cookieData = {
      sessionId,
      timestamp: Date.now(),
    };

    return SessionUtils.createSecureCookieValue(cookieData, this.config.secret);
  }

  /**
   * Parse session cookie value
   */
  parseSessionCookie(cookieValue: string): string | null {
    const cookieData = SessionUtils.parseSecureCookieValue(cookieValue, this.config.secret);

    if (!cookieData || !cookieData.sessionId) {
      return null;
    }

    // Check if cookie is too old (additional security)
    const maxCookieAge = 24 * 60 * 60 * 1000; // 24 hours
    if (Date.now() - cookieData.timestamp > maxCookieAge) {
      return null;
    }

    return cookieData.sessionId;
  }

  /**
   * Generate CSRF token for session
   */
  generateCSRFToken(sessionId: string): string {
    const session = this.activeSessions.get(sessionId);

    if (!session) {
      throw new Error('Session not found');
    }

    const tokenData = `${sessionId}:${session.userId}:${Date.now()}`;
    return EncryptionUtils.createHMAC(tokenData, this.config.secret);
  }

  /**
   * Verify CSRF token
   */
  verifyCSRFToken(sessionId: string, token: string): boolean {
    const session = this.activeSessions.get(sessionId);

    if (!session) {
      return false;
    }

    // Generate expected token (with some time tolerance)
    const now = Date.now();
    const tolerance = 5 * 60 * 1000; // 5 minutes

    for (let time = now; time >= now - tolerance; time -= 60000) {
      const tokenData = `${sessionId}:${session.userId}:${Math.floor(time / 60000) * 60000}`;
      const expectedToken = EncryptionUtils.createHMAC(tokenData, this.config.secret);

      if (expectedToken === token) {
        return true;
      }
    }

    return false;
  }

  /**
   * Get session statistics
   */
  getSessionStats(): {
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
    sessionsByRole: Record<string, number>;
  } {
    let activeSessions = 0;
    let expiredSessions = 0;
    const sessionsByRole: Record<string, number> = {};

    for (const session of this.activeSessions.values()) {
      if (this.isSessionExpired(session)) {
        expiredSessions++;
      } else {
        activeSessions++;
        sessionsByRole[session.role] = (sessionsByRole[session.role] || 0) + 1;
      }
    }

    return {
      totalSessions: this.activeSessions.size,
      activeSessions,
      expiredSessions,
      sessionsByRole,
    };
  }

  /**
   * Check if session is expired
   */
  private isSessionExpired(session: SessionData): boolean {
    const now = Date.now();
    return (now - session.lastActivity) > this.config.maxAge;
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (this.isSessionExpired(session)) {
        expiredSessions.push(sessionId);
      }
    }

    expiredSessions.forEach(sessionId => {
      this.activeSessions.delete(sessionId);
    });

    if (expiredSessions.length > 0) {
      console.log(`Cleaned up ${expiredSessions.length} expired sessions`);
    }
  }
}

/**
 * Default session configuration
 */
export const defaultSessionConfig: SessionConfig = {
  secret: process.env.SESSION_SECRET || 'default-session-secret',
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  secure: process.env.NODE_ENV === 'production',
  httpOnly: true,
  sameSite: 'strict',
};
