import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';
import { 
  Lightbulb, 
  Heart, 
  Activity, 
  Apple, 
  Moon,
  ArrowRight
} from 'lucide-react';

interface Recommendation {
  id: number;
  title: string;
  description: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
  actionText: string;
  icon: string;
}

interface RecommendationsSectionProps {
  maxItems?: number;
}

export const RecommendationsSection: React.FC<RecommendationsSectionProps> = ({ 
  maxItems = 3 
}) => {
  const navigate = useNavigate();

  // Mock recommendations data
  const recommendations: Recommendation[] = [
    {
      id: 1,
      title: "Increase Daily Water Intake",
      description: "Based on your activity level, we recommend drinking 8-10 glasses of water daily to stay properly hydrated.",
      category: "nutrition",
      priority: "high",
      actionText: "Set Water Reminder",
      icon: "water"
    },
    {
      id: 2,
      title: "Schedule Annual Physical",
      description: "It's been over a year since your last physical exam. Regular check-ups help prevent health issues.",
      category: "health",
      priority: "high",
      actionText: "Book Appointment",
      icon: "health"
    },
    {
      id: 3,
      title: "Try 10-Minute Morning Walks",
      description: "Adding a short morning walk can boost your energy and help you reach your daily step goal.",
      category: "activity",
      priority: "medium",
      actionText: "Start Walking Plan",
      icon: "activity"
    },
    {
      id: 4,
      title: "Improve Sleep Schedule",
      description: "Your sleep data shows irregular bedtimes. A consistent sleep schedule can improve your wellness score.",
      category: "sleep",
      priority: "medium",
      actionText: "Set Sleep Goals",
      icon: "sleep"
    }
  ];

  const displayedRecommendations = recommendations.slice(0, maxItems);

  // Get icon component based on category
  const getIcon = (category: string) => {
    switch (category) {
      case 'activity':
        return <Activity className="h-5 w-5" />;
      case 'nutrition':
        return <Apple className="h-5 w-5" />;
      case 'sleep':
        return <Moon className="h-5 w-5" />;
      case 'health':
        return <Heart className="h-5 w-5" />;
      default:
        return <Lightbulb className="h-5 w-5" />;
    }
  };

  // Get priority colors
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'medium':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'low':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  // Get category colors
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'activity':
        return 'text-blue-600 bg-blue-50';
      case 'nutrition':
        return 'text-green-600 bg-green-50';
      case 'sleep':
        return 'text-purple-600 bg-purple-50';
      case 'health':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-teal-600 bg-teal-50';
    }
  };

  return (
    <div className="space-y-4">
      {displayedRecommendations.map((recommendation, index) => (
        <Card 
          key={recommendation.id}
          className="hover:shadow-md transition-all duration-300 cursor-pointer"
          onClick={() => navigate(`/member/recommendations/${recommendation.id}`)}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-4">
              {/* Icon */}
              <div className={`flex items-center justify-center w-10 h-10 rounded-full ${getCategoryColor(recommendation.category)}`}>
                {getIcon(recommendation.category)}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {recommendation.title}
                  </h4>
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${getPriorityColor(recommendation.priority)}`}
                  >
                    {recommendation.priority} priority
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                  {recommendation.description}
                </p>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 p-0 h-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/member/recommendations/${recommendation.id}/action`);
                  }}
                >
                  <span className="text-sm">{recommendation.actionText}</span>
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* View All Link */}
      {recommendations.length > maxItems && (
        <div className="text-center pt-4">
          <Button
            variant="outline"
            onClick={() => navigate('/member/recommendations')}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            View All Recommendations
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  );
};
