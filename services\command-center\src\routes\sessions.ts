import { Router } from 'express';
import { SessionController } from '../controllers/SessionController';
import { authorize } from '../middleware/auth';
import { UserRole } from '@aperion/shared';

/**
 * Session Management Router
 *
 * Handles user session operations including creation, monitoring,
 * termination, and activity tracking.
 *
 * Authentication: Required for all routes
 * Authorization: System Admin, Employer, Wellness Coach
 *
 * @routes
 * GET    /test                        - API health check
 * GET    /dev                         - Get user sessions (development)
 * GET    /dev/statistics              - Get session statistics (development)
 * GET    /dev/user/:userId            - Get active user sessions (development)
 * GET    /dev/:id                     - Get session by ID (development)
 * POST   /dev                         - Create user session (development)
 * PUT    /dev/:id                     - Update session (development)
 * DELETE /dev/:id                     - Delete session (development)
 * PATCH  /dev/:id/terminate           - Terminate session (development)
 * PATCH  /dev/activity/:sessionId     - Update session activity (development)
 * PATCH  /dev/user/:userId/terminate-all - Terminate all user sessions (development)
 * GET    /                            - Get user sessions (production)
 * GET    /statistics                  - Get session statistics (production)
 * GET    /user/:userId                - Get active user sessions (production)
 * GET    /:id                         - Get session by ID (production)
 * POST   /                            - Create user session (production)
 * PUT    /:id                         - Update session (production)
 * DELETE /:id                         - Delete session (production)
 * PATCH  /:id/terminate               - Terminate session (production)
 * PATCH  /activity/:sessionId         - Update session activity (production)
 * PATCH  /user/:userId/terminate-all  - Terminate all user sessions (production)
 */

const router = Router();
const sessionController = new SessionController();

// =============================================================================
// PROTECTED ROUTES (Authentication & Authorization Required)
// =============================================================================

// Apply authorization middleware to all routes
// System admins can manage all sessions, other roles have limited access
router.use(authorize([UserRole.SYSTEM_ADMIN, UserRole.EMPLOYER, UserRole.WELLNESS_COACH]));

// =============================================================================
// PUBLIC ROUTES (Within Protected Context)
// =============================================================================

/**
 * @route   GET /test
 * @desc    API health check endpoint
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @returns {Object} Health status and timestamp
 */
router.get('/test', (_req, res) => {
  res.json({
    success: true,
    message: 'Session management API is working!',
    timestamp: new Date().toISOString(),
    service: 'command-center',
    module: 'sessions',
  });
});

// =============================================================================
// DEVELOPMENT ENDPOINTS
// =============================================================================

/**
 * @route   GET /dev
 * @desc    Get user sessions with filtering and pagination
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @query   {number} page - Page number (default: 1)
 * @query   {number} limit - Items per page (default: 20)
 * @query   {string} userId - Filter by user ID
 * @query   {string} status - Filter by session status
 */
router.get('/dev', sessionController.getUserSessions);

/**
 * @route   GET /dev/statistics
 * @desc    Get session statistics and metrics
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @returns {Object} Session statistics including active sessions, duration metrics
 */
router.get('/dev/statistics', sessionController.getUserSessionStatistics);

/**
 * @route   GET /dev/user/:userId
 * @desc    Get active sessions for a specific user
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @param   {string} userId - User ID
 * @returns {Array} List of active user sessions
 */
router.get('/dev/user/:userId', sessionController.getActiveUserSessions);

/**
 * @route   GET /dev/:id
 * @desc    Get session details by session ID
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @param   {string} id - Session ID
 * @returns {Object} Session details
 */
router.get('/dev/:id', sessionController.getUserSessionById);

/**
 * @route   POST /dev
 * @desc    Create new user session
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @body    {Object} Session data
 * @returns {Object} Created session details
 */
router.post('/dev', sessionController.createUserSession);

/**
 * @route   PUT /dev/:id
 * @desc    Update session by ID
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @param   {string} id - Session ID
 * @body    {Object} Updated session data
 * @returns {Object} Updated session details
 */
router.put('/dev/:id', sessionController.updateUserSession);

/**
 * @route   DELETE /dev/:id
 * @desc    Delete session by ID
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @param   {string} id - Session ID
 * @returns {Object} Deletion confirmation
 */
router.delete('/dev/:id', sessionController.deleteUserSession);

/**
 * @route   PATCH /dev/:id/terminate
 * @desc    Terminate specific session
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @param   {string} id - Session ID
 * @returns {Object} Termination confirmation
 */
router.patch('/dev/:id/terminate', sessionController.terminateUserSession);

/**
 * @route   PATCH /dev/activity/:sessionId
 * @desc    Update session activity timestamp
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @param   {string} sessionId - Session ID
 * @returns {Object} Activity update confirmation
 */
router.patch('/dev/activity/:sessionId', sessionController.updateSessionActivity);

/**
 * @route   PATCH /dev/user/:userId/terminate-all
 * @desc    Terminate all sessions for a specific user
 * @access  Private (System Admin, Employer, Wellness Coach)
 * @param   {string} userId - User ID
 * @returns {Object} Termination confirmation
 */
router.patch('/dev/user/:userId/terminate-all', sessionController.terminateAllUserSessions);

// =============================================================================
// PRODUCTION ENDPOINTS
// =============================================================================

/**
 * @route   GET /
 * @desc    Get user sessions with filtering and pagination
 * @access  Private (System Admin, Employer, Wellness Coach)
 */
router.get('/', sessionController.getUserSessions);

/**
 * @route   GET /statistics
 * @desc    Get session statistics and metrics
 * @access  Private (System Admin only)
 */
router.get('/statistics', authorize([UserRole.SYSTEM_ADMIN]), sessionController.getUserSessionStatistics);

/**
 * @route   GET /user/:userId
 * @desc    Get active sessions for a specific user
 * @access  Private (System Admin, Employer, Wellness Coach)
 */
router.get('/user/:userId', sessionController.getActiveUserSessions);

/**
 * @route   GET /:id
 * @desc    Get session details by session ID
 * @access  Private (System Admin, Employer, Wellness Coach)
 */
router.get('/:id', sessionController.getUserSessionById);

/**
 * @route   POST /
 * @desc    Create new user session
 * @access  Private (System Admin, Employer, Wellness Coach)
 */
router.post('/', sessionController.createUserSession);

/**
 * @route   PUT /:id
 * @desc    Update session by ID
 * @access  Private (System Admin, Employer, Wellness Coach)
 */
router.put('/:id', sessionController.updateUserSession);

/**
 * @route   DELETE /:id
 * @desc    Delete session by ID
 * @access  Private (System Admin only)
 */
router.delete('/:id', authorize([UserRole.SYSTEM_ADMIN]), sessionController.deleteUserSession);

/**
 * @route   PATCH /:id/terminate
 * @desc    Terminate specific session
 * @access  Private (System Admin, Employer, Wellness Coach)
 */
router.patch('/:id/terminate', sessionController.terminateUserSession);

/**
 * @route   PATCH /activity/:sessionId
 * @desc    Update session activity timestamp
 * @access  Private (System Admin, Employer, Wellness Coach)
 */
router.patch('/activity/:sessionId', sessionController.updateSessionActivity);

/**
 * @route   PATCH /user/:userId/terminate-all
 * @desc    Terminate all sessions for a specific user
 * @access  Private (System Admin only)
 */
router.patch('/user/:userId/terminate-all', authorize([UserRole.SYSTEM_ADMIN]), sessionController.terminateAllUserSessions);

export { router as sessionsRouter };
