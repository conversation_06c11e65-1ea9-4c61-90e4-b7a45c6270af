import React, { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Heart,
  Shield,
  Users,
  GraduationCap,
  Monitor,
  Activity,
  Target,
  TrendingUp,
  Calendar,
  MessageCircle,
  Award,
  Clock,
  Star,
  CheckCircle,
  ArrowRight,
  Building,
  UserCheck,
  BookOpen,
  Settings,
  Lock,
  LogOut
} from 'lucide-react';

const HomePage: React.FC = () => {
  const { user, getDefaultRoute, isLoading, isTokenValid, logout } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // TEMP: Disable automatic redirect to allow access to landing page even with token
  // Check authentication status and handle routing
  // useEffect(() => {
  //   if (!isLoading) {
  //     if (user && isTokenValid) {
  //       // User is authenticated with valid token, redirect to their portal
  //       const defaultRoute = getDefaultRoute();
  //       if (defaultRoute !== '/auth/send-code') {
  //         navigate(defaultRoute);
  //       }
  //     } else if (isTokenValid === false && localStorage.getItem('aperion_token')) {
  //       // Token exists but is invalid, show unauthorized message
  //       toast({
  //         title: "Session Expired",
  //         description: "Your session has expired. Please log in again.",
  //         variant: "destructive",
  //       });
  //       // Clear invalid token
  //       localStorage.removeItem('aperion_token');
  //       localStorage.removeItem('aperion_user');
  //     }
  //   }
  // }, [user, isLoading, isTokenValid, getDefaultRoute, navigate, toast]);

  // Role-based portal access mapping
  const portalRoleMapping = {
    'member': ['member'],
    'employer': ['employer', 'hr-manager'],
    'coach': ['wellness-coach', 'wellness-coordinator'],
    'zenx': ['learner', 'content-creator', 'lms-admin'],
    'command': ['system-admin', 'operations-manager']
  };

  const handlePortalAccess = (portalId: string, href: string) => {
    if (!user) {
      // Navigate to login page when user is not authenticated
      navigate('/auth/send-code');
      return;
    }

    const allowedRoles = portalRoleMapping[portalId as keyof typeof portalRoleMapping];
    if (!allowedRoles?.includes(user.role)) {
      // User doesn't have access to this portal, redirect to their appropriate portal
      const userDefaultRoute = getDefaultRoute();
      toast({
        title: "Access Redirected",
        description: `Redirecting you to your portal. Your role: ${user.role}`,
        variant: "default",
      });
      navigate(userDefaultRoute);
      return;
    }

    // User has access, navigate to portal using React Router
    navigate(href);
  };

  const handleGetStarted = () => {
    if (user) {
      // User is authenticated, go to their default route
      const defaultRoute = getDefaultRoute();
      navigate(defaultRoute);
    } else {
      // Navigate to login page when user is not authenticated
      navigate('/auth/send-code');
    }
  };

  const handleLogout = () => {
    logout();
    toast({
      title: "Logged Out Successfully",
      description: "You have been signed out of your account.",
      variant: "default",
    });
  };

  const servicePortals = [
    {
      id: 'member',
      title: 'Member Portal',
      description: 'Personal wellness journey with health plans, goals tracking, and rewards',
      icon: <Heart className="w-8 h-8" />,
      href: '/member',
      color: 'bg-gradient-to-br from-blue-500 to-indigo-600',
      features: ['Health Plan Management', 'Wellness Goals', 'Rewards Program', 'Health ID Card']
    },
    {
      id: 'employer',
      title: 'Employer Portal',
      description: 'Comprehensive employee wellness management and analytics platform',
      icon: <Building className="w-8 h-8" />,
      href: '/employer',
      color: 'bg-gradient-to-br from-emerald-500 to-teal-600',
      features: ['Employee Management', 'Wellness Analytics', 'Program Administration', 'Cost Tracking']
    },
    {
      id: 'coach',
      title: 'Wellness Central',
      description: 'Advanced coaching platform for health professionals and wellness experts',
      icon: <UserCheck className="w-8 h-8" />,
      href: '/wellness',
      color: 'bg-gradient-to-br from-teal-500 to-cyan-600',
      features: ['Client Management', 'Session Tracking', 'Care Plans', 'Progress Monitoring']
    },
    {
      id: 'zenx',
      title: 'ZenX Learning',
      description: 'Intelligent learning management system with adaptive content delivery',
      icon: <BookOpen className="w-8 h-8" />,
      href: '/lms',
      color: 'bg-gradient-to-br from-purple-500 to-violet-600',
      features: ['Course Management', 'Adaptive Learning', 'Progress Tracking', 'Certifications']
    },
    {
      id: 'command',
      title: 'Command Center',
      description: 'Centralized administration hub for platform management and monitoring',
      icon: <Monitor className="w-8 h-8" />,
      href: '/admin',
      color: 'bg-gradient-to-br from-slate-500 to-gray-600',
      features: ['User Management', 'System Monitoring', 'Analytics Dashboard', 'Security Center']
    }
  ];

  const platformFeatures = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'Enterprise Security',
      description: 'Bank-level security with multi-factor authentication and encrypted data'
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: 'Advanced Analytics',
      description: 'Real-time insights and predictive analytics for better health outcomes'
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: 'Multi-Role Access',
      description: 'Tailored experiences for members, employers, coaches, and administrators'
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: 'Goal Tracking',
      description: 'Comprehensive wellness goal setting and progress monitoring system'
    },
    {
      icon: <Calendar className="w-6 h-6" />,
      title: 'Smart Scheduling',
      description: 'Intelligent appointment and session scheduling with automated reminders'
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: 'Rewards System',
      description: 'Gamified wellness experience with points, achievements, and incentives'
    }
  ];

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <header className="relative z-50 py-4 px-6 md:px-10 bg-white/90 dark:bg-slate-900/90 shadow-lg backdrop-blur-md border-b border-slate-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-3"
          >
            <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold text-xl shadow-lg">
              H
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-400 dark:to-indigo-500">
                Horizon Health
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-400">Comprehensive Wellness Platform</p>
            </div>
          </motion.div>

          <nav className="hidden lg:flex gap-2 items-center">
            {servicePortals.map((portal, index) => (
              <motion.div
                key={portal.id}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800"
                  onClick={() => handlePortalAccess(portal.id, portal.href)}
                >
                  {portal.title}
                </Button>
              </motion.div>
            ))}

            {/* Logout Button - Only visible for authenticated users */}
            {user && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: servicePortals.length * 0.1 }}
                className="ml-2 pl-2 border-l border-slate-200 dark:border-slate-700"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300"
                  onClick={handleLogout}
                >
                  <LogOut className="w-4 h-4 mr-1" />
                  Logout
                </Button>
              </motion.div>
            )}
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main className="relative">
        <section className="py-20 md:py-32 px-6">
          <div className="max-w-7xl mx-auto">
            <div className="text-center space-y-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-4"
              >
                <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 px-4 py-2">
                  Next-Generation Wellness Platform
                </Badge>
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-slate-900 via-blue-800 to-indigo-800 dark:from-slate-100 dark:via-blue-200 dark:to-indigo-200">
                    Transform Your
                  </span>
                  <br />
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">
                    Wellness Journey
                  </span>
                </h1>
                <p className="text-xl md:text-2xl text-slate-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed">
                  Comprehensive healthcare and wellness platform featuring five specialized portals for members, employers, coaches, learning, and administration
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
                className="flex flex-col sm:flex-row gap-4 justify-center items-center"
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg px-8 py-3 text-lg"
                  onClick={handleGetStarted}
                >
                  <Heart className="w-5 h-5 mr-2" />
                  {user ? 'Go to My Portal' : 'Start Your Wellness Journey'}
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-slate-300 dark:border-slate-600 px-8 py-3 text-lg"
                  onClick={() => handlePortalAccess('command', '/admin')}
                >
                  <Monitor className="w-5 h-5 mr-2" />
                  Explore Platform
                </Button>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Service Portals Section */}
        <section className="py-20 px-6 bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center space-y-4 mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-slate-100">
                Five Specialized Portals
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
                Each portal is designed for specific user roles with tailored features and optimized workflows
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
              {servicePortals.map((portal, index) => (
                <motion.div
                  key={portal.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                  className={`${index === 4 ? 'md:col-span-2 xl:col-span-1' : ''}`}
                >
                  <Card className="h-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-slate-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardHeader className="space-y-4">
                      <div className={`w-16 h-16 rounded-2xl ${portal.color} flex items-center justify-center text-white shadow-lg`}>
                        {portal.icon}
                      </div>
                      <div>
                        <CardTitle className="text-xl text-slate-900 dark:text-slate-100">
                          {portal.title}
                        </CardTitle>
                        <CardDescription className="text-slate-600 dark:text-slate-300 mt-2">
                          {portal.description}
                        </CardDescription>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        {portal.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-sm text-slate-600 dark:text-slate-300">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button
                        className="w-full group"
                        variant="outline"
                        onClick={() => handlePortalAccess(portal.id, portal.href)}
                      >
                        Access {portal.title}
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Platform Features Section */}
        <section className="py-20 px-6 bg-slate-50 dark:bg-slate-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center space-y-4 mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-slate-100">
                Platform Features
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
                Built with enterprise-grade security and modern technology for exceptional user experiences
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {platformFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                  className="text-center space-y-4"
                >
                  <div className="w-12 h-12 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mx-auto">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100">
                    {feature.title}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="space-y-2"
              >
                <div className="text-4xl font-bold">5</div>
                <div className="text-blue-100">Specialized Portals</div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="space-y-2"
              >
                <div className="text-4xl font-bold">24/7</div>
                <div className="text-blue-100">Platform Monitoring</div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                viewport={{ once: true }}
                className="space-y-2"
              >
                <div className="text-4xl font-bold">100%</div>
                <div className="text-blue-100">Data Encryption</div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
                viewport={{ once: true }}
                className="space-y-2"
              >
                <div className="text-4xl font-bold">∞</div>
                <div className="text-blue-100">Scalability</div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-6">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="space-y-4"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-slate-100">
                Ready to Transform Your Wellness Experience?
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300">
                Join thousands of users who trust Horizon Health for their wellness journey
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              viewport={{ once: true }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg px-8 py-3 text-lg"
                onClick={handleGetStarted}
              >
                <Heart className="w-5 h-5 mr-2" />
                {user ? 'Access My Portal' : 'Get Started Today'}
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-slate-300 dark:border-slate-600 px-8 py-3 text-lg"
                onClick={() => handlePortalAccess('command', '/admin')}
              >
                <Settings className="w-5 h-5 mr-2" />
                View Demo
              </Button>
            </motion.div>

            {/* TEMP: Direct Portal Selection */}
            {!user && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
                viewport={{ once: true }}
                className="pt-6 space-y-4"
              >

                <Button
                  variant="default"
                  size="lg"
                  className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg px-8 py-3 text-lg"
                  onClick={() => navigate('/auth/send-code')}
                >
                  <Monitor className="w-5 h-5 mr-2" />
                  Login to Access Portals
                </Button>
              </motion.div>
            )}
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-12 px-6 bg-slate-900 text-slate-300">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold">
                  H
                </div>
                <span className="text-xl font-bold text-white">Horizon Health</span>
              </div>
              <p className="text-slate-400">
                Comprehensive wellness platform for the modern healthcare experience
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Portals</h3>
              <div className="space-y-2">
                <Link to="/member" className="block hover:text-white transition-colors">Member Portal</Link>
                <Link to="/employer" className="block hover:text-white transition-colors">Employer Portal</Link>
                <Link to="/wellness" className="block hover:text-white transition-colors">Coach Portal</Link>
                <Link to="/lms" className="block hover:text-white transition-colors">ZenX Learning</Link>
                <Link to="/admin" className="block hover:text-white transition-colors">Command Center</Link>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Features</h3>
              <div className="space-y-2">
                <div className="hover:text-white transition-colors">Health Management</div>
                <div className="hover:text-white transition-colors">Wellness Goals</div>
                <div className="hover:text-white transition-colors">Learning Platform</div>
                <div className="hover:text-white transition-colors">Analytics Dashboard</div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Company</h3>
              <div className="space-y-2">
                <div className="hover:text-white transition-colors">About Us</div>
                <div className="hover:text-white transition-colors">Privacy Policy</div>
                <div className="hover:text-white transition-colors">Terms of Service</div>
                <div className="hover:text-white transition-colors">Support</div>
              </div>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-slate-800 text-center text-slate-400">
            <p>&copy; 2024 Horizon Health. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;