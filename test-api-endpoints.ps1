# API Endpoint Testing Script for Command Center Service
# Base URL for the Command Center service
$baseUrl = "http://localhost:3005"

# Initialize results tracking
$results = @()
$totalEndpoints = 0
$workingEndpoints = 0
$brokenEndpoints = 0

# Function to test an endpoint
function Test-Endpoint {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Description,
        [hashtable]$Body = $null
    )
    
    $global:totalEndpoints++
    Write-Host "Testing: $Description" -ForegroundColor Yellow
    Write-Host "  $Method $Url" -ForegroundColor Gray
    
    try {
        $headers = @{
            'Content-Type' = 'application/json'
        }
        
        if ($Body) {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -Body $jsonBody -ErrorAction Stop
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -ErrorAction Stop
        }
        
        Write-Host "  ✅ SUCCESS" -ForegroundColor Green
        $global:workingEndpoints++
        
        $global:results += [PSCustomObject]@{
            Method = $Method
            Endpoint = $Url.Replace($baseUrl, "")
            Description = $Description
            Status = "✅ Working"
            Error = $null
        }
        
        return $response
    }
    catch {
        Write-Host "  ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        $global:brokenEndpoints++
        
        $global:results += [PSCustomObject]@{
            Method = $Method
            Endpoint = $Url.Replace($baseUrl, "")
            Description = $Description
            Status = "❌ Broken"
            Error = $_.Exception.Message
        }
        
        return $null
    }
}

Write-Host "🚀 Starting API Endpoint Testing for Command Center Service" -ForegroundColor Cyan
Write-Host "Base URL: $baseUrl" -ForegroundColor Cyan
Write-Host "=" * 60

# Test Companies API
Write-Host "`n📊 Testing Companies API" -ForegroundColor Magenta

Test-Endpoint -Method "GET" -Url "$baseUrl/admin/companies/dev" -Description "Get all companies"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/companies/dev/statistics" -Description "Get company statistics"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/companies/dev/search?q=test" -Description "Search companies"

# Create a test company first
$testCompany = @{
    name = "Test Company API"
    code = "TEST001"
    industry = "Technology"
    companySize = "11-50"
    contactEmail = "<EMAIL>"
    status = "active"
}

$createdCompany = Test-Endpoint -Method "POST" -Url "$baseUrl/admin/companies/dev" -Description "Create new company" -Body $testCompany

if ($createdCompany -and $createdCompany.data -and $createdCompany.data.company) {
    $companyId = $createdCompany.data.company.id
    Test-Endpoint -Method "GET" -Url "$baseUrl/admin/companies/dev/$companyId" -Description "Get company by ID"
    
    $updateData = @{
        name = "Updated Test Company"
        industry = "Updated Technology"
    }
    Test-Endpoint -Method "PUT" -Url "$baseUrl/admin/companies/dev/$companyId" -Description "Update company" -Body $updateData
    Test-Endpoint -Method "DELETE" -Url "$baseUrl/admin/companies/dev/$companyId" -Description "Delete company"
}

# Test Subscriptions API
Write-Host "`n📋 Testing Subscriptions API" -ForegroundColor Magenta

Test-Endpoint -Method "GET" -Url "$baseUrl/admin/subscriptions/dev" -Description "Get all subscription plans"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/subscriptions/dev/statistics" -Description "Get subscription statistics"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/subscriptions/dev/active" -Description "Get active subscriptions"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/subscriptions/dev/search?q=basic" -Description "Search subscriptions"

# Create a test subscription
$testSubscription = @{
    name = "Test Basic Plan"
    description = "Test basic subscription plan"
    planType = "basic"
    priceMonthly = 29.99
    priceYearly = 299.99
    maxUsers = 10
    features = @("Feature 1", "Feature 2")
    isActive = $true
}

$createdSubscription = Test-Endpoint -Method "POST" -Url "$baseUrl/admin/subscriptions/dev" -Description "Create new subscription" -Body $testSubscription

if ($createdSubscription -and $createdSubscription.data -and $createdSubscription.data.plan) {
    $subscriptionId = $createdSubscription.data.plan.id
    Test-Endpoint -Method "GET" -Url "$baseUrl/admin/subscriptions/dev/$subscriptionId" -Description "Get subscription by ID"
    
    $updateData = @{
        name = "Updated Test Basic Plan"
        priceMonthly = 39.99
    }
    Test-Endpoint -Method "PUT" -Url "$baseUrl/admin/subscriptions/dev/$subscriptionId" -Description "Update subscription" -Body $updateData
    Test-Endpoint -Method "DELETE" -Url "$baseUrl/admin/subscriptions/dev/$subscriptionId" -Description "Delete subscription"
}

# Test Sessions API
Write-Host "`n🔐 Testing Sessions API" -ForegroundColor Magenta

Test-Endpoint -Method "GET" -Url "$baseUrl/admin/sessions/dev" -Description "Get all user sessions"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/sessions/dev/statistics" -Description "Get session statistics"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/sessions/dev/user/1" -Description "Get sessions for user ID 1"

# Create a test session
$testSession = @{
    userId = 1
    sessionId = "test-session-$(Get-Date -Format 'yyyyMMddHHmmss')"
    ipAddress = "127.0.0.1"
    userAgent = "PowerShell Test Script"
}

$createdSession = Test-Endpoint -Method "POST" -Url "$baseUrl/admin/sessions/dev" -Description "Create new session" -Body $testSession

if ($createdSession -and $createdSession.data -and $createdSession.data.session) {
    $sessionId = $createdSession.data.session.id
    Test-Endpoint -Method "GET" -Url "$baseUrl/admin/sessions/dev/$sessionId" -Description "Get session by ID"
    
    $updateData = @{
        userAgent = "Updated PowerShell Test Script"
    }
    Test-Endpoint -Method "PUT" -Url "$baseUrl/admin/sessions/dev/$sessionId" -Description "Update session" -Body $updateData
    Test-Endpoint -Method "PATCH" -Url "$baseUrl/admin/sessions/dev/activity/$($testSession.sessionId)" -Description "Update session activity"
    Test-Endpoint -Method "PATCH" -Url "$baseUrl/admin/sessions/dev/$sessionId/terminate" -Description "Terminate session"
    Test-Endpoint -Method "DELETE" -Url "$baseUrl/admin/sessions/dev/$sessionId" -Description "Delete session"
}

# Test Assignments API
Write-Host "`n📝 Testing Assignments API" -ForegroundColor Magenta

Test-Endpoint -Method "GET" -Url "$baseUrl/admin/assignments/dev/companies" -Description "Get company assignments"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/assignments/dev/subscriptions" -Description "Get subscription assignments"
Test-Endpoint -Method "GET" -Url "$baseUrl/admin/assignments/dev/statistics" -Description "Get assignment statistics"

Write-Host "`n" + "=" * 60
Write-Host "📊 API Testing Summary" -ForegroundColor Cyan
Write-Host "Total Endpoints Tested: $totalEndpoints" -ForegroundColor White
Write-Host "Working Endpoints: $workingEndpoints" -ForegroundColor Green
Write-Host "Broken Endpoints: $brokenEndpoints" -ForegroundColor Red

if ($brokenEndpoints -gt 0) {
    Write-Host "`n❌ Broken Endpoints Details:" -ForegroundColor Red
    $results | Where-Object { $_.Status -eq "❌ Broken" } | ForEach-Object {
        Write-Host "  $($_.Method) $($_.Endpoint) - $($_.Error)" -ForegroundColor Red
    }
}

Write-Host "`n📋 Detailed Results:" -ForegroundColor Cyan
$results | Format-Table -AutoSize

# Calculate API health percentage
$healthPercentage = [math]::Round(($workingEndpoints / $totalEndpoints) * 100, 2)
Write-Host "`n🏥 Overall API Health: $healthPercentage%" -ForegroundColor $(if ($healthPercentage -ge 80) { "Green" } elseif ($healthPercentage -ge 60) { "Yellow" } else { "Red" })
