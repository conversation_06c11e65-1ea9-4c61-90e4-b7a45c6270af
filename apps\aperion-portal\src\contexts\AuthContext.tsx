import React, { createContext, useContext, useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

interface User {
  id: string
  email: string
  role: string
  firstName?: string
  lastName?: string
  avatar?: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  isLoading: boolean
  isTokenValid: boolean | null
  login: (email: string, password: string) => Promise<void>
  loginWithOTP: (token: string, userData: User, roleId: number) => void
  signup: (userData: SignupData) => Promise<void>
  activate: (activationCode: string, userData: ActivationData) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  getDefaultRoute: () => string
  getPortalRouteByRoleId: (roleId: number) => string
  validateStoredToken: () => Promise<boolean>
  getStoredToken: () => string | null
}

interface SignupData {
  email: string
  password: string
  firstName: string
  lastName: string
}

interface ActivationData {
  firstName: string
  lastName: string
  password: string
  dateOfBirth: string
  phone: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null)
  const navigate = useNavigate()

  const validateStoredToken = async (): Promise<boolean> => {
    const storedToken = localStorage.getItem('aperion_token')
    if (!storedToken) return false

    try {


      const response = await fetch('/api/auth/verify', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${storedToken}`,
        },
      })

      return response.ok
    } catch (error) {
      return false
    }
  }

  useEffect(() => {
    // Check for stored token on app load and validate it
    const initializeAuth = async () => {
      const storedToken = localStorage.getItem('aperion_token')
      const storedUser = localStorage.getItem('aperion_user')

      if (storedToken && storedUser) {
        setToken(storedToken)
        setUser(JSON.parse(storedUser))

        // Validate stored token with backend
        const isValid = await validateStoredToken()
        setIsTokenValid(isValid)

        if (!isValid) {
          // Token is invalid, clear storage
          localStorage.removeItem('aperion_token')
          localStorage.removeItem('aperion_user')
          setToken(null)
          setUser(null)
        }
      } else {
        setIsTokenValid(false)
      }

      setIsLoading(false)
    }

    initializeAuth()
  }, [])

  const getDefaultRoute = () => {
    if (!user) return '/auth/send-code'

    switch (user.role) {
      case 'member':
        return '/member/dashboard'
      case 'employer':
        return '/employer/dashboard'
      case 'wellness-coach':
        return '/wellness/dashboard'
      case 'content-creator':
        return '/lms/dashboard'
      case 'system-admin':
        return '/admin/dashboard'
      default:
        return '/auth/send-code'
    }
  }

  const getPortalRouteByRoleId = (roleId: number): string => {
    switch (roleId) {
      case 1: // Admin = Command Center portal
        return '/admin/dashboard'
      case 2: // Employer portal
        return '/employer/dashboard'
      case 3: // Member portal
        return '/member/dashboard'
      case 4: // Wellness Coach portal
        return '/wellness/dashboard'
      default:
        return '/member/dashboard' // Default to member portal
    }
  }

  const loginWithOTP = (token: string, userData: User, roleId: number) => {
    // Set authentication state synchronously
    setToken(token)
    setUser(userData)
    setIsTokenValid(true)

    // Store in localStorage
    localStorage.setItem('aperion_token', token)
    localStorage.setItem('aperion_user', JSON.stringify(userData))

    // Use setTimeout to ensure state updates are processed before navigation
    // This prevents race conditions with route guards
    setTimeout(() => {
      const portalRoute = getPortalRouteByRoleId(roleId)
      navigate(portalRoute)
    }, 100) // Small delay to ensure state is updated
  }

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Login failed')
      }

      const data = await response.json()

      setToken(data.token)
      setUser(data.user)

      localStorage.setItem('aperion_token', data.token)
      localStorage.setItem('aperion_user', JSON.stringify(data.user))

      // Navigate to role-specific dashboard based on user role
      let defaultRoute = '/auth/send-code'
      switch (data.user.role) {
        case 'member':
          defaultRoute = '/member/dashboard'
          break
        case 'employer':
          defaultRoute = '/employer/dashboard'
          break
        case 'wellness-coach':
          defaultRoute = '/wellness/dashboard'
          break
        case 'content-creator':
          defaultRoute = '/lms/dashboard'
          break
        case 'system-admin':
          defaultRoute = '/admin/dashboard'
          break
      }
      navigate(defaultRoute)
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const signup = async (userData: SignupData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Signup failed')
      }

      const data = await response.json()

      setToken(data.token)
      setUser(data.user)

      localStorage.setItem('aperion_token', data.token)
      localStorage.setItem('aperion_user', JSON.stringify(data.user))

      // Navigate to role-specific dashboard
      const defaultRoute = getDefaultRoute()
      navigate(defaultRoute)
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const activate = async (activationCode: string, userData: ActivationData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ activationCode, ...userData }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Activation failed')
      }

      const data = await response.json()

      setToken(data.token)
      setUser(data.user)

      localStorage.setItem('aperion_token', data.token)
      localStorage.setItem('aperion_user', JSON.stringify(data.user))

      // Navigate to member dashboard (activated users are always members)
      navigate('/member/dashboard')
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    setToken(null)
    localStorage.removeItem('aperion_token')
    localStorage.removeItem('aperion_user')
    navigate('/auth/send-code')
  }

  const refreshToken = async () => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setToken(data.token)
        localStorage.setItem('aperion_token', data.token)
      } else {
        logout()
      }
    } catch (error) {
      logout()
    }
  }

  const getStoredToken = (): string | null => {
    return localStorage.getItem('aperion_token')
  }

  const value = {
    user,
    token,
    isLoading,
    isTokenValid,
    login,
    loginWithOTP,
    signup,
    activate,
    logout,
    refreshToken,
    getDefaultRoute,
    getPortalRouteByRoleId,
    validateStoredToken,
    getStoredToken,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
