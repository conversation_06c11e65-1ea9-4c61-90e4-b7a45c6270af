{"name": "@aperion/member", "version": "1.0.0", "description": "Member service for Aperion Health", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@aperion/shared": "^1.0.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@types/node-fetch": "^2.6.12", "aws-sdk": "^2.1691.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "drizzle-orm": "^0.28.6", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "pg": "^8.11.3", "pino": "^9.7.0", "pino-http": "^10.5.0", "pino-pretty": "^13.0.0", "sharp": "^0.33.5", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^22.15.30", "@types/pg": "^8.10.7", "@types/uuid": "^9.0.7", "drizzle-kit": "^0.19.13", "jest": "^29.7.0", "nodemon": "^3.0.2", "rimraf": "^5.0.5", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}