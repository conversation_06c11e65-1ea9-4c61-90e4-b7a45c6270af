import { UserModel } from '../models/User';
import { logger } from '../utils/logger';
import { sendMail } from '../../../utils/mailService';
import { decryptData, encryptData } from '../../../utils/cryptoUtils';
import fs from 'fs/promises';
import ejs from 'ejs';
import dotenv from 'dotenv';
import path from 'path';
import {
  AdminUser,
  CreateUserRequest,
  UpdateUserRequest,
  UserQueryParams,
  UserStatistics,
} from '../types/user';
import { ApiResponse, ApiMeta, UserRole } from '@aperion/shared';
import { signUpUser, deleteUserFromCognito } from './CognitoService';

dotenv.config();

export class UserService {
  private userModel: UserModel;

  constructor() {
    this.userModel = new UserModel();
  }

  /**
   * Get all users with pagination and filtering
   */
  async getUsers(params: UserQueryParams): Promise<ApiResponse<AdminUser[]>> {
    try {
      logger.info('Fetching users with params:', params);

      const { users, total } = await this.userModel.getUsers(params);

      const page = params.page || 1;
      const limit = params.limit || 20;
      const totalPages = Math.ceil(total / limit);

      const meta: ApiMeta = {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };

      logger.info(
        `Successfully fetched ${users.length} users out of ${total} total`
      );

      return {
        success: true,
        data: users,
        meta,
      };
    } catch (error) {
      logger.error('Error in UserService.getUsers:', error);
      return {
        success: false,
        error: {
          code: 'USER_FETCH_ERROR',
          message: 'Failed to fetch users',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get users filtered by role with enhanced role mapping
   * This method provides dedicated role-based filtering functionality
   * with automatic mapping between frontend role names and backend user types
   */
  async getUsersByRoleFilter(params: UserQueryParams): Promise<ApiResponse<AdminUser[]>> {
    try {
      logger.info('Fetching users with role filter:', {
        role: params.role,
        page: params.page,
        limit: params.limit,
        search: params.search,
        status: params.status,
        company: params.company
      });

      // Validate that role parameter is provided for this filter endpoint
      if (!params.role) {
        return {
          success: false,
          error: {
            code: 'MISSING_ROLE_FILTER',
            message: 'Role filter is required for this endpoint',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Use the existing getUsers method from the model with the role filter
      const { users, total } = await this.userModel.getUsers(params);

      const page = params.page || 1;
      const limit = params.limit || 20;
      const totalPages = Math.ceil(total / limit);

      const meta: ApiMeta = {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };

      logger.info(
        `Successfully fetched ${users.length} users out of ${total} total with role filter: ${params.role}`
      );

      return {
        success: true,
        data: users,
        meta,
      };
    } catch (error) {
      logger.error('Error in UserService.getUsersByRoleFilter:', error);
      return {
        success: false,
        error: {
          code: 'USER_FILTER_ERROR',
          message: 'Failed to fetch filtered users',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<ApiResponse<AdminUser>> {
    try {
      logger.info(`Fetching user with ID: ${id}`);

      if (!id || typeof id !== 'string') {
        return {
          success: false,
          error: {
            code: 'INVALID_USER_ID',
            message: 'Valid user ID is required',
            timestamp: new Date().toISOString(),
          },
        };
      }

      const user = await this.userModel.getUserById(id);

      if (!user) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            timestamp: new Date().toISOString(),
          },
        };
      }

      logger.info(`Successfully fetched user: ${user.email}`);

      return {
        success: true,
        data: user,
      };
    } catch (error) {
      logger.error('Error in UserService.getUserById:', error);
      return {
        success: false,
        error: {
          code: 'USER_FETCH_ERROR',
          message: 'Failed to fetch user',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Create a new user with Cognito integration
   */
  async createUser(
    userData: CreateUserRequest
  ): Promise<ApiResponse<AdminUser>> {
    try {
      logger.info('Creating new user:', {
        email: userData.email,
        role: userData.role,
      });

      // Check if user with email already exists in our database
      const existingUsers = await this.userModel.getUsers({
        search: userData.email,
        limit: 1,
      });

      if (existingUsers.users.length > 0) {
        return {
          success: false,
          error: {
            code: 'USER_ALREADY_EXISTS',
            message: 'User with this email already exists in our system',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Map role to roleId for Cognito (only for supported roles)
      const roleIdMapping: Partial<Record<UserRole, number>> = {
        [UserRole.SYSTEM_ADMIN]: 1,
        [UserRole.EMPLOYER]: 2,
        [UserRole.MEMBER]: 3,
        [UserRole.WELLNESS_COACH]: 4,
      };

      const roleId = roleIdMapping[userData.role];
      if (!roleId) {
        return {
          success: false,
          error: {
            code: 'INVALID_ROLE',
            message: `Invalid role: ${userData.role}`,
            timestamp: new Date().toISOString(),
          },
        };
      }
      // Step 1: Create user in AWS Cognito first
      logger.info('Creating user in Cognito:', { email: userData.email });

      let cognitoResponse: { userSub: string; password: string };
      try {
        if (!userData.phone) {
          return {
            success: false,
            error: {
              code: 'MISSING_PHONE',
              message: 'Phone number is required for Cognito signup',
              timestamp: new Date().toISOString(),
            },
          };
        }

        cognitoResponse = await signUpUser({
          phoneNumber: userData.phone,
          email: userData.email,
          roleId: roleId,
        });

        logger.info('Successfully signed up user in Cognito:', {
          email: userData.email,
          userSub: cognitoResponse.userSub,
        });
      } catch (cognitoError: any) {
        logger.error('Failed to sign up user in Cognito:', {
          email: userData.email,
          error: cognitoError.message,
        });

        return {
          success: false,
          error: {
            code: 'COGNITO_CREATION_ERROR',
            message:
              cognitoError.message || 'Failed to sign up user in Cognito',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Step 2: Only if Cognito succeeds, create user in our database
      logger.info('Creating user in database with Cognito UserSub:', {
        email: userData.email,
        userSub: cognitoResponse.userSub,
      });

      try {
        // Create user data with Cognito UserSub
        const userDataWithCognito = {
          ...userData,
          cognitoUserId: cognitoResponse.userSub,
        };

        //send mail for confirmation account
        sendWelcomeEmail(userData.email);
        async function sendWelcomeEmail(toEmail: string) {
          try {
            const cognitoResponseEncrypted = encryptData(
              cognitoResponse.userSub
            );
            const decryptRes = decryptData(
              cognitoResponseEncrypted
            );
            console.log(decryptRes, "decryptRes");
            console.log(cognitoResponseEncrypted,"cognitoResponseEncrypted");
            const templatePath = path.resolve(
              __dirname,
              '../../templates/confirmationEmail.ejs'
            );
            const template = await fs.readFile(templatePath, 'utf8');
            const htmlContent = ejs.render(template, {
              confirmationLink:
                process.env.FRONTEND_URL +
                '/confirmaccount?username=' +
                cognitoResponseEncrypted,
            });

            const subject = 'Welcome! Please Confirm Your Email';
            const isSent = await sendMail(htmlContent, toEmail, subject);

            if (isSent) {
              console.log('Confirmation Email sent successfully!');
            } else {
              console.log('Failed to send email.');
            }
          } catch (error) {
            console.error(
              'Error reading email template or sending email:',
              error
            );
          }
        }

        // Create user in database
        const newUser = await this.userModel.createUser(userDataWithCognito);

        logger.info(`Successfully created complete user: ${newUser.email}`, {
          userId: newUser.id,
          cognitoUserId: newUser.cognitoUserId,
        });

        // Log additional Cognito information for debugging
        logger.info('User creation completed successfully:', {
          userId: newUser.id,
          email: newUser.email,
          cognitoUserSub: cognitoResponse.userSub,
          welcomeEmailSent: true,
        });

        return {
          success: true,
          data: newUser,
        };
      } catch (dbError: any) {
        // If database creation fails, we need to clean up the Cognito user to maintain consistency
        logger.error(
          'Database creation failed after Cognito success - initiating rollback:',
          {
            email: userData.email,
            cognitoUserSub: cognitoResponse.userSub,
            error: dbError.message,
          }
        );

        // Rollback: Delete the user from Cognito since database creation failed
        // Use the same username that was used to create the user (phone or email)
        const cognitoUsername = userData.phone!;

        try {
          await deleteUserFromCognito(cognitoUsername, true); // Pass true to indicate this is a rollback operation
          logger.info(
            'Successfully rolled back Cognito user after database failure:',
            {
              email: userData.email,
              cognitoUsername: cognitoUsername,
              cognitoUserSub: cognitoResponse.userSub,
            }
          );
        } catch (rollbackError: any) {
          // Log rollback failure but don't throw - we still need to return the original database error
          logger.error(
            'CRITICAL: Failed to rollback Cognito user after database failure:',
            {
              email: userData.email,
              cognitoUsername: cognitoUsername,
              cognitoUserSub: cognitoResponse.userSub,
              rollbackError: rollbackError.message,
              originalDbError: dbError.message,
            }
          );

          // Return error with both database and rollback failure information
          return {
            success: false,
            error: {
              code: 'DATABASE_CREATION_ERROR_WITH_ROLLBACK_FAILURE',
              message:
                'User created in Cognito but failed to save to database. Additionally, failed to clean up Cognito user. Please contact support immediately.',
              timestamp: new Date().toISOString(),
              details: {
                cognitoUserSub: cognitoResponse.userSub,
                cognitoUsername: cognitoUsername,
                dbError: dbError.message,
                rollbackError: rollbackError.message,
              },
            },
          };
        }

        // Return error indicating successful rollback
        return {
          success: false,
          error: {
            code: 'DATABASE_CREATION_ERROR',
            message:
              'Failed to save user to database. Cognito user has been automatically removed to maintain consistency.',
            timestamp: new Date().toISOString(),
            details: {
              cognitoUserSub: cognitoResponse.userSub,
              cognitoUsername: cognitoUsername,
              dbError: dbError.message,
              rollbackSuccessful: true,
            },
          },
        };
      }
    } catch (error: any) {
      logger.error('Unexpected error in UserService.createUser:', error);
      return {
        success: false,
        error: {
          code: 'USER_CREATION_ERROR',
          message: 'An unexpected error occurred while creating the user',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Update user
   */
  async updateUser(
    id: string,
    userData: UpdateUserRequest
  ): Promise<ApiResponse<AdminUser>> {
    try {
      logger.info(`Updating user with ID: ${id}`, userData);

      if (!id || typeof id !== 'string') {
        return {
          success: false,
          error: {
            code: 'INVALID_USER_ID',
            message: 'Valid user ID is required',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Check if user exists
      const existingUser = await this.userModel.getUserById(id);
      if (!existingUser) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Check if email is being changed and if it already exists
      if (userData.email && userData.email !== existingUser.email) {
        const existingUsers = await this.userModel.getUsers({
          search: userData.email,
          limit: 1,
        });

        if (existingUsers.users.length > 0) {
          return {
            success: false,
            error: {
              code: 'EMAIL_ALREADY_EXISTS',
              message: 'User with this email already exists',
              timestamp: new Date().toISOString(),
            },
          };
        }
      }

      const updatedUser = await this.userModel.updateUser(id, userData);

      if (!updatedUser) {
        return {
          success: false,
          error: {
            code: 'USER_UPDATE_ERROR',
            message: 'Failed to update user',
            timestamp: new Date().toISOString(),
          },
        };
      }

      logger.info(`Successfully updated user: ${updatedUser.email}`);

      return {
        success: true,
        data: updatedUser,
      };
    } catch (error) {
      logger.error('Error in UserService.updateUser:', error);
      return {
        success: false,
        error: {
          code: 'USER_UPDATE_ERROR',
          message: 'Failed to update user',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Delete user (soft delete)
   */
  async deleteUser(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    try {
      logger.info(`Deleting user with ID: ${id}`);

      if (!id || typeof id !== 'string') {
        return {
          success: false,
          error: {
            code: 'INVALID_USER_ID',
            message: 'Valid user ID is required',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Check if user exists
      const existingUser = await this.userModel.getUserById(id);
      if (!existingUser) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            timestamp: new Date().toISOString(),
          },
        };
      }

      const deleted = await this.userModel.deleteUser(id);

      if (!deleted) {
        return {
          success: false,
          error: {
            code: 'USER_DELETE_ERROR',
            message: 'Failed to delete user',
            timestamp: new Date().toISOString(),
          },
        };
      }

      logger.info(`Successfully deleted user: ${existingUser.email}`);

      return {
        success: true,
        data: { deleted: true },
      };
    } catch (error) {
      logger.error('Error in UserService.deleteUser:', error);
      return {
        success: false,
        error: {
          code: 'USER_DELETE_ERROR',
          message: 'Failed to delete user',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get user statistics
   */
  async getUserStatistics(): Promise<ApiResponse<UserStatistics>> {
    try {
      logger.info('Fetching user statistics');

      const statistics = await this.userModel.getUserStatistics();

      logger.info('Successfully fetched user statistics');

      return {
        success: true,
        data: statistics,
      };
    } catch (error) {
      logger.error('Error in UserService.getUserStatistics:', error);
      return {
        success: false,
        error: {
          code: 'STATISTICS_FETCH_ERROR',
          message: 'Failed to fetch user statistics',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Suspend user
   */
  async suspendUser(id: string): Promise<ApiResponse<AdminUser>> {
    return this.updateUser(id, { status: 'suspended' as any });
  }

  /**
   * Activate user
   */
  async activateUser(id: string): Promise<ApiResponse<AdminUser>> {
    return this.updateUser(id, { status: 'active' as any });
  }

  /**
   * Get companies list for filtering
   * Now uses real database data instead of mock data
   */
  async getCompanies(): Promise<ApiResponse<string[]>> {
    try {
      logger.info('Fetching companies from database for filtering');

      // Use the new method from UserModel to get real company data
      const companies = await this.userModel.getUniqueCompanies();

      logger.info(`Successfully fetched ${companies.length} unique companies`);

      return {
        success: true,
        data: companies,
      };
    } catch (error) {
      logger.error('Error in UserService.getCompanies:', error);
      return {
        success: false,
        error: {
          code: 'COMPANIES_FETCH_ERROR',
          message: 'Failed to fetch companies from database',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }
}
