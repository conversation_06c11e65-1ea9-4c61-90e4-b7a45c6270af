-- =====================================================
-- ADD PHONE NUMBER TO USER REFERENCES TABLE
-- Migration to add phone_number field to shared_data.user_references
-- =====================================================

-- Add phone_number column to user_references table
ALTER TABLE shared_data.user_references 
ADD COLUMN phone_number VARCHAR(20);

-- Add index for phone_number for better query performance
CREATE INDEX IF NOT EXISTS idx_user_references_phone ON shared_data.user_references(phone_number);

-- Add comment for documentation
COMMENT ON COLUMN shared_data.user_references.phone_number IS 'User phone number for contact and authentication purposes';

-- Update the user_management_view to include phone_number
DROP VIEW IF EXISTS command_center.user_management_view;
CREATE VIEW command_center.user_management_view AS
SELECT 
  ur.id,
  ur.cognito_user_id,
  ur.first_name,
  ur.last_name,
  ur.email,
  ur.phone_number,
  ur.user_type,
  ur.service_user_id,
  ur.status,
  ur.created_at,
  ur.updated_at,
  -- Role mapping for display
  CASE 
    WHEN ur.user_type = 'member' THEN 'member'
    WHEN ur.user_type = 'employer' THEN 'employer'
    WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
    WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
    WHEN ur.user_type = 'system_admin' THEN 'system-admin'
    ELSE ur.user_type
  END as role,
  -- Mock company data
  CASE 
    WHEN ur.user_type = 'member' THEN 'TechCorp Solutions'
    WHEN ur.user_type = 'employer' THEN 'Global Health Corp'
    WHEN ur.user_type = 'wellness_coach' THEN 'WellnessTech Inc'
    ELSE 'Platform Administration'
  END as company,
  -- Mock subscription data
  CASE 
    WHEN ur.user_type = 'member' THEN 'Premium Health Plan'
    WHEN ur.user_type = 'employer' THEN 'Enterprise'
    ELSE NULL
  END as subscription,
  -- Activity indicators
  CASE 
    WHEN ur.updated_at > NOW() - INTERVAL '1 hour' THEN '1 hour ago'
    WHEN ur.updated_at > NOW() - INTERVAL '3 hours' THEN '3 hours ago'
    WHEN ur.updated_at > NOW() - INTERVAL '1 day' THEN '1 day ago'
    ELSE 'More than 1 day ago'
  END as last_active
FROM shared_data.user_references ur
WHERE ur.status != 'deleted';

-- Update search function to include phone_number in search
DROP FUNCTION IF EXISTS command_center.search_users(text, text, text, integer, integer);
CREATE OR REPLACE FUNCTION command_center.search_users(
  search_term text DEFAULT NULL,
  filter_role text DEFAULT NULL,
  filter_status text DEFAULT NULL,
  page_limit integer DEFAULT 20,
  page_offset integer DEFAULT 0
)
RETURNS TABLE (
  id uuid,
  cognito_user_id varchar,
  first_name varchar,
  last_name varchar,
  email varchar,
  phone_number varchar,
  user_type varchar,
  role text,
  status varchar,
  company text,
  subscription text,
  last_active text,
  created_at timestamp,
  updated_at timestamp,
  total_count bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ur.id,
    ur.cognito_user_id,
    ur.first_name,
    ur.last_name,
    ur.email,
    ur.phone_number,
    ur.user_type,
    CASE 
      WHEN ur.user_type = 'member' THEN 'member'
      WHEN ur.user_type = 'employer' THEN 'employer'
      WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
      WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
      WHEN ur.user_type = 'system_admin' THEN 'system-admin'
      ELSE ur.user_type
    END as role,
    ur.status,
    CASE 
      WHEN ur.user_type = 'member' THEN 'TechCorp Solutions'
      WHEN ur.user_type = 'employer' THEN 'Global Health Corp'
      WHEN ur.user_type = 'wellness_coach' THEN 'WellnessTech Inc'
      ELSE 'Platform Administration'
    END as company,
    CASE 
      WHEN ur.user_type = 'member' THEN 'Premium Health Plan'
      WHEN ur.user_type = 'employer' THEN 'Enterprise'
      ELSE NULL
    END as subscription,
    CASE 
      WHEN ur.updated_at > NOW() - INTERVAL '1 hour' THEN '1 hour ago'
      WHEN ur.updated_at > NOW() - INTERVAL '3 hours' THEN '3 hours ago'
      WHEN ur.updated_at > NOW() - INTERVAL '1 day' THEN '1 day ago'
      ELSE 'More than 1 day ago'
    END as last_active,
    ur.created_at,
    ur.updated_at,
    COUNT(*) OVER() as total_count
  FROM shared_data.user_references ur
  WHERE 
    ur.status != 'deleted'
    AND (search_term IS NULL OR (
      ur.first_name ILIKE '%' || search_term || '%' OR
      ur.last_name ILIKE '%' || search_term || '%' OR
      ur.email ILIKE '%' || search_term || '%' OR
      ur.phone_number ILIKE '%' || search_term || '%'
    ))
    AND (filter_role IS NULL OR ur.user_type = filter_role)
    AND (filter_status IS NULL OR ur.status = filter_status)
  ORDER BY ur.created_at DESC
  LIMIT page_limit OFFSET page_offset;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions for the updated view and function
GRANT SELECT ON command_center.user_management_view TO postgres;
GRANT EXECUTE ON FUNCTION command_center.search_users(text, text, text, integer, integer) TO postgres;

-- Add comment for migration tracking
COMMENT ON COLUMN shared_data.user_references.phone_number IS 'Added in migration 08_add_phone_number_to_user_references.sql - User phone number for contact and authentication purposes';
