#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';

// Required environment variables (Single Database Architecture with Root .env Only)
const requiredEnvVars: string[] = [
  // Database Configuration
  'DB_HOST',
  'DB_PORT',
  'DB_NAME',
  'DB_USER',
  'DB_PASSWORD',

  // JWT Configuration
  'JWT_SECRET',
  'SERVICE_JWT_SECRET',

  // API Gateway Configuration
  'API_GATEWAY_PORT',
  'CORS_ORIGINS',

  // Service Ports
  'MEMBER_PORT',
  'EMPLOYER_PORT',
  'WELLNESS_CENTRAL_PORT',
  'ZENX_LMS_PORT',
  'COMMAND_CENTER_PORT',

  // Service URLs
  'MEMBER_SERVICE_URL',
  'EMPLOYER_SERVICE_URL',
  'WELLNESS_SERVICE_URL',
  'LMS_SERVICE_URL',
  'COMMAND_CENTER_SERVICE_URL',
];

// Optional environment variables with defaults
const optionalEnvVars: Record<string, string> = {
  NODE_ENV: 'development',
  LOG_LEVEL: 'info',
  DB_SSL: 'false',
  DB_CONNECTION_LIMIT: '10',
  DB_IDLE_TIMEOUT: '30000',
  DB_CONNECTION_TIMEOUT: '2000',
  RATE_LIMIT_WINDOW_MS: '900000',
  RATE_LIMIT_MAX_REQUESTS: '100',
  AWS_REGION: 'us-east-1',
  ENABLE_METRICS: 'false',
  METRICS_PORT: '9090',
};

function loadEnvFile(filePath: string): Record<string, string> | null {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env: Record<string, string> = {};

    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });

    return env;
  } catch (error) {
    return null;
  }
}

function validateEnvironment(): boolean {
  console.log(`🔍 Validating root environment configuration`);

  // Load environment variables from root .env only
  const env = loadEnvFile('.env');

  if (!env) {
    console.log(`   ⚠️  No .env file found at project root`);

    if (fs.existsSync('.env.example')) {
      console.log(`   💡 Found .env.example - copy it to .env and configure`);
    }

    return false;
  }

  console.log(`   ✅ Found .env file at project root`);

  // Check required variables
  const missing: string[] = [];
  const present: string[] = [];

  requiredEnvVars.forEach(varName => {
    if (!env[varName] || env[varName].trim() === '') {
      missing.push(varName);
    } else {
      present.push(varName);
    }
  });

  // Report results
  if (present.length > 0) {
    console.log(`   ✅ Required variables present: ${present.length}`);
    present.forEach(varName => {
      console.log(`      ✓ ${varName}`);
    });
  }

  if (missing.length > 0) {
    console.log(`   ❌ Missing required variables: ${missing.length}`);
    missing.forEach(varName => {
      console.log(`      ✗ ${varName}`);
    });
  }

  // Check optional variables
  const optionalMissing: Array<{ varName: string; defaultValue: string }> = [];
  Object.entries(optionalEnvVars).forEach(([varName, defaultValue]) => {
    if (!env[varName]) {
      optionalMissing.push({ varName, defaultValue });
    }
  });

  if (optionalMissing.length > 0) {
    console.log(`   ⚠️  Optional variables using defaults: ${optionalMissing.length}`);
    optionalMissing.forEach(({ varName, defaultValue }) => {
      console.log(`      ~ ${varName} (default: ${defaultValue})`);
    });
  }

  return missing.length === 0;
}

function validateDatabaseConnection(): boolean {
  console.log(`🔗 Validating database connection configuration`);

  // Database variables required in root .env
  const dbVars = [
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD'
  ];

  // Load environment from root .env only
  const env = loadEnvFile('.env');

  if (!env) {
    console.log(`   ❌ Cannot validate database - no .env file`);
    return false;
  }

  const missingDbVars = dbVars.filter(varName => !env[varName]);

  if (missingDbVars.length > 0) {
    console.log(`   ❌ Missing database variables: ${missingDbVars.join(', ')}`);
    return false;
  }

  console.log(`   ✅ Database variables present`);
  console.log(`   📍 Database: ${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}`);

  // Check schema configurations
  const schemaVars = [
    'SHARED_SCHEMA',
    'MEMBER_SCHEMA',
    'EMPLOYER_SCHEMA',
    'WELLNESS_SCHEMA',
    'LMS_SCHEMA',
    'COMMAND_CENTER_SCHEMA'
  ];

  const missingSchemas = schemaVars.filter(varName => !env[varName]);
  if (missingSchemas.length > 0) {
    console.log(`   ⚠️  Missing schema configurations: ${missingSchemas.join(', ')}`);
  } else {
    console.log(`   ✅ All schema configurations present`);
  }

  return true;
}

function validateServiceUrls(): boolean {
  console.log(`🌐 Validating service URLs...`);

  const env = loadEnvFile('.env');

  if (!env) {
    console.log(`   ❌ Cannot validate URLs - no .env file found`);
    return false;
  }

  const serviceUrls = [
    'MEMBER_SERVICE_URL',
    'EMPLOYER_SERVICE_URL',
    'WELLNESS_SERVICE_URL',
    'LMS_SERVICE_URL',
    'COMMAND_CENTER_SERVICE_URL',
  ];

  let allValid = true;

  serviceUrls.forEach(urlVar => {
    const url = env[urlVar];
    if (!url) {
      console.log(`   ❌ Missing: ${urlVar}`);
      allValid = false;
    } else {
      try {
        new URL(url);
        console.log(`   ✅ Valid: ${urlVar} = ${url}`);
      } catch (error) {
        console.log(`   ❌ Invalid URL: ${urlVar} = ${url}`);
        allValid = false;
      }
    }
  });

  return allValid;
}

function generateEnvTemplate(): void {
  console.log(`📄 Generating .env template`);

  const template = [
    `# Aperion Health Environment Configuration`,
    `# Generated on ${new Date().toISOString()}`,
    '',
    '# Required Variables',
    ...requiredEnvVars.map(varName => `${varName}=`),
    '',
    '# Optional Variables (with defaults)',
    ...Object.entries(optionalEnvVars).map(([varName, defaultValue]) =>
      `# ${varName}=${defaultValue}`
    ),
    ''
  ].join('\n');

  fs.writeFileSync('.env.template', template);
  console.log(`   ✅ Template created: .env.template`);
}

function main(): void {
  console.log('🔧 Aperion Health Environment Validation\n');

  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'validate':
      console.log('🔍 Validating root environment configuration...\n');

      let allValid = true;

      // Validate root environment
      allValid = validateEnvironment() && allValid;
      console.log('');

      // Validate database configuration
      allValid = validateDatabaseConnection() && allValid;
      console.log('');

      // Validate service URLs
      allValid = validateServiceUrls() && allValid;
      console.log('');

      if (allValid) {
        console.log('🎉 All environment validations passed!');
      } else {
        console.log('❌ Some environment validations failed. Please fix the issues above.');
        process.exit(1);
      }
      break;

    case 'template':
      generateEnvTemplate();
      break;

    case 'db':
      validateDatabaseConnection();
      break;

    default:
      console.log('Usage:');
      console.log('  tsx scripts/validate-env.ts validate    # Validate environment');
      console.log('  tsx scripts/validate-env.ts template    # Generate .env template');
      console.log('  tsx scripts/validate-env.ts db          # Validate database config');
      console.log('');
      console.log('Examples:');
      console.log('  tsx scripts/validate-env.ts validate    # Validate root .env');
      console.log('  tsx scripts/validate-env.ts template    # Generate .env template');
      console.log('  tsx scripts/validate-env.ts db          # Check database config');
  }
}

if (require.main === module) {
  main();
}

export { validateEnvironment, validateDatabaseConnection, validateServiceUrls, generateEnvTemplate };
