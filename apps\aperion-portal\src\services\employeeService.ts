import apiClient from './apiClient';

// API base URL - this will be proxied to the employer service via API Gateway
// Note: apiClient already has baseURL '/api', so we only need the service path
const API_BASE_URL = '/employer';

export interface Employee {
  id: string;
  employerId: string;
  memberId: string;
  employeeId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  departmentId?: string;
  departmentName?: string; // Department name from JOIN query
  jobTitle?: string;
  hireDate?: string;
  employmentStatus: 'active' | 'inactive' | 'terminated' | 'on_leave' | 'pending';
  managerId?: string;
  salaryBand?: string;
  benefitsEligible: boolean;
  wellnessProgramEnrolled: boolean;
  lastHealthScreening?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateEmployeeRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string | undefined;
  department: string;
  role: string;
  healthPlan: string;
}

export interface CreateEmployeeResponse {
  employee: Employee;
  cognitoUserSub: string;
  temporaryPassword: string;
  isTemporary: boolean;
}

export interface Department {
  id: string;
  employerId: string;
  name: string;
  description?: string;
  managerEmail?: string;
  budget?: number;
  memberCount: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface HealthPlan {
  id: string;
  name: string;
  description?: string;
  type: 'basic' | 'standard' | 'premium';
  monthlyPremium?: number;
  deductible?: number;
  status: 'active' | 'inactive';
}

export interface EmployeeQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  department?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
  };
  meta?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

class EmployeeService {
  /**
   * Register a new employee following Command Center pattern (Cognito + Database)
   */
  async registerEmployee(employeeData: CreateEmployeeRequest): Promise<ApiResponse<{ id?: string; member_id: string; email: string; phone?: string; role: string; status: string; message: string }>> {
    try {
      console.log('Registering employee via Command Center pattern:', employeeData);

      const response = await apiClient.post(`${API_BASE_URL}/employees`, employeeData);

      console.log('Register employee response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('Error registering employee:', error);

      // Handle API error responses
      if (error.response?.data) {
        return error.response.data;
      }

      // Handle network or other errors
      return {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: error.message || 'Failed to register employee',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Register a new employee in Cognito only (legacy method)
   */
  async registerEmployeeCognitoOnly(employeeData: CreateEmployeeRequest): Promise<ApiResponse<{ member_id: string; message: string }>> {
    try {
      console.log('Registering employee via user-management API (Cognito-only):', employeeData);

      const response = await apiClient.post('/user-management/employees', employeeData);

      console.log('Register employee response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('Error registering employee:', error);

      // Handle API error responses
      if (error.response?.data) {
        return error.response.data;
      }

      // Handle network or other errors
      return {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: error.message || 'Failed to register employee',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Create a new employee (legacy method - kept for backward compatibility)
   */
  async createEmployee(employeeData: CreateEmployeeRequest): Promise<ApiResponse<CreateEmployeeResponse>> {
    try {
      console.log('Creating employee via API:', employeeData);

      const response = await apiClient.post(`${API_BASE_URL}/employees`, employeeData);

      console.log('Create employee response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('Error creating employee:', error);

      // Handle API error responses
      if (error.response?.data) {
        return error.response.data;
      }

      // Handle network or other errors
      return {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: error.message || 'Failed to create employee',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get employees with pagination and filtering
   */
  async getEmployees(params: EmployeeQueryParams = {}): Promise<ApiResponse<Employee[]>> {
    try {
      console.log('Fetching employees from:', `${API_BASE_URL}/employees`, 'with params:', params);

      const response = await apiClient.get(`${API_BASE_URL}/employees`, { params });

      console.log('Employees API response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('Error fetching employees:', error);

      // Handle API error responses
      if (error.response?.data) {
        return error.response.data;
      }

      // Return fallback mock data for development
      console.log('Falling back to mock data due to error:', error.message);
      return this.getMockEmployees(params);
    }
  }

  /**
   * Get current (non-pending) employees with pagination and filtering
   */
  async getCurrentEmployees(params: EmployeeQueryParams = {}): Promise<ApiResponse<Employee[]>> {
    try {
      console.log('Fetching current employees from:', `${API_BASE_URL}/employees/current`, 'with params:', params);

      const response = await apiClient.get(`${API_BASE_URL}/employees/current`, { params });

      console.log('Current employees API response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('Error fetching current employees:', error);

      // Handle API error responses
      if (error.response?.data) {
        return error.response.data;
      }

      // Return fallback mock data for development (filtered to exclude pending)
      console.log('Falling back to mock data due to error:', error.message);
      const mockResponse = this.getMockEmployees(params);
      if (mockResponse.success && mockResponse.data) {
        // Filter out pending employees from mock data
        mockResponse.data = mockResponse.data.filter(emp => emp.employmentStatus !== 'pending');
      }
      return mockResponse;
    }
  }

  /**
   * Get pending employees with pagination and filtering
   */
  async getPendingEmployees(params: EmployeeQueryParams = {}): Promise<ApiResponse<Employee[]>> {
    try {
      console.log('Fetching pending employees from:', `${API_BASE_URL}/employees/pending`, 'with params:', params);

      const response = await apiClient.get(`${API_BASE_URL}/employees/pending`, { params });

      console.log('Pending employees API response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('Error fetching pending employees:', error);

      // Handle API error responses
      if (error.response?.data) {
        return error.response.data;
      }

      // Return fallback mock data for development (filtered to only pending)
      console.log('Falling back to mock data due to error:', error.message);
      const mockResponse = this.getMockEmployees(params);
      if (mockResponse.success && mockResponse.data) {
        // Filter to only pending employees from mock data
        mockResponse.data = mockResponse.data.filter(emp => emp.employmentStatus === 'pending');
      }
      return mockResponse;
    }
  }

  /**
   * Get departments for the current employer
   * Using hardcoded departments instead of API call (departments endpoint temporarily disabled)
   */
  async getDepartments(): Promise<ApiResponse<Department[]>> {
    console.log('Using hardcoded departments (API endpoint disabled)');

    // Return hardcoded departments directly
    return {
      success: true,
      data: [
        { id: '1', employerId: 'current', name: 'Engineering', memberCount: 15, status: 'active' as const, createdAt: '2023-01-01', updatedAt: '2023-01-01' },
        { id: '2', employerId: 'current', name: 'Marketing', memberCount: 8, status: 'active' as const, createdAt: '2023-01-01', updatedAt: '2023-01-01' },
        { id: '3', employerId: 'current', name: 'Finance', memberCount: 5, status: 'active' as const, createdAt: '2023-01-01', updatedAt: '2023-01-01' },
        { id: '4', employerId: 'current', name: 'HR', memberCount: 3, status: 'active' as const, createdAt: '2023-01-01', updatedAt: '2023-01-01' },
        { id: '5', employerId: 'current', name: 'Sales', memberCount: 12, status: 'active' as const, createdAt: '2023-01-01', updatedAt: '2023-01-01' },
        { id: '6', employerId: 'current', name: 'Customer Success', memberCount: 6, status: 'active' as const, createdAt: '2023-01-01', updatedAt: '2023-01-01' },
        { id: '7', employerId: 'current', name: 'Product', memberCount: 10, status: 'active' as const, createdAt: '2023-01-01', updatedAt: '2023-01-01' }
      ]
    };
  }

  /**
   * Delete pending employee invitation
   */
  async deleteInvitation(employeeId: string): Promise<ApiResponse<{ message: string }>> {
    try {
      console.log('Deleting employee invitation:', employeeId);

      const response = await apiClient.delete(`${API_BASE_URL}/employees/pending/${employeeId}`);

      console.log('Delete invitation API response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('Error deleting employee invitation:', error);

      // Handle API error responses
      if (error.response?.data) {
        return error.response.data;
      }

      // Return error response for development
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: error.message || 'Failed to delete employee invitation',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get available health plans
   */
  async getHealthPlans(): Promise<ApiResponse<HealthPlan[]>> {
    try {
      console.log('Fetching health plans from:', `${API_BASE_URL}/health-plans`);

      const response = await apiClient.get(`${API_BASE_URL}/health-plans`);

      console.log('Health plans API response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('Error fetching health plans:', error);

      // Handle API error responses
      if (error.response?.data) {
        return error.response.data;
      }

      // Return fallback mock data for development
      console.log('Falling back to mock health plans due to error:', error.message);
      return {
        success: true,
        data: [
          { id: '1', name: 'Basic', type: 'basic' as const, monthlyPremium: 150, deductible: 2000, status: 'active' as const },
          { id: '2', name: 'Standard', type: 'standard' as const, monthlyPremium: 250, deductible: 1000, status: 'active' as const },
          { id: '3', name: 'Premium', type: 'premium' as const, monthlyPremium: 400, deductible: 500, status: 'active' as const }
        ]
      };
    }
  }

  /**
   * Mock employees data for development fallback
   */
  private getMockEmployees(params: EmployeeQueryParams): ApiResponse<Employee[]> {
    const mockEmployees: Employee[] = [
      {
        id: '1',
        employerId: 'mock-employer',
        memberId: 'mock-member-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
        departmentId: '1',
        departmentName: 'Engineering',
        jobTitle: 'Software Engineer',
        hireDate: '2021-05-12',
        employmentStatus: 'active',
        benefitsEligible: true,
        wellnessProgramEnrolled: true,
        createdAt: '2021-05-12T00:00:00Z',
        updatedAt: '2021-05-12T00:00:00Z'
      },
      {
        id: '2',
        employerId: 'mock-employer',
        memberId: 'mock-member-2',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        departmentId: '2',
        departmentName: 'Marketing',
        jobTitle: 'Marketing Manager',
        hireDate: '2020-11-03',
        employmentStatus: 'active',
        benefitsEligible: true,
        wellnessProgramEnrolled: false,
        createdAt: '2020-11-03T00:00:00Z',
        updatedAt: '2020-11-03T00:00:00Z'
      },
      {
        id: '3',
        employerId: 'mock-employer',
        memberId: 'mock-member-3',
        firstName: 'Thomas',
        lastName: 'Anderson',
        email: '<EMAIL>',
        departmentId: '1',
        departmentName: 'Engineering',
        jobTitle: 'Frontend Developer',
        employmentStatus: 'pending',
        benefitsEligible: true,
        wellnessProgramEnrolled: false,
        createdAt: '2023-05-10T00:00:00Z',
        updatedAt: '2023-05-10T00:00:00Z'
      },
      {
        id: '4',
        employerId: 'mock-employer',
        memberId: 'mock-member-4',
        firstName: 'Jessica',
        lastName: 'Taylor',
        email: '<EMAIL>',
        departmentId: '7',
        departmentName: 'Product',
        jobTitle: 'Product Manager',
        employmentStatus: 'pending',
        benefitsEligible: true,
        wellnessProgramEnrolled: false,
        createdAt: '2023-05-12T00:00:00Z',
        updatedAt: '2023-05-12T00:00:00Z'
      }
    ];

    // Apply basic filtering for mock data
    let filteredEmployees = mockEmployees;
    
    if (params.search) {
      const search = params.search.toLowerCase();
      filteredEmployees = filteredEmployees.filter(emp =>
        emp.firstName.toLowerCase().includes(search) ||
        emp.lastName.toLowerCase().includes(search) ||
        emp.email.toLowerCase().includes(search)
      );
    }

    return {
      success: true,
      data: filteredEmployees,
      meta: {
        page: params.page || 1,
        limit: params.limit || 20,
        total: filteredEmployees.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  }
}

export const employeeService = new EmployeeService();
