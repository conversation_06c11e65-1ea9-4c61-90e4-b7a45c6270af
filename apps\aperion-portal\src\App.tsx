import { Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import { Toaster } from './components/ui/toaster'
import ProtectedRoute from './components/ProtectedRoute'
import RoleBasedRoute from './components/RoleBasedRoute'
import { UserRole } from '@aperion/shared'

//Home page
import HomePage from './pages/HomePage'

//Varification Account Page
import VerificationPage from './pages/verification/VerificationPage'

// Auth pages
// import LoginPage from './pages/auth/step1Page'
// import SignupPage from './pages/auth/SignupPage'
import ActivationPage from './pages/auth/ActivationPage'
import Auth1Page from './pages/auth/Auth1Page'
import Auth2Page from './pages/auth/Auth2Page'
import RegisterPage from './pages/auth/RegisterPage'

// Role-based layouts
import MemberLayout from './layouts/MemberLayout'
// import EmployerLayout from './layouts/EmployerLayout'
import { EmployerLayout } from './layouts/EmployerLayout'
import WellnessLayout from './layouts/WellnessLayout'
import LMSLayout from './layouts/LMSLayout'
import {CommandCenterLayout} from './components/command-center/CommandCenterLayout'

// Member pages
import MemberDashboard from './pages/member/Dashboard'
import MemberProfile from './pages/member/Profile'

// Employer pages
import EmployerDashboard from './pages/employer/Dashboard'
import EmployeeManagement from './pages/employer/EmployeeManagement'
import EmployeeInsurance from './pages/employer/EmployeeInsurance'
import EmployerSettings from './pages/employer/Settings'
import EmployerReports from './pages/employer/Reports'

// Wellness pages
import WellnessDashboard from './pages/wellness/Dashboard'
import MembersList from './pages/wellness/MembersList'

// LMS pages
import LMSDashboard from './pages/lms/Dashboard'
import LearningModules from './pages/lms/LearningModules'

// Admin pages
import AdminDashboard from './pages/admin/Dashboard'
import { UserManagement } from './pages/admin/UserManagement';



function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-background">
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/auth/activate" element={<ActivationPage />} />
          <Route path="/auth/send-code" element={<Auth1Page />} />
          <Route path="/auth/verify-code" element={<Auth2Page />} />
          <Route path="/auth/register" element={<RegisterPage />} />
         <Route path="/confirmaccount" element={<VerificationPage />} />

          {/* Protected role-based routes */}
          <Route path="/member/*" element={
            <ProtectedRoute>
              <RoleBasedRoute allowedRoles={[UserRole.MEMBER]}>
                <MemberLayout />
              </RoleBasedRoute>
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<MemberDashboard />} />
            <Route path="profile" element={<MemberProfile />} />
            <Route path="dependents" element={<div>Dependents Page</div>} />
            <Route path="health-plan" element={<div>Health Plan Page</div>} />
            <Route path="wellness-goals" element={<div>Wellness Goals Page</div>} />
            <Route path="recommendations" element={<div>Recommendations Page</div>} />
            <Route path="rewards" element={<div>Rewards Page</div>} />
            <Route path="support" element={<div>Support Page</div>} />
          </Route>

          <Route path="/employer/*" element={
            <ProtectedRoute>
              <RoleBasedRoute allowedRoles={[UserRole.EMPLOYER]}>
                <EmployerLayout />
              </RoleBasedRoute>
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<EmployerDashboard />} />
            <Route path="employees" element={<EmployeeManagement />} />
            <Route path="onboard" element={<EmployeeInsurance />} />
            <Route path="settings" element={<EmployerSettings />} />
            <Route path="reports" element={<EmployerReports />} />
          </Route>

          <Route path="/wellness/*" element={
            <ProtectedRoute>
              <RoleBasedRoute allowedRoles={[UserRole.WELLNESS_COACH]}>
                <WellnessLayout />
              </RoleBasedRoute>
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<WellnessDashboard />} />
            <Route path="members" element={<MembersList />} />
          </Route>

          <Route path="/lms/*" element={
            <ProtectedRoute>
              <RoleBasedRoute allowedRoles={[UserRole.CONTENT_CREATOR]}>
                <LMSLayout />
              </RoleBasedRoute>
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<LMSDashboard />} />
            <Route path="modules" element={<LearningModules />} />
          </Route>

          <Route path="/admin/*" element={
            <ProtectedRoute>
              <RoleBasedRoute allowedRoles={[UserRole.SYSTEM_ADMIN]}>
                <CommandCenterLayout />
              </RoleBasedRoute>
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<AdminDashboard />} />
            <Route path="users" element={<UserManagement />} />
          </Route>

          {/* Default redirect based on user role */}
          <Route path="/" element={<Navigate to="/" replace />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
        <Toaster />
      </div>
    </AuthProvider>
  )
}

export default App
