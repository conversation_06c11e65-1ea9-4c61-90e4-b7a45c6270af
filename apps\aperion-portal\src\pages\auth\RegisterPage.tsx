import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import {
  ArrowRight,
  ArrowLeft,
  Mail,
  Phone,
  AlertCircle,
  Heart,
  Shield,
  Users,
  Activity,
  UserPlus,
  Calendar,
  Lock,
  Eye,
  EyeOff,
  CheckCircle,
  Building,
} from 'lucide-react';

// Validation schemas
const basicInfoSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters'),
  phoneCountryCode: z
    .string()
    .min(1, 'Country code is required'),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || /^[\d\s\-\(\)]+$/.test(val), 'Please enter a valid phone number'),
  dateOfBirth: z
    .string()
    .min(1, 'Date of birth is required')
    .refine((val) => {
      if (!val) return false;
      const date = new Date(val);
      const today = new Date();
      const age = today.getFullYear() - date.getFullYear();
      return age >= 13 && age <= 120;
    }, 'You must be between 13 and 120 years old'),
  gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say'], {
    required_error: 'Gender selection is required',
    invalid_type_error: 'Please select a valid gender option',
  }),
}).refine((data) => {
  // If phone is provided, validate the combined country code + phone number
  if (data.phone && data.phone.trim()) {
    const fullPhone = data.phoneCountryCode + data.phone.replace(/\s/g, '');
    return /^\+[1-9]\d{1,14}$/.test(fullPhone);
  }
  return true;
}, {
  message: "Please enter a valid phone number",
  path: ["phone"],
});

const accountSetupSchema = z.object({
  // Address fields
  streetAddress: z
    .string()
    .min(1, 'Street address is required')
    .max(100, 'Street address must be less than 100 characters'),
  apartmentUnit: z
    .string()
    .min(1, 'Apartment/unit number is required')
    .max(20, 'Apartment/unit must be less than 20 characters'),
  city: z
    .string()
    .min(1, 'City is required')
    .max(50, 'City must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'City can only contain letters, spaces, hyphens, and apostrophes'),
  state: z
    .string()
    .min(1, 'State/Province is required')
    .max(50, 'State/Province must be less than 50 characters'),
  zipCode: z
    .string()
    .min(1, 'ZIP/Postal code is required')
    .max(20, 'ZIP/Postal code must be less than 20 characters')
    .regex(/^[A-Za-z0-9\s-]+$/, 'Invalid ZIP/Postal code format'),
  country: z
    .string()
    .min(1, 'Country is required')
    .max(50, 'Country must be less than 50 characters'),
});

const privacySchema = z.object({
  dataSharing: z.boolean(),
  marketing: z.boolean(),
  analytics: z.boolean(),
  termsAccepted: z.boolean().refine((val) => val === true, 'You must accept the terms and conditions'),
  privacyAccepted: z.boolean().refine((val) => val === true, 'You must accept the privacy policy'),
});

type BasicInfoFormData = z.infer<typeof basicInfoSchema>;
type AccountSetupFormData = z.infer<typeof accountSetupSchema>;
type PrivacyFormData = z.infer<typeof privacySchema>;

type RegistrationStep = 'basic-info' | 'account-setup' | 'privacy' | 'success';

const RegisterPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<RegistrationStep>('basic-info');
  const [isLoading, setIsLoading] = useState(false);
  const [activationCode, setActivationCode] = useState<string | null>(null);
  const [invitationData, setInvitationData] = useState<any>(null);
  const [registrationData, setRegistrationData] = useState<{
    basicInfo?: BasicInfoFormData;
    accountSetup?: AccountSetupFormData;
    privacy?: PrivacyFormData;
  }>({});

  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();

  // Handle activation code and token from URL parameters
  useEffect(() => {
    const codeParam = searchParams.get('code');
    const emailParam = searchParams.get('email');
    const tokenParam = searchParams.get('token');

    if (codeParam) {
      setActivationCode(codeParam);

      // Validate activation code
      validateActivationCodeFromURL(codeParam, emailParam);
    } else if (tokenParam) {
      // Handle employee invitation token
      validateEmployeeInvitationToken(tokenParam);
    }
  }, [searchParams]);

  const validateActivationCodeFromURL = async (code: string, email: string | null) => {
    try {
      const response = await fetch(`/api/member/validate-activation-code?activationCode=${encodeURIComponent(code)}`);
      const result = await response.json();

      if (response.ok && result.success) {
        setInvitationData(result.data.invitation);

        // Pre-fill form with invitation data
        if (result.data.invitation.email) {
          basicInfoForm.setValue('email', result.data.invitation.email);
        }
        if (result.data.invitation.firstName) {
          basicInfoForm.setValue('firstName', result.data.invitation.firstName);
        }
        if (result.data.invitation.lastName) {
          basicInfoForm.setValue('lastName', result.data.invitation.lastName);
        }

        toast({
          title: "Invitation validated",
          description: `Welcome ${result.data.invitation.firstName || 'to AperionHealth'}! Please complete your registration.`,
        });
      } else {
        toast({
          title: "Invalid invitation",
          description: result.error?.message || "The invitation link is invalid or has expired.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error validating activation code:', error);
      toast({
        title: "Validation error",
        description: "Unable to validate invitation. Please try again.",
        variant: "destructive",
      });
    }
  };

  const validateEmployeeInvitationToken = async (token: string) => {
    try {
      setIsLoading(true);

      // Call the employer portal API to validate the token and get user info
      const response = await fetch(`/api/employer/user-management/validate-invitation-token?token=${encodeURIComponent(token)}`);
      const result = await response.json();

      if (response.ok && result.success) {
        const userData = result.data;

        // Pre-fill form with user data from Cognito
        if (userData.email) {
          basicInfoForm.setValue('email', userData.email);
        }
        if (userData.firstName) {
          basicInfoForm.setValue('firstName', userData.firstName);
        }
        if (userData.lastName) {
          basicInfoForm.setValue('lastName', userData.lastName);
        }
        if (userData.phoneNumber) {
          // Extract country code and phone number
          const phoneMatch = userData.phoneNumber.match(/^(\+\d{1,3})(.+)$/);
          if (phoneMatch) {
            basicInfoForm.setValue('phoneCountryCode', phoneMatch[1]);
            basicInfoForm.setValue('phone', phoneMatch[2]);
          }
        }

        // Store the token for later use in registration
        setActivationCode(token);
        setInvitationData({
          ...userData,
          isEmployeeInvitation: true
        });

        toast({
          title: "Employee invitation validated",
          description: `Welcome ${userData.firstName || 'to AperionHealth'}! Please complete your registration.`,
        });
      } else {
        toast({
          title: "Invalid invitation",
          description: result.message || "The invitation link is invalid or has expired.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error validating employee invitation token:', error);
      toast({
        title: "Validation error",
        description: "Unable to validate invitation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Form instances for each step
  const basicInfoForm = useForm<BasicInfoFormData>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneCountryCode: '+1',
      phone: '',
      dateOfBirth: '',
      gender: undefined,
    },
  });

  const accountSetupForm = useForm<AccountSetupFormData>({
    resolver: zodResolver(accountSetupSchema),
    defaultValues: {
      streetAddress: '',
      apartmentUnit: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'United States',
    },
  });

  const privacyForm = useForm<PrivacyFormData>({
    resolver: zodResolver(privacySchema),
    defaultValues: {
      dataSharing: true,
      marketing: false,
      analytics: true,
      termsAccepted: false,
      privacyAccepted: false,
    },
  });

  const handleBasicInfoSubmit = async (data: BasicInfoFormData) => {
    setIsLoading(true);
    try {
      // TODO: Validate basic info with backend
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Store data as-is, don't combine phone number yet
      const processedData = {
        ...data,
        phone: data.phone && data.phone.trim() ? data.phone.replace(/\s/g, '') : ''
      };

      setRegistrationData(prev => ({ ...prev, basicInfo: processedData }));
      setCurrentStep('account-setup');

      toast({
        title: "Information saved",
        description: "Please complete your account setup.",
      });
    } catch (error) {
      toast({
        title: "Validation failed",
        description: "Please check your information and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAccountSetupSubmit = async (data: AccountSetupFormData) => {
    setIsLoading(true);
    try {
      // Combine all registration data
      const completeRegistrationData: any = {
        // Basic info from previous step (now includes dateOfBirth and gender)
        firstName: registrationData.basicInfo?.firstName || '',
        lastName: registrationData.basicInfo?.lastName || '',
        email: registrationData.basicInfo?.email || '',
        phoneCountryCode: registrationData.basicInfo?.phoneCountryCode || '+1',
        // Send the phone number as stored (without country code)
        phone: registrationData.basicInfo?.phone || '',
        dateOfBirth: registrationData.basicInfo?.dateOfBirth || '',
        gender: registrationData.basicInfo?.gender || '',

        // Account setup data from current step (address only)
        streetAddress: data.streetAddress,
        apartmentUnit: data.apartmentUnit,
        city: data.city,
        state: data.state,
        zipCode: data.zipCode,
        country: data.country,

        // System fields
        employerId: invitationData?.employerId || '1', // Use employer from invitation or default
      };

      // Only include activation code for non-employee invitations
      if (!invitationData?.isEmployeeInvitation && activationCode) {
        completeRegistrationData.activationCode = activationCode;
      }

      // Debug: Log the data being sent
      console.log('Registration data being sent:', {
        ...completeRegistrationData,
        isEmployeeInvitation: invitationData?.isEmployeeInvitation,
        hasActivationCode: !!completeRegistrationData.activationCode
      });

      // Submit registration to backend via API gateway
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(completeRegistrationData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || 'Registration failed');
      }

      // Store the complete registration data
      setRegistrationData(prev => ({
        ...prev,
        accountSetup: data,
        registrationResult: result.data
      }));

      setCurrentStep('privacy');

      toast({
        title: "Account created successfully!",
        description: "Your account has been created. Please review your privacy preferences.",
      });
    } catch (error) {
      console.error('Registration error:', error);
      toast({
        title: "Account creation failed",
        description: error instanceof Error ? error.message : "Please try again or contact support.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrivacySubmit = async (data: PrivacyFormData) => {
    setIsLoading(true);
    try {
      // TODO: Complete registration with backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setRegistrationData(prev => ({ ...prev, privacy: data }));
      setCurrentStep('success');
      
      toast({
        title: "Registration complete!",
        description: "Welcome to Aperion Health. You can now sign in.",
      });
    } catch (error) {
      toast({
        title: "Registration failed",
        description: "Please try again or contact support.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const goBack = () => {
    if (currentStep === 'account-setup') {
      setCurrentStep('basic-info');
    } else if (currentStep === 'privacy') {
      setCurrentStep('account-setup');
    }
  };

  const getStepNumber = () => {
    switch (currentStep) {
      case 'basic-info': return 1;
      case 'account-setup': return 2;
      case 'privacy': return 3;
      case 'success': return 3; // Keep at 3 since success is completion, not a new step
      default: return 1;
    }
  };

  const renderBasicInfoStep = () => (
    <Card className="shadow-2xl border-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-md">
      <CardHeader className="space-y-1 text-center pb-6">
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg"
        >
          <UserPlus className="w-8 h-8" />
        </motion.div>
        <CardTitle className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-400 dark:to-indigo-500">
          Create Your Account
        </CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-300">
          Enter your basic information and personal details
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <form onSubmit={basicInfoForm.handleSubmit(handleBasicInfoSubmit)} className="space-y-6">
          <div className="space-y-4">

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium">
                  First Name *
                </Label>
                <Input
                  id="firstName"
                  type="text"
                  placeholder="John"
                  {...basicInfoForm.register('firstName')}
                  className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                    basicInfoForm.formState.errors.firstName
                      ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                      : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                  }`}
                />
                {basicInfoForm.formState.errors.firstName && (
                  <p className="text-sm text-red-600">
                    {basicInfoForm.formState.errors.firstName.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium">
                  Last Name *
                </Label>
                <Input
                  id="lastName"
                  type="text"
                  placeholder="Doe"
                  {...basicInfoForm.register('lastName')}
                  className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                    basicInfoForm.formState.errors.lastName
                      ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                      : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                  }`}
                />
                {basicInfoForm.formState.errors.lastName && (
                  <p className="text-sm text-red-600">
                    {basicInfoForm.formState.errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                Email Address *
              </Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...basicInfoForm.register('email')}
                  className={`h-12 text-base pr-12 transition-all duration-300 border focus:outline-none ${
                    basicInfoForm.formState.errors.email
                      ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                      : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                  }`}
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Mail className="w-4 h-4 text-blue-500" />
                </div>
              </div>
              {basicInfoForm.formState.errors.email && (
                <p className="text-sm text-red-600">
                  {basicInfoForm.formState.errors.email.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium">
                Phone Number (Optional)
              </Label>
              <div className="grid grid-cols-3 gap-2">
                {/* Country Code Dropdown */}
                <div className="col-span-1">
                  <Select
                    onValueChange={(value) => basicInfoForm.setValue('phoneCountryCode', value)}
                    defaultValue="+1"
                  >
                    <SelectTrigger className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                      basicInfoForm.formState.errors.phoneCountryCode
                        ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                        : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                    }`}>
                      <SelectValue placeholder="+1" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="+1">🇺🇸 +1</SelectItem>
                      <SelectItem value="+91">🇮🇳 +91</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Phone Number Input */}
                <div className="col-span-2 relative">
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="(*************"
                    {...basicInfoForm.register('phone')}
                    className={`h-12 text-base pr-12 transition-all duration-300 border focus:outline-none ${
                      basicInfoForm.formState.errors.phone
                        ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                        : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                    }`}
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Phone className="w-4 h-4 text-blue-500" />
                  </div>
                </div>
              </div>
              {(basicInfoForm.formState.errors.phone || basicInfoForm.formState.errors.phoneCountryCode) && (
                <p className="text-sm text-red-600">
                  {basicInfoForm.formState.errors.phone?.message || basicInfoForm.formState.errors.phoneCountryCode?.message}
                </p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dateOfBirth" className="text-sm font-medium">
                  Date of Birth *
                </Label>
                <div className="relative">
                  <Input
                    id="dateOfBirth"
                    type="date"
                    {...basicInfoForm.register('dateOfBirth')}
                    className={`h-12 text-base pr-12 transition-all duration-300 border focus:outline-none [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden ${
                      basicInfoForm.formState.errors.dateOfBirth
                        ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                        : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                    }`}
                    style={{
                      colorScheme: 'light',
                    }}
                  />
                  <div
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-auto cursor-pointer"
                    onClick={(e) => {
                      e.preventDefault();
                      const input = e.currentTarget.parentElement?.querySelector('input[type="date"]') as HTMLInputElement;
                      input?.showPicker?.();
                    }}
                  >
                    <Calendar className="w-4 h-4 text-blue-500" />
                  </div>
                </div>
                {basicInfoForm.formState.errors.dateOfBirth && (
                  <p className="text-sm text-red-600">
                    {basicInfoForm.formState.errors.dateOfBirth.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender" className="text-sm font-medium">
                  Gender *
                </Label>
                <Select onValueChange={(value) => basicInfoForm.setValue('gender', value as any)}>
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue placeholder="Select your gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                    <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
                  </SelectContent>
                </Select>
                {basicInfoForm.formState.errors.gender && (
                  <p className="text-sm text-red-600">
                    {basicInfoForm.formState.errors.gender.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          <Button
            type="submit"
            className="w-full h-12 text-base bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg transition-all duration-300 disabled:opacity-50"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Saving...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                Continue to Address
                <ArrowRight className="w-4 h-4" />
              </div>
            )}
          </Button>
        </form>

        <div className="pt-4 border-t border-slate-100 dark:border-slate-800 text-center">
          <p className="text-sm text-slate-600 dark:text-slate-400">
            Need help?{' '}
            <button
              onClick={() => navigate('/auth/step1')}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Contact support
            </button>
          </p>
        </div>
      </CardContent>
    </Card>
  );

  const renderAccountSetupStep = () => (
    <Card className="shadow-2xl border-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-md">
      <CardHeader className="space-y-1 text-center pb-6">
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg"
        >
          <Lock className="w-8 h-8" />
        </motion.div>
        <CardTitle className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-400 dark:to-indigo-500">
          Address Information
        </CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-300">
          Complete your address information
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <form onSubmit={accountSetupForm.handleSubmit(handleAccountSetupSubmit)} className="space-y-6">
          <div className="space-y-4">
            {/* Address Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
                Address Information
              </h3>

              <div className="space-y-2">
                <Label htmlFor="streetAddress" className="text-sm font-medium">
                  Street Address *
                </Label>
                <Input
                  id="streetAddress"
                  type="text"
                  placeholder="123 Main Street"
                  {...accountSetupForm.register('streetAddress')}
                  className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                    accountSetupForm.formState.errors.streetAddress
                      ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                      : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                  }`}
                />
                {accountSetupForm.formState.errors.streetAddress && (
                  <p className="text-sm text-red-600">
                    {accountSetupForm.formState.errors.streetAddress.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="apartmentUnit" className="text-sm font-medium">
                  Apartment/Unit *
                </Label>
                <Input
                  id="apartmentUnit"
                  type="text"
                  placeholder="Apt 4B, Unit 205, Suite 100, etc."
                  {...accountSetupForm.register('apartmentUnit')}
                  className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                    accountSetupForm.formState.errors.apartmentUnit
                      ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                      : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                  }`}
                />
                {accountSetupForm.formState.errors.apartmentUnit && (
                  <p className="text-sm text-red-600">
                    {accountSetupForm.formState.errors.apartmentUnit.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city" className="text-sm font-medium">
                    City *
                  </Label>
                  <Input
                    id="city"
                    type="text"
                    placeholder="San Francisco"
                    {...accountSetupForm.register('city')}
                    className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                      accountSetupForm.formState.errors.city
                        ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                        : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                    }`}
                  />
                  {accountSetupForm.formState.errors.city && (
                    <p className="text-sm text-red-600">
                      {accountSetupForm.formState.errors.city.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state" className="text-sm font-medium">
                    State/Province *
                  </Label>
                  <Input
                    id="state"
                    type="text"
                    placeholder="California"
                    {...accountSetupForm.register('state')}
                    className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                      accountSetupForm.formState.errors.state
                        ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                        : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                    }`}
                  />
                  {accountSetupForm.formState.errors.state && (
                    <p className="text-sm text-red-600">
                      {accountSetupForm.formState.errors.state.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="zipCode" className="text-sm font-medium">
                    ZIP/Postal Code *
                  </Label>
                  <Input
                    id="zipCode"
                    type="text"
                    placeholder="94102"
                    {...accountSetupForm.register('zipCode')}
                    className={`h-12 text-base transition-all duration-300 border focus:outline-none ${
                      accountSetupForm.formState.errors.zipCode
                        ? 'border-red-500 focus:ring-2 focus:ring-red-500/20'
                        : 'border-gray-300 focus:ring-2 focus:ring-blue-500/20'
                    }`}
                  />
                  {accountSetupForm.formState.errors.zipCode && (
                    <p className="text-sm text-red-600">
                      {accountSetupForm.formState.errors.zipCode.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country" className="text-sm font-medium">
                    Country *
                  </Label>
                  <Select
                    onValueChange={(value) => accountSetupForm.setValue('country', value)}
                    defaultValue="United States"
                  >
                    <SelectTrigger className="h-12 text-base">
                      <SelectValue placeholder="Select your country" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="United States">United States</SelectItem>
                      <SelectItem value="India">India</SelectItem>
                    </SelectContent>
                  </Select>
                  {accountSetupForm.formState.errors.country && (
                    <p className="text-sm text-red-600">
                      {accountSetupForm.formState.errors.country.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={goBack}
              className="flex-1 h-12 text-base"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <Button
              type="submit"
              className="flex-1 h-12 text-base bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg transition-all duration-300 disabled:opacity-50"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Creating...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  Continue
                  <ArrowRight className="w-4 h-4" />
                </div>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );

  const renderPrivacyStep = () => (
    <Card className="shadow-2xl border-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-md">
      <CardHeader className="space-y-1 text-center pb-6">
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg"
        >
          <Shield className="w-8 h-8" />
        </motion.div>
        <CardTitle className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-400 dark:to-indigo-500">
          Privacy & Terms
        </CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-300">
          Review your privacy preferences and accept our terms
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <form onSubmit={privacyForm.handleSubmit(handlePrivacySubmit)} className="space-y-6">
          <div className="space-y-4">
            <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-4 space-y-4">
              <h3 className="font-semibold text-slate-900 dark:text-slate-100">Privacy Preferences</h3>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Data Sharing for Health Insights
                    </label>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Allow anonymized data to improve health recommendations
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    {...privacyForm.register('dataSharing')}
                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Marketing Communications
                    </label>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Receive wellness tips and program updates
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    {...privacyForm.register('marketing')}
                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Analytics & Performance
                    </label>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Help us improve the platform experience
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    {...privacyForm.register('analytics')}
                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  {...privacyForm.register('termsAccepted')}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500 mt-1"
                />
                <div className="flex-1">
                  <label className="text-sm text-slate-700 dark:text-slate-300">
                    I agree to the{' '}
                    <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">
                      User Agreement
                    </a>
                  </label>
                </div>
              </div>
              {privacyForm.formState.errors.termsAccepted && (
                <p className="text-sm text-red-600 ml-7">
                  {privacyForm.formState.errors.termsAccepted.message}
                </p>
              )}

              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  {...privacyForm.register('privacyAccepted')}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500 mt-1"
                />
                <div className="flex-1">
                  <label className="text-sm text-slate-700 dark:text-slate-300">
                    I acknowledge that I have read and understand the{' '}
                    <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">
                      Privacy Policy
                    </a>{' '}
                    and{' '}
                    <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">
                      HIPAA Notice
                    </a>
                  </label>
                </div>
              </div>
              {privacyForm.formState.errors.privacyAccepted && (
                <p className="text-sm text-red-600 ml-7">
                  {privacyForm.formState.errors.privacyAccepted.message}
                </p>
              )}
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={goBack}
              className="flex-1 h-12 text-base"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <Button
              type="submit"
              className="flex-1 h-12 text-base bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg transition-all duration-300 disabled:opacity-50"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Completing...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  Complete Registration
                  <CheckCircle className="w-4 h-4" />
                </div>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );

  const renderSuccessStep = () => (
    <Card className="shadow-2xl border-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-md">
      <CardContent className="text-center py-12">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
          className="w-20 h-20 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center text-white mx-auto mb-6 shadow-lg"
        >
          <CheckCircle className="w-10 h-10" />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-700 mb-4">
            Welcome to Aperion Health!
          </h2>
          <p className="text-slate-600 dark:text-slate-300 mb-8 text-lg">
            Your account has been successfully created. You can now access your personalized health dashboard.
          </p>

          <div className="space-y-4">
            <Button
              onClick={() => navigate('/auth/step1')}
              className="w-full h-12 text-base bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg transition-all duration-300"
            >
              Sign In to Your Account
            </Button>

            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="w-full h-12 text-base"
            >
              Return to Home
            </Button>
          </div>
        </motion.div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="min-h-screen flex flex-col lg:flex-row">
        {/* Left Side - Project Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 relative overflow-hidden"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-32 h-32 bg-white rounded-full blur-xl"></div>
            <div className="absolute bottom-32 right-20 w-48 h-48 bg-white rounded-full blur-xl"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white rounded-full blur-lg"></div>
          </div>

          <div className="relative z-10 flex flex-col justify-center px-12 xl:px-20 text-white">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
            >
              <div className="flex items-center mb-8">
                <div className="w-12 h-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center mr-4">
                  <Heart className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-3xl font-bold">Aperion Health</h1>
              </div>

              <h2 className="text-4xl xl:text-5xl font-bold mb-6 leading-tight">
                Join Our Health
                <span className="block text-blue-200">Community</span>
              </h2>

              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                Create your account and start your personalized wellness journey with
                expert guidance and comprehensive health management.
              </p>

              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Shield className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Secure Registration</h3>
                    <p className="text-blue-200">
                      Your data is protected with enterprise-grade security
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Expert Support</h3>
                    <p className="text-blue-200">
                      Access to certified wellness coaches and health professionals
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.0, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Activity className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Personalized Care</h3>
                    <p className="text-blue-200">
                      Tailored wellness plans based on your health goals
                    </p>
                  </div>
                </motion.div>
              </div>

              {/* Progress Indicator */}
              <div className="mt-12 pt-8 border-t border-white/20">
                <div className="flex items-center justify-between text-sm text-blue-200 mb-2">
                  <span>Registration Progress</span>
                  <span>{getStepNumber()}/3</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div
                    className="bg-white rounded-full h-2 transition-all duration-500"
                    style={{ width: `${(getStepNumber() / 3) * 100}%` }}
                  ></div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Right Side - Registration Forms */}
        <div className="flex-1 lg:w-1/2 xl:w-2/5 flex flex-col">
          {/* Mobile Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:hidden bg-gradient-to-r from-blue-600 to-indigo-700 p-6 text-white"
          >
            <div className="flex items-center justify-center">
              <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center mr-3">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold">Aperion Health</h1>
            </div>
            <p className="text-center text-blue-100 mt-2 text-sm">
              Create Your Health Account
            </p>

            {/* Mobile Progress */}
            <div className="mt-4">
              <div className="flex items-center justify-between text-xs text-blue-200 mb-2">
                <span>Step {getStepNumber()} of 3</span>
                <span>{Math.round((getStepNumber() / 3) * 100)}%</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-1">
                <div
                  className="bg-white rounded-full h-1 transition-all duration-500"
                  style={{ width: `${(getStepNumber() / 3) * 100}%` }}
                ></div>
              </div>
            </div>
          </motion.div>

          <div className="flex-1 flex items-center justify-center p-4 sm:p-6 lg:p-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="w-full max-w-md"
            >
              {/* Step-specific content */}
              {currentStep === 'basic-info' && renderBasicInfoStep()}
              {currentStep === 'account-setup' && renderAccountSetupStep()}
              {currentStep === 'privacy' && renderPrivacyStep()}
              {currentStep === 'success' && renderSuccessStep()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
