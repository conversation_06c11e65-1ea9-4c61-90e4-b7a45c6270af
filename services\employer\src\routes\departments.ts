import { Router } from 'express';
// import { EmployeeController } from '../controllers/EmployeeController';

const router = Router();
// const employeeController = new EmployeeController();

/**
 * @route GET /departments (via API Gateway: GET /api/employer/departments)
 * @desc Get departments for the authenticated employer
 * @access Employer only
 * TEMPORARILY DISABLED - Using hardcoded departments in frontend
 */
// router.get('/', employeeController.getDepartments);

export { router as departmentRouter };
