/**
 * Database Configuration for Aperion Health
 * Single Database with Schema-based Architecture
 */

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  schema: string;
  connectionLimit?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

export interface ServiceDatabaseConfig {
  member: DatabaseConfig;
  employer: DatabaseConfig;
  wellness: DatabaseConfig;
  lms: DatabaseConfig;
  commandCenter: DatabaseConfig;
  shared: DatabaseConfig;
}

// Base database configuration
const baseConfig = {
  host: process.env.DB_HOST || '**************',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'AperionHealth-Dev',
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'Cloud@2025',
  ssl: process.env.DB_SSL === 'true',
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10'),
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
};

// Service-specific database configurations with different schemas
export const databaseConfig: ServiceDatabaseConfig = {
  member: {
    ...baseConfig,
    schema: 'member_service',
  },
  employer: {
    ...baseConfig,
    schema: 'employer_service',
  },
  wellness: {
    ...baseConfig,
    schema: 'wellness_service',
  },
  lms: {
    ...baseConfig,
    schema: 'lms_service',
  },
  commandCenter: {
    ...baseConfig,
    schema: 'command_center',
  },
  shared: {
    ...baseConfig,
    schema: 'shared_data',
  },
};

// Connection string builder
export function buildConnectionString(config: DatabaseConfig): string {
  const { host, port, database, username, password, ssl } = config;
  const sslParam = ssl ? '?sslmode=require' : '';
  return `postgresql://${username}:${password}@${host}:${port}/${database}${sslParam}`;
}

// Get configuration for specific service
export function getServiceDatabaseConfig(service: keyof ServiceDatabaseConfig): DatabaseConfig {
  return databaseConfig[service];
}

// Database connection options for different ORMs/clients
export const connectionOptions = {
  // For pg (node-postgres)
  pg: (config: DatabaseConfig) => ({
    host: config.host,
    port: config.port,
    database: config.database,
    user: config.username,
    password: config.password,
    ssl: config.ssl,
    max: config.connectionLimit,
    idleTimeoutMillis: config.idleTimeoutMillis,
    connectionTimeoutMillis: config.connectionTimeoutMillis,
    options: `-c search_path=${config.schema},shared_data,public`,
  }),

  // For TypeORM
  typeorm: (config: DatabaseConfig) => ({
    type: 'postgres' as const,
    host: config.host,
    port: config.port,
    database: config.database,
    username: config.username,
    password: config.password,
    ssl: config.ssl,
    schema: config.schema,
    synchronize: false, // Use migrations instead
    logging: process.env.NODE_ENV === 'development',
    entities: ['src/entities/**/*.ts'],
    migrations: ['src/migrations/**/*.ts'],
    extra: {
      max: config.connectionLimit,
      idleTimeoutMillis: config.idleTimeoutMillis,
      connectionTimeoutMillis: config.connectionTimeoutMillis,
    },
  }),

  // For Prisma
  prisma: (config: DatabaseConfig) => ({
    url: `${buildConnectionString(config)}&schema=${config.schema}`,
  }),

  // For Sequelize
  sequelize: (config: DatabaseConfig) => ({
    dialect: 'postgres' as const,
    host: config.host,
    port: config.port,
    database: config.database,
    username: config.username,
    password: config.password,
    schema: config.schema,
    ssl: config.ssl,
    pool: {
      max: config.connectionLimit,
      min: 0,
      acquire: config.connectionTimeoutMillis,
      idle: config.idleTimeoutMillis,
    },
    define: {
      schema: config.schema,
    },
  }),
};

// Health check query
export const healthCheckQuery = 'SELECT 1 as health_check';

// Schema validation queries
export const schemaQueries = {
  checkSchema: (schema: string) => `
    SELECT schema_name 
    FROM information_schema.schemata 
    WHERE schema_name = '${schema}'
  `,
  
  listTables: (schema: string) => `
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = '${schema}'
    ORDER BY table_name
  `,
  
  checkTable: (schema: string, table: string) => `
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = '${schema}' AND table_name = '${table}'
  `,
};

export default databaseConfig;
