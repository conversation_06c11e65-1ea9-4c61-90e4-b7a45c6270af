import { z } from 'zod';

// User company assignment interface
export interface UserCompanyAssignment {
  id: string;
  userId: number;
  companyId: string;
  roleInCompany?: string;
  assignedAt: Date;
  assignedBy?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // Joined data
  company?: {
    id: string;
    name: string;
    industry?: string;
    status: string;
  };
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    role: string;
  };
  assignedByUser?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
}

// Create user company assignment schema
export const createUserCompanyAssignmentSchema = z.object({
  userId: z.number().min(1, 'User ID is required'),
  companyId: z.string().uuid('Invalid company ID'),
  roleInCompany: z.string().max(100, 'Role name too long').optional(),
  assignedBy: z.number().min(1).optional(),
});

// Update user company assignment schema
export const updateUserCompanyAssignmentSchema = z.object({
  roleInCompany: z.string().max(100, 'Role name too long').optional(),
  isActive: z.boolean().optional(),
});

// User company assignment query parameters schema
export const userCompanyAssignmentQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('10'),
  userId: z.string().transform(Number).pipe(z.number().min(1)).optional(),
  companyId: z.string().uuid().optional(),
  roleInCompany: z.string().optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  assignedBy: z.string().transform(Number).pipe(z.number().min(1)).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  sortBy: z.enum(['assignedAt', 'createdAt', 'updatedAt']).default('assignedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Type definitions from schemas
export type CreateUserCompanyAssignmentRequest = z.infer<typeof createUserCompanyAssignmentSchema>;
export type UpdateUserCompanyAssignmentRequest = z.infer<typeof updateUserCompanyAssignmentSchema>;
export type UserCompanyAssignmentQueryParams = z.infer<typeof userCompanyAssignmentQuerySchema>;

// Assignment statistics interface
export interface AssignmentStatistics {
  totalAssignments: number;
  activeAssignments: number;
  inactiveAssignments: number;
  assignmentsByCompany: Array<{
    companyId: string;
    companyName: string;
    userCount: number;
  }>;
  assignmentsByRole: Array<{
    role: string;
    count: number;
  }>;
  recentAssignments: Array<{
    id: string;
    userId: number;
    userName: string;
    companyName: string;
    roleInCompany?: string;
    assignedAt: Date;
  }>;
}

// Bulk assignment operations
export interface BulkAssignmentOperation {
  userIds: number[];
  companyId: string;
  operation: 'assign' | 'unassign' | 'update_role';
  roleInCompany?: string;
  assignedBy?: number;
}

// Assignment history interface for tracking changes
export interface AssignmentHistory {
  id: string;
  assignmentId: string;
  action: 'created' | 'updated' | 'activated' | 'deactivated';
  previousData?: any; // JSONB field
  newData?: any; // JSONB field
  changedBy: number;
  changedAt: Date;
}
