import { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import {
  LayoutDashboard,
  Users,
  Menu,
  X,
  LogOut,
  HelpCircle,
  Building2,
  LineChart,
  Home,
  Shield
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';

export function EmployerLayout() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { toast } = useToast();
  const [sidebarOpen, setSidebarOpen] = useState(true); // Default to true for desktop
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false); // Separate state for mobile
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], { weekday: 'short', month: 'short', day: 'numeric' });
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/employer/dashboard',
      icon: LayoutDashboard,
      description: 'Overview of employee wellness program performance'
    },
    {
      name: 'Employee Management',
      href: '/employer/employees',
      icon: Users,
      description: 'Manage employees and their wellness plans'
    },
    {
      name: 'Insurance Configuration',
      href: '/employer/onboard',
      icon: Shield,
      description: 'Configure insurance plans and wellness integrations'
    }
  ];

  // const secondaryNavigation = [
  //   {
  //     name: 'Company Settings',
  //     href: '/employer/settings',
  //     icon: Building2,
  //     description: 'Manage company information and preferences'
  //   },
  //   {
  //     name: 'Reports & Analytics',
  //     href: '/employer/reports',
  //     icon: LineChart,
  //     description: 'View detailed wellness program analytics and ROI'
  //   }
  // ];

  const getPageTitle = (path: string): string => {
    if (path === '/employer' || path === '/employer/dashboard') return 'Dashboard';
    if (path === '/employer/employees') return 'Employee Management';
    if (path === '/employer/onboard') return 'Insurance Configuration';
    if (path === '/employer/reports') return 'Reports & Analytics';
    if (path === '/employer/settings') return 'Company Settings';
    return 'Employer Portal';
  };

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleLogout = () => {
    toast({
      title: "Logout Successful",
      description: "You have been successfully logged out.",
    });
    logout();
  };

  return (
    <div className="flex h-screen bg-slate-50 dark:bg-slate-900">
      {/* Sidebar for desktop */}
      <motion.aside
        initial={false}
        animate={{
          width: sidebarOpen ? 280 : 72
        }}
        transition={{ duration: 0.2, ease: "easeInOut" }}
        className="hidden md:flex md:flex-col md:fixed md:inset-y-0 border-r border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-900 shadow-sm"
      >
        {/* Sidebar header with logo */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-slate-200 dark:border-slate-800 bg-gradient-to-r from-purple-600 to-indigo-600">
          <AnimatePresence mode="wait">
            {sidebarOpen && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex items-center space-x-3"
              >
                <div className="w-8 h-8 rounded-md bg-white flex items-center justify-center">
                  <Building2 className="h-5 w-5 text-indigo-600" />
                </div>
                <span className="text-white text-lg font-semibold">Horizon Employer</span>
              </motion.div>
            )}
          </AnimatePresence>

          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="h-8 w-8 p-0 text-white hover:text-slate-200 hover:bg-white/10"
          >
            {sidebarOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
          </Button>
        </div>

        {/* Admin profile summary */}
        <div className="flex-grow flex flex-col overflow-y-auto">
          <div className="flex items-center p-4 border-b border-slate-200 dark:border-slate-800">
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage src="/admin-avatar.png" alt="Admin" />
              <AvatarFallback className="bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300">AC</AvatarFallback>
            </Avatar>
            <AnimatePresence mode="wait">
              {sidebarOpen && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.15 }}
                  className="flex-1 min-w-0"
                >
                  <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">Admin Console</p>
                  <p className="text-xs text-slate-500 dark:text-slate-400 truncate">ACME Corporation</p>
                </motion.div>
              )}
            </AnimatePresence>
            {sidebarOpen && (
              <Badge variant="secondary" className="ml-2 px-2 py-1 text-xs">
                Enterprise
              </Badge>
            )}
          </div>

          {/* Date and time indicator */}
          {sidebarOpen && (
            <div className="flex items-center justify-between px-6 py-3 border-b border-slate-200 dark:border-slate-800 text-sm text-slate-500 dark:text-slate-400">
              <div>{formatDate(currentTime)}</div>
              <div>{formatTime(currentTime)}</div>
            </div>
          )}

          {/* Main navigation */}
          <div>
            <div className="px-4 py-4">
              {sidebarOpen && (
                <h3 className="px-3 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Main
                </h3>
              )}
              <nav className="mt-2 space-y-1">
                <TooltipProvider>
                  {navigation.map((item) => {
                    const isActive = location.pathname === item.href;
                    return (
                      <Tooltip key={item.name}>
                        <TooltipTrigger asChild>
                          <div
                            className={`
                            group relative flex items-center p-4 rounded-lg transition-colors duration-150 cursor-pointer
                            ${isActive
                                ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900/40 dark:text-indigo-200'
                                : 'text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-200'
                              }
                          `}
                            onClick={() => navigate(item.href)}
                          >
                            <div className="flex-shrink-0">
                              <item.icon className={`h-4 w-4 ${isActive ? 'text-indigo-600 dark:text-indigo-400' : ''}`} />
                            </div>

                            <AnimatePresence mode="wait">
                              {sidebarOpen && (
                                <motion.div
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  exit={{ opacity: 0, x: -10 }}
                                  transition={{ duration: 0.15 }}
                                  className="flex-1 ml-3 min-w-0"
                                >
                                  <p className="text-sm font-medium truncate">{item.name}</p>
                                </motion.div>
                              )}
                            </AnimatePresence>

                            {isActive && (
                              <div className="absolute right-0 top-1/2 w-1 h-8 bg-indigo-500 rounded-l-full transform -translate-y-1/2" />
                            )}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          <p>{item.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    );
                  })}
                </TooltipProvider>
              </nav>
            </div>

            {/* Secondary navigation */}
            {/* <div className="px-3 py-4">
              {sidebarOpen && (
                <h3 className="px-3 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Administration
                </h3>
              )}
              <nav className="mt-2 space-y-1">
                <TooltipProvider>
                  {secondaryNavigation.map((item) => {
                    const isActive = location.pathname === item.href;
                    return (
                      <Tooltip key={item.name}>
                        <TooltipTrigger asChild>
                          <div
                            className={`
                            group relative flex items-center p-3 rounded-lg transition-colors duration-150 cursor-pointer
                            ${isActive
                                ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900/40 dark:text-indigo-200'
                                : 'text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-200'
                              }
                          `}
                            onClick={() => navigate(item.href)}
                          >
                            <div className="flex-shrink-0">
                              <item.icon className={`h-5 w-5 ${isActive ? 'text-indigo-600 dark:text-indigo-400' : ''}`} />
                            </div>

                            <AnimatePresence mode="wait">
                              {sidebarOpen && (
                                <motion.div
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  exit={{ opacity: 0, x: -10 }}
                                  transition={{ duration: 0.15 }}
                                  className="flex-1 ml-3 min-w-0"
                                >
                                  <p className="text-sm font-medium truncate">{item.name}</p>
                                </motion.div>
                              )}
                            </AnimatePresence>

                            {isActive && (
                              <div className="absolute right-0 top-1/2 w-1 h-8 bg-indigo-500 rounded-l-full transform -translate-y-1/2" />
                            )}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          <p>{item.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    );
                  })}
                </TooltipProvider>
              </nav>
            </div> */}

            {/* Footer with actions - positioned at bottom */}
            {user && (
              <div className="p-4 border-t border-slate-200 dark:border-slate-800 space-y-1">
                <div
                  className="group relative flex items-center p-3 rounded-lg transition-colors duration-150 cursor-pointer text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-200"
                  onClick={() => navigate('/')}
                >
                  <div className="flex-shrink-0">
                    <Home className="h-4 w-4" />
                  </div>

                  <AnimatePresence mode="wait">
                    {sidebarOpen && (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        transition={{ duration: 0.15 }}
                        className="flex-1 ml-3 min-w-0"
                      >
                        <p className="text-sm font-medium truncate">Back to Home</p>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                <div
                  className="group relative flex items-center p-3 rounded-lg transition-colors duration-150 cursor-pointer text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-200"
                  onClick={handleLogout}
                >
                  <div className="flex-shrink-0">
                    <LogOut className="h-4 w-4" />
                  </div>

                  <AnimatePresence mode="wait">
                    {sidebarOpen && (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        transition={{ duration: 0.15 }}
                        className="flex-1 ml-3 min-w-0"
                      >
                        <p className="text-sm font-medium truncate">Logout</p>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            )}
          </div>
        </div>

      </motion.aside>

      {/* Mobile sidebar */}
      <div className={`md:hidden fixed inset-0 flex z-40 ${mobileSidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-slate-600 dark:bg-slate-900 bg-opacity-75" onClick={() => setMobileSidebarOpen(false)}></div>
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white dark:bg-slate-900 shadow-xl">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <Button
              variant="ghost"
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white text-white"
              onClick={() => setMobileSidebarOpen(false)}
            >
              <span className="sr-only">Close sidebar</span>
              <X className="h-6 w-6" aria-hidden="true" />
            </Button>
          </div>

          {/* Mobile sidebar header with logo */}
          <div className="flex items-center h-16 px-4 border-b border-slate-200 dark:border-slate-800 bg-gradient-to-r from-purple-600 to-indigo-600">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-md bg-white flex items-center justify-center">
                <Building2 className="h-4 w-4 text-indigo-600" />
              </div>
              <span className="text-white text-lg font-semibold">Horizon Employer</span>
            </div>
          </div>

          {/* Admin profile for mobile */}
          <div className="flex items-center p-4 border-b border-slate-200 dark:border-slate-800">
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage src="/admin-avatar.png" alt="Admin" />
              <AvatarFallback className="bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300">AC</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">Admin Console</p>
              <p className="text-xs text-slate-500 dark:text-slate-400 truncate">ACME Corporation</p>
            </div>
          </div>

          {/* Mobile navigation */}
          <div className="flex-1 h-0 overflow-y-auto">
            <div className="px-2 py-4 space-y-6">
              <div>
                <h3 className="px-3 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Main
                </h3>
                <nav className="mt-2 space-y-1">
                  {navigation.map((item) => {
                    const isActive = location.pathname === item.href;
                    return (
                      <Button
                        key={item.name}
                        variant={isActive ? "secondary" : "ghost"}
                        className={`w-full justify-start px-3 py-6 ${isActive
                          ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900/40 dark:text-indigo-200'
                          : 'text-slate-700 hover:bg-slate-100 dark:text-slate-300 dark:hover:bg-slate-800/60'
                          }`}
                        onClick={() => {
                          navigate(item.href);
                          setMobileSidebarOpen(false);
                        }}
                      >
                        <item.icon className={`mr-3 h-4 w-4 ${isActive ? 'text-indigo-600 dark:text-indigo-400' : ''}`} aria-hidden="true" />
                        <span className="text-sm font-medium">{item.name}</span>
                      </Button>
                    );
                  })}
                </nav>
              </div>

              {/* <div>
                <h3 className="px-3 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                  Administration
                </h3>
                <nav className="mt-2 space-y-1">
                  {secondaryNavigation.map((item) => {
                    const isActive = location.pathname === item.href;
                    return (
                      <Button
                        key={item.name}
                        variant="ghost"
                        className={`w-full justify-start px-3 py-6 ${isActive
                          ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900/40 dark:text-indigo-200'
                          : 'text-slate-700 hover:bg-slate-100 dark:text-slate-300 dark:hover:bg-slate-800/60'
                          }`}
                        onClick={() => {
                          navigate(item.href);
                          setMobileSidebarOpen(false);
                        }}
                      >
                        <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-indigo-600 dark:text-indigo-400' : ''}`} aria-hidden="true" />
                        <span className="text-sm font-medium">{item.name}</span>
                      </Button>
                    );
                  })}
                </nav>
              </div> */}
            </div>
          </div>

          {/* Mobile footer with actions */}
          {user && (
            <div className="p-4 border-t border-slate-200 dark:border-slate-800 space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start text-slate-700 dark:text-slate-300 border-slate-300 dark:border-slate-700"
                onClick={() => {
                  navigate('/');
                  setMobileSidebarOpen(false);
                }}
              >
                <Home className="mr-3 h-4 w-4" />
                <span className="text-sm">Back to Home</span>
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start text-slate-700 hover:text-slate-900 dark:text-slate-300 dark:hover:text-white"
                onClick={() => {
                  handleLogout();
                  setMobileSidebarOpen(false);
                }}
              >
                <LogOut className="mr-3 h-4 w-4" />
                <span className="text-sm">Logout</span>
              </Button>
            </div>
          )}
        </div>
        <div className="flex-shrink-0 w-14"></div>
      </div>

      {/* Mobile header */}
      <div className="md:hidden sticky top-0 z-10 flex items-center justify-between bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800 h-16 px-4">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setMobileSidebarOpen(true)}
            className="text-slate-700 dark:text-slate-300"
          >
            <span className="sr-only">Open sidebar</span>
            <Menu className="h-6 w-6" />
          </Button>
        </div>
        <div className="flex items-center">
          <div className="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-md p-1 mr-2">
            <Building2 className="h-4 w-4 text-white" />
          </div>
          <h1 className="text-lg font-semibold text-slate-900 dark:text-white">{getPageTitle(location.pathname)}</h1>
        </div>
        <div className="w-8"></div> {/* Placeholder for symmetry */}
      </div>

      {/* Main content */}
      <motion.div
        initial={false}
        animate={{
          paddingLeft: sidebarOpen ? 280 : 72
        }}
        transition={{ duration: 0.2, ease: "easeInOut" }}
        className="hidden md:flex md:flex-col md:flex-1"
      >
        {/* Desktop header */}
        <div className="sticky top-0 z-10 bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800 h-16 flex items-center justify-between px-6">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-slate-900 dark:text-white">{getPageTitle(location.pathname)}</h1>
            <Badge variant="outline" className="ml-4 px-2 py-1 text-xs bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-800">
              Admin Console
            </Badge>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm font-medium text-slate-700 dark:text-slate-300">
              <Building2 className="h-4 w-4 text-slate-500 dark:text-slate-400" />
              <span>ACME Corporation</span>
            </div>
            <Avatar className="h-8 w-8">
              <AvatarImage src="/admin-avatar.png" alt="Admin" />
              <AvatarFallback className="bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300">AC</AvatarFallback>
            </Avatar>
          </div>
        </div>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-slate-50 dark:bg-slate-900">
          <Outlet />
        </main>
      </motion.div>

      {/* Mobile main content */}
      <div className="md:hidden flex flex-col flex-1">
        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-slate-50 dark:bg-slate-900">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

// export default EmployerLayout
