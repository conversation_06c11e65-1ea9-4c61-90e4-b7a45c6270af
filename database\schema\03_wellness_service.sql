-- =====================================================
-- WELLNESS SERVICE SCHEMA
-- =====================================================

-- Wellness Coaches Table
CREATE TABLE wellness_service.wellness_coaches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cognito_user_id VARCHAR(255) UNIQUE NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  specializations TEXT[] DEFAULT ARRAY[]::TEXT[],
  certifications JSONB DEFAULT '[]',
  bio TEXT,
  profile_picture_url VARCHAR(500),
  availability JSONB DEFAULT '{"monday": [], "tuesday": [], "wednesday": [], "thursday": [], "friday": [], "saturday": [], "sunday": []}',
  max_members INTEGER DEFAULT 50,
  current_members INTEGER DEFAULT 0,
  hourly_rate DECIMAL(8,2),
  languages TEXT[] DEFAULT ARRAY['English'],
  timezone VARCHAR(50) DEFAULT 'UTC',
  experience_years INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0,
  total_ratings INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'on_leave', 'suspended')),
  onboarding_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Member Coach Assignments Table
CREATE TABLE wellness_service.member_coach_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL, -- References member_service.members(id)
  coach_id UUID REFERENCES wellness_service.wellness_coaches(id),
  assigned_at TIMESTAMP DEFAULT NOW(),
  assignment_reason TEXT,
  assignment_type VARCHAR(50) DEFAULT 'general' CHECK (assignment_type IN ('general', 'specialized', 'temporary', 'trial')),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
  notes TEXT,
  ended_at TIMESTAMP,
  end_reason TEXT,
  member_satisfaction_rating INTEGER CHECK (member_satisfaction_rating BETWEEN 1 AND 5),
  coach_notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Coaching Sessions Table
CREATE TABLE wellness_service.coaching_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assignment_id UUID REFERENCES wellness_service.member_coach_assignments(id) ON DELETE CASCADE,
  member_id UUID NOT NULL, -- References member_service.members(id)
  coach_id UUID REFERENCES wellness_service.wellness_coaches(id),
  session_type VARCHAR(50) NOT NULL CHECK (session_type IN ('initial_consultation', 'follow_up', 'goal_review', 'crisis_support', 'group_session')),
  scheduled_at TIMESTAMP NOT NULL,
  duration_minutes INTEGER DEFAULT 30,
  actual_duration_minutes INTEGER,
  location VARCHAR(200),
  meeting_link VARCHAR(500),
  session_notes TEXT,
  member_goals_discussed JSONB DEFAULT '[]',
  action_items JSONB DEFAULT '[]',
  next_session_recommended BOOLEAN DEFAULT true,
  status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show')),
  member_feedback TEXT,
  member_rating INTEGER CHECK (member_rating BETWEEN 1 AND 5),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Wellness Programs Table
CREATE TABLE wellness_service.wellness_programs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  program_name VARCHAR(200) NOT NULL,
  description TEXT,
  program_type VARCHAR(50) NOT NULL CHECK (program_type IN ('weight_management', 'stress_reduction', 'fitness', 'nutrition', 'mental_health', 'chronic_disease')),
  duration_weeks INTEGER NOT NULL,
  max_participants INTEGER DEFAULT 20,
  current_participants INTEGER DEFAULT 0,
  coach_id UUID REFERENCES wellness_service.wellness_coaches(id),
  curriculum JSONB DEFAULT '[]',
  requirements JSONB DEFAULT '[]',
  start_date DATE,
  end_date DATE,
  enrollment_deadline DATE,
  cost DECIMAL(8,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'full', 'completed', 'cancelled')),
  created_by UUID NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Program Enrollments Table
CREATE TABLE wellness_service.program_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  program_id UUID REFERENCES wellness_service.wellness_programs(id) ON DELETE CASCADE,
  member_id UUID NOT NULL, -- References member_service.members(id)
  enrolled_at TIMESTAMP DEFAULT NOW(),
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'enrolled' CHECK (status IN ('enrolled', 'active', 'completed', 'dropped_out', 'paused')),
  completion_certificate_url VARCHAR(500),
  final_assessment_score DECIMAL(5,2),
  member_feedback TEXT,
  member_rating INTEGER CHECK (member_rating BETWEEN 1 AND 5),
  created_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR WELLNESS SERVICE
-- =====================================================

CREATE INDEX idx_coaches_specializations ON wellness_service.wellness_coaches USING GIN(specializations);
CREATE INDEX idx_coaches_status ON wellness_service.wellness_coaches(status);
CREATE INDEX idx_coaches_cognito_id ON wellness_service.wellness_coaches(cognito_user_id);
CREATE INDEX idx_coaches_rating ON wellness_service.wellness_coaches(rating);

CREATE INDEX idx_assignments_member ON wellness_service.member_coach_assignments(member_id);
CREATE INDEX idx_assignments_coach ON wellness_service.member_coach_assignments(coach_id);
CREATE INDEX idx_assignments_status ON wellness_service.member_coach_assignments(status);
CREATE INDEX idx_assignments_assigned_at ON wellness_service.member_coach_assignments(assigned_at);

CREATE INDEX idx_sessions_assignment ON wellness_service.coaching_sessions(assignment_id);
CREATE INDEX idx_sessions_member ON wellness_service.coaching_sessions(member_id);
CREATE INDEX idx_sessions_coach ON wellness_service.coaching_sessions(coach_id);
CREATE INDEX idx_sessions_scheduled ON wellness_service.coaching_sessions(scheduled_at);
CREATE INDEX idx_sessions_status ON wellness_service.coaching_sessions(status);

CREATE INDEX idx_programs_type ON wellness_service.wellness_programs(program_type);
CREATE INDEX idx_programs_coach ON wellness_service.wellness_programs(coach_id);
CREATE INDEX idx_programs_status ON wellness_service.wellness_programs(status);
CREATE INDEX idx_programs_dates ON wellness_service.wellness_programs(start_date, end_date);

CREATE INDEX idx_enrollments_program ON wellness_service.program_enrollments(program_id);
CREATE INDEX idx_enrollments_member ON wellness_service.program_enrollments(member_id);
CREATE INDEX idx_enrollments_status ON wellness_service.program_enrollments(status);

-- =====================================================
-- TRIGGERS FOR WELLNESS SERVICE
-- =====================================================

CREATE TRIGGER update_coaches_updated_at 
  BEFORE UPDATE ON wellness_service.wellness_coaches 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sessions_updated_at 
  BEFORE UPDATE ON wellness_service.coaching_sessions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_programs_updated_at 
  BEFORE UPDATE ON wellness_service.wellness_programs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
