import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Trash2, 
  Ban, 
  CheckCircle, 
  AlertTriangle, 
  User,
  Loader2 
} from 'lucide-react';

export type ConfirmationAction = 'delete' | 'suspend' | 'activate';

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  action: ConfirmationAction;
  userName: string;
  userRole: string;
  onConfirm: () => Promise<void>;
  isLoading?: boolean;
}

const actionConfigs = {
  delete: {
    title: 'Delete User',
    description: 'This action cannot be undone. The user will be permanently removed from the system.',
    icon: Trash2,
    iconColor: 'text-red-500',
    confirmText: 'Delete User',
    confirmVariant: 'destructive' as const,
    warningText: 'This will permanently delete the user account and all associated data.',
  },
  suspend: {
    title: 'Suspend User',
    description: 'The user will lose access to the platform until their account is reactivated.',
    icon: Ban,
    iconColor: 'text-orange-500',
    confirmText: 'Suspend User',
    confirmVariant: 'destructive' as const,
    warningText: 'The user will be immediately logged out and unable to access the platform.',
  },
  activate: {
    title: 'Activate User',
    description: 'The user will regain full access to the platform.',
    icon: CheckCircle,
    iconColor: 'text-green-500',
    confirmText: 'Activate User',
    confirmVariant: 'default' as const,
    warningText: 'The user will be able to log in and access all their permissions.',
  },
};

export function ConfirmationDialog({
  open,
  onOpenChange,
  action,
  userName,
  userRole,
  onConfirm,
  isLoading = false,
}: ConfirmationDialogProps) {
  const config = actionConfigs[action];
  const IconComponent = config.icon;

  const handleConfirm = async () => {
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      console.error(`Error performing ${action} action:`, error);
      // Error handling is done in the parent component
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-[500px]">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center space-x-2">
            <IconComponent className={`w-5 h-5 ${config.iconColor}`} />
            <span>{config.title}</span>
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                {userName.split(' ').map(n => n[0]).join('')}
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-slate-900 dark:text-slate-100">
                    {userName}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {userRole}
                  </Badge>
                </div>
                <User className="w-3 h-3 inline mr-1" />
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  Platform User
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <p className="text-sm text-slate-700 dark:text-slate-300">
                {config.description}
              </p>
              
              <div className="flex items-start space-x-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                <AlertTriangle className="w-4 h-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-amber-800 dark:text-amber-200">
                  {config.warningText}
                </p>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              variant={config.confirmVariant}
              onClick={handleConfirm}
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              {config.confirmText}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Convenience hooks for different actions
export function useConfirmationDialog() {
  const [dialogState, setDialogState] = React.useState<{
    open: boolean;
    action: ConfirmationAction;
    userName: string;
    userRole: string;
    onConfirm: () => Promise<void>;
    isLoading: boolean;
  }>({
    open: false,
    action: 'delete',
    userName: '',
    userRole: '',
    onConfirm: async () => {},
    isLoading: false,
  });

  const showConfirmation = (
    action: ConfirmationAction,
    userName: string,
    userRole: string,
    onConfirm: () => Promise<void>
  ) => {
    setDialogState({
      open: true,
      action,
      userName,
      userRole,
      onConfirm,
      isLoading: false,
    });
  };

  const hideConfirmation = () => {
    setDialogState(prev => ({ ...prev, open: false }));
  };

  const setLoading = (loading: boolean) => {
    setDialogState(prev => ({ ...prev, isLoading: loading }));
  };

  return {
    dialogState,
    showConfirmation,
    hideConfirmation,
    setLoading,
  };
}
