import { Router } from 'express';
import { body, query, validationResult } from 'express-validator';
import {
  registerMember,
  validateRegistrationData,
  checkEmailAvailability,
  createMockActivationCode,
  validateActivationCode
} from '../controllers/registration';
import { async<PERSON>and<PERSON> } from '@aperion/shared';

const router = Router();

// Validation middleware for registration
const registrationValidation = [
  // Basic Info Validation
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('First name can only contain letters, spaces, hyphens, and apostrophes'),
  
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Last name can only contain letters, spaces, hyphens, and apostrophes'),
  
  body('email')
    .trim()
    .isEmail()
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('Please enter a valid email address (max 255 characters)'),
  
  body('phoneCountryCode')
    .trim()
    .matches(/^\+\d{1,4}$/)
    .withMessage('Invalid country code format'),

  body('phone')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .matches(/^[\d\s\-\(\)]*$/)
    .withMessage('Phone number can only contain digits, spaces, dashes, and parentheses'),

  // Personal Information Validation (required fields)
  body('dateOfBirth')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Date of birth is required')
    .custom((value) => {
      if (!value) {
        throw new Error('Date of birth is required');
      }

      // Accept both yyyy-MM-dd and ISO 8601 formats
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;

      if (dateRegex.test(value) || isoRegex.test(value)) {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          throw new Error('Date of birth must be a valid date');
        }
        return true;
      }

      throw new Error('Date of birth must be in yyyy-MM-dd or ISO 8601 format');
    }),

  body('gender')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Gender is required')
    .isIn(['male', 'female', 'other', 'prefer_not_to_say'])
    .withMessage('Invalid gender selection'),

  body('streetAddress')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Street address is required and must be less than 200 characters'),

  body('apartmentUnit')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Apartment unit is required and must be less than 50 characters'),

  body('city')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('City is required and must be less than 100 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('City can only contain letters, spaces, hyphens, and apostrophes'),

  body('state')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('State is required and must be less than 100 characters'),

  body('zipCode')
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('ZIP code is required and must be less than 20 characters')
    .matches(/^[A-Za-z0-9\s-]+$/)
    .withMessage('Invalid ZIP code format'),

  body('country')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Country is required')
    .isIn(['United States', 'India'])
    .withMessage('Country must be either United States or India'),
  
  // System fields
  body('employerId')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Employer ID must be less than 50 characters'),
  
  body('companyReference')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Company reference must be less than 100 characters'),

  // Activation Code Validation (optional)
  body('activationCode')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Activation code must be between 1 and 50 characters'),
];

// Email validation for availability check
const emailValidation = [
  query('email')
    .trim()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
];

// Validation error handler
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        details: errors.array(),
        timestamp: new Date().toISOString(),
        requestId: req.requestId || 'unknown',
        path: req.originalUrl,
        method: req.method,
      },
    });
  }
  next();
};

/**
 * POST /api/member/register
 * Register a new member/employee
 */
router.post(
  '/register',
  registrationValidation,
  handleValidationErrors,
  asyncHandler(registerMember)
);

/**
 * POST /api/member/validate-registration
 * Validate registration data without creating account
 */
router.post(
  '/validate-registration',
  registrationValidation,
  handleValidationErrors,
  asyncHandler(validateRegistrationData)
);

/**
 * GET /api/member/check-email
 * Check if email is available for registration
 */
router.get(
  '/check-email',
  emailValidation,
  handleValidationErrors,
  asyncHandler(checkEmailAvailability)
);

/**
 * POST /api/member/create-mock-activation-code
 * Create a mock activation code for testing purposes
 */
router.post(
  '/create-mock-activation-code',
  [
    body('email')
      .trim()
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address'),
    body('firstName')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('First name must be less than 50 characters'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Last name must be less than 50 characters'),
  ],
  handleValidationErrors,
  asyncHandler(createMockActivationCode)
);

/**
 * GET /api/member/validate-activation-code
 * Validate an activation code
 */
router.get(
  '/validate-activation-code',
  [
    query('activationCode')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Activation code is required and must be less than 50 characters'),
  ],
  handleValidationErrors,
  asyncHandler(validateActivationCode)
);

export { router as registrationRouter };
