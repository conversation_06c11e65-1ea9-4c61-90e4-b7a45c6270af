import { z } from 'zod';

// Company status enum
export type CompanyStatus = 'active' | 'inactive' | 'suspended';

// Company size enum
export type CompanySize = '1-10' | '11-50' | '51-200' | '201-500' | '501-1000' | '1000+';

// Company interface
export interface Company {
  id: string;
  name: string;
  code?: string;
  industry?: string;
  companySize?: CompanySize;
  address?: any; // JSONB field
  contactEmail?: string;
  contactPhone?: string;
  website?: string;
  logoUrl?: string;
  status: CompanyStatus;
  createdAt: Date;
  updatedAt: Date;
}

// Create company request schema
export const createCompanySchema = z.object({
  name: z.string().min(1, 'Company name is required').max(200, 'Company name too long'),
  code: z.string().max(50, 'Company code too long').optional(),
  industry: z.string().max(100, 'Industry name too long').optional(),
  companySize: z.enum(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']).optional(),
  address: z.any().optional(), // JSONB field
  contactEmail: z.string().email('Invalid email format').optional(),
  contactPhone: z.string().max(20, 'Phone number too long').optional(),
  website: z.string().url('Invalid website URL').optional(),
  logoUrl: z.string().url('Invalid logo URL').max(500, 'Logo URL too long').optional(),
  status: z.enum(['active', 'inactive', 'suspended']).default('active'),
});

// Update company request schema
export const updateCompanySchema = createCompanySchema.partial();

// Company query parameters schema
export const companyQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('10'),
  search: z.string().optional(),
  industry: z.string().optional(),
  companySize: z.enum(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']).optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  sortBy: z.enum(['name', 'industry', 'companySize', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Type definitions from schemas
export type CreateCompanyRequest = z.infer<typeof createCompanySchema>;
export type UpdateCompanyRequest = z.infer<typeof updateCompanySchema>;
export type CompanyQueryParams = z.infer<typeof companyQuerySchema>;

// Company statistics interface
export interface CompanyStatistics {
  totalCompanies: number;
  activeCompanies: number;
  inactiveCompanies: number;
  suspendedCompanies: number;
  companiesByIndustry: Array<{
    industry: string;
    count: number;
  }>;
  companiesBySize: Array<{
    size: CompanySize;
    count: number;
  }>;
}
