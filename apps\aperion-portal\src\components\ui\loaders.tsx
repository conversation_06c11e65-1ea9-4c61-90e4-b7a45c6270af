import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingContainerProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingType?: 'spinner' | 'wave' | 'heartbeat' | 'circle';
  text?: string;
  className?: string;
}

export const LoadingContainer: React.FC<LoadingContainerProps> = ({
  isLoading,
  children,
  loadingType = 'spinner',
  text,
  className
}) => {
  if (!isLoading) {
    return <>{children}</>;
  }

  return (
    <div className={cn('flex items-center justify-center p-8', className)}>
      <div className="text-center">
        {loadingType === 'spinner' && <SpinnerLoader />}
        {loadingType === 'wave' && <WaveLoader />}
        {loadingType === 'heartbeat' && <HeartbeatLoader />}
        {loadingType === 'circle' && <CircleLoader />}
        {text && <p className="mt-2 text-sm text-gray-500">{text}</p>}
      </div>
    </div>
  );
};

export const SpinnerLoader: React.FC<{ size?: 'sm' | 'md' | 'lg'; color?: string }> = ({ 
  size = 'md', 
  color = 'hsl(var(--primary))' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div 
      className={cn('animate-spin rounded-full border-2 border-gray-200', sizeClasses[size])}
      style={{ borderTopColor: color }}
    />
  );
};

export const WaveLoader: React.FC<{ color?: string }> = ({ color = 'hsl(var(--primary))' }) => {
  return (
    <div className="flex space-x-1">
      {[0, 1, 2, 3].map((i) => (
        <div
          key={i}
          className="w-2 h-8 rounded-full animate-pulse"
          style={{ 
            backgroundColor: color,
            animationDelay: `${i * 0.1}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );
};

export const HeartbeatLoader: React.FC<{ size?: 'sm' | 'md' | 'lg'; color?: string }> = ({ 
  size = 'md', 
  color = 'hsl(var(--wellness-magenta))' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={cn('animate-pulse', sizeClasses[size])}>
      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
          fill={color}
        />
      </svg>
    </div>
  );
};

export const CircleLoader: React.FC<{ color?: string }> = ({ color = 'hsl(var(--primary))' }) => {
  return (
    <div className="relative w-12 h-12">
      <div 
        className="absolute inset-0 rounded-full border-4 border-gray-200"
      />
      <div 
        className="absolute inset-0 rounded-full border-4 border-transparent animate-spin"
        style={{ borderTopColor: color }}
      />
    </div>
  );
};
