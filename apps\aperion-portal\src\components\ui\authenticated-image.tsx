import React, { useState, useEffect } from 'react'
import apiClient from '@/services/apiClient'

// AuthenticatedImage component for secure image loading
interface AuthenticatedImageProps {
  src: string
  alt: string
  className?: string
  fallback: React.ReactNode
}

export const AuthenticatedImage: React.FC<AuthenticatedImageProps> = ({ 
  src, 
  alt, 
  className, 
  fallback 
}) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    if (!src || src.startsWith('data:') || src.startsWith('blob:')) {
      // Handle preview images or data URLs directly
      setImageSrc(src)
      setIsLoading(false)
      return
    }

    if (!src.startsWith('/api/')) {
      // Handle external URLs directly
      setImageSrc(src)
      setIsLoading(false)
      return
    }

    // For API endpoints, fetch with authentication using apiClient
    const fetchImage = async () => {
      try {
        setIsLoading(true)
        setHasError(false)

        // Check if we have a valid token
        if (!apiClient.hasValidToken()) {
          setHasError(true)
          setIsLoading(false)
          return
        }

        // Use apiClient for authenticated requests
        const response = await apiClient.getInstance().get(src, {
          responseType: 'blob'
        })

        const blob = response.data
        const objectUrl = URL.createObjectURL(blob)
        setImageSrc(objectUrl)
        setIsLoading(false)

        // Cleanup object URL when component unmounts
        return () => URL.revokeObjectURL(objectUrl)
      } catch (error) {
        console.error('Error fetching authenticated image:', error)
        setHasError(true)
        setIsLoading(false)
      }
    }

    fetchImage()
  }, [src])

  if (isLoading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (hasError || !imageSrc) {
    return <>{fallback}</>
  }

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      onError={() => setHasError(true)}
    />
  )
}
