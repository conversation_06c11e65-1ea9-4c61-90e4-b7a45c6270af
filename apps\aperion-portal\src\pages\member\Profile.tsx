import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { useAuth } from '@/contexts/AuthContext'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Edit, Users, Trash2, Save, X, Loader2, Upload, Camera, Calendar } from 'lucide-react'
import { memberProfileService, type ProfileData, type CreateDependentData, type UpdateDependentData, type Dependent } from '@/lib/api'
import { formatDateForInput, formatDateForAPI, formatDateForDisplay } from '@/lib/utils'
import { logger } from '@/utils/logger'
import { useToast } from '@/hooks/use-toast'
import { AuthenticatedImage } from '@/components/ui/authenticated-image'
import DependentForm from '@/components/DependentForm'
import ConfirmationDialog from '@/components/ConfirmationDialog'

// Define form validation schema
const profileFormSchema = z.object({
  firstName: z.string().min(1, { message: "First name is required." }),
  lastName: z.string().min(1, { message: "Last name is required." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  gender: z.enum(['male', 'female', 'other', 'prefer-not-to-say']).optional(),
  street: z.string().min(1, { message: "Street address is required." }),
  city: z.string().min(1, { message: "City is required." }),
  state: z.string().min(1, { message: "State is required." }),
  zipCode: z.string().min(1, { message: "Zip code is required." }),
  country: z.string().min(1, { message: "Country is required." }),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

const MemberProfile = () => {
  const { user } = useAuth()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState('profile')
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [profileData, setProfileData] = useState<ProfileData | null>(null)

  // Photo upload state
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [imageCacheBuster, setImageCacheBuster] = useState(Date.now())
  const [showPhotoMenu, setShowPhotoMenu] = useState(false)
  const [isDeletingPhoto, setIsDeletingPhoto] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const photoMenuRef = useRef<HTMLDivElement>(null)

  // Dependent management state
  const [isDependentFormOpen, setIsDependentFormOpen] = useState(false)
  const [dependentFormMode, setDependentFormMode] = useState<'create' | 'edit'>('create')
  const [editingDependent, setEditingDependent] = useState<Dependent | null>(null)
  const [isDependentLoading, setIsDependentLoading] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [dependentToDelete, setDependentToDelete] = useState<Dependent | null>(null)
  const [isDeletingDependent, setIsDeletingDependent] = useState(false)

  // Initialize form with default values
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      gender: undefined,
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
    },
  })

  // Load profile data on component mount
  useEffect(() => {
    loadProfileData()
  }, [])

  // Close photo menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (photoMenuRef.current && !photoMenuRef.current.contains(event.target as Node)) {
        setShowPhotoMenu(false)
      }
    }

    if (showPhotoMenu) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPhotoMenu])

  const loadProfileData = async () => {
    try {
      setIsLoading(true)
      logger.logUserAction('load_profile', 'MemberProfile')

      const data = await memberProfileService.getProfile()
      setProfileData(data)

      // Update form with loaded data
      const profile = data.profile

      form.reset({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        email: profile.email || '',
        phone: profile.phone || '',
        dateOfBirth: formatDateForInput(profile.dateOfBirth),
        gender: profile.gender as any || undefined,
        street: profile.address?.street || '',
        city: profile.address?.city || '',
        state: profile.address?.state || '',
        zipCode: profile.address?.zipCode || '',
        country: profile.address?.country || '',
      })

      logger.info('Profile data loaded successfully', {
        memberId: profile.id,
        profileCompleteness: data.stats.profileCompleteness,
      })
    } catch (error) {
      logger.error('Failed to load profile data', { error })
      toast({
        title: "Error",
        description: "Failed to load profile data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditToggle = () => {
    if (isEditing) {
      // Cancel editing - reset form to original values
      if (profileData) {
        const profile = profileData.profile

        form.reset({
          firstName: profile.firstName || '',
          lastName: profile.lastName || '',
          email: profile.email || '',
          phone: profile.phone || '',
          dateOfBirth: formatDateForInput(profile.dateOfBirth),
          gender: profile.gender as any || undefined,
          street: profile.address?.street || '',
          city: profile.address?.city || '',
          state: profile.address?.state || '',
          zipCode: profile.address?.zipCode || '',
          country: profile.address?.country || '',
        })
      }
      setIsEditing(false)
      logger.logUserAction('cancel_edit_profile', 'MemberProfile')
    } else {
      setIsEditing(true)
      logger.logUserAction('start_edit_profile', 'MemberProfile')
    }
  }

  async function onSubmit(values: ProfileFormValues) {
    try {
      setIsSaving(true)
      logger.logUserAction('save_profile', 'MemberProfile', { fields: Object.keys(values) })

      // Prepare update data
      const updateData: Partial<MemberProfile> = {
        firstName: values.firstName,
        lastName: values.lastName,
        phone: values.phone,
        dateOfBirth: formatDateForAPI(values.dateOfBirth),
        gender: values.gender,
        address: {
          street: values.street,
          city: values.city,
          state: values.state,
          zipCode: values.zipCode,
          country: values.country,
        },
      }

      // Update profile via API
      const updatedProfile = await memberProfileService.updateProfile(updateData)

      // Update local state
      if (profileData) {
        const updatedProfileData = {
          ...profileData,
          profile: updatedProfile,
        }
        setProfileData(updatedProfileData)

        // Emit event for layout components to update
        window.dispatchEvent(new CustomEvent('profileUpdated', {
          detail: updatedProfileData
        }))
      }

      setIsEditing(false)
      toast({
        title: "Success",
        description: "Profile updated successfully!",
      })

      logger.info('Profile updated successfully', {
        memberId: updatedProfile.id,
        updatedFields: Object.keys(updateData)
      })
    } catch (error) {
      logger.error('Failed to update profile', { error })

      // Extract error message from API response
      let errorMessage = "Failed to update profile. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      console.error('Profile update error:', error);

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Photo upload functions
  const handlePhotoClick = () => {
    if (profileData?.profile.profilePictureUrl) {
      // Show menu for existing photo
      setShowPhotoMenu(!showPhotoMenu)
    } else {
      // Direct upload for no photo
      fileInputRef.current?.click()
    }
  }

  const handleChangePhoto = () => {
    setShowPhotoMenu(false)
    fileInputRef.current?.click()
  }

  const handleRemovePhoto = async () => {
    try {
      setIsDeletingPhoto(true)
      setShowPhotoMenu(false)

      logger.logUserAction('delete_profile_photo', 'MemberProfile')

      // Call delete API using service
      await memberProfileService.deleteProfilePhoto()

      toast({
        title: "Success",
        description: "Profile photo removed successfully!",
      })

      // Update profile data state to remove photo URL
      if (profileData) {
        const updatedProfileData = {
          ...profileData,
          profile: {
            ...profileData.profile,
            profilePictureUrl: null,
          },
        }
        setProfileData(updatedProfileData)

        // Emit event for layout components to update
        window.dispatchEvent(new CustomEvent('profileUpdated', {
          detail: updatedProfileData
        }))
      }

      // Force image refresh
      setImageCacheBuster(Date.now())

    } catch (error) {
      logger.error('Profile photo deletion failed', { error })

      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete photo. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDeletingPhoto(false)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid File Type",
        description: "Please select a JPEG, PNG, or WebP image.",
        variant: "destructive",
      })
      return
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      toast({
        title: "File Too Large",
        description: "Please select an image smaller than 5MB.",
        variant: "destructive",
      })
      return
    }

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreviewImage(e.target?.result as string)
    }
    reader.readAsDataURL(file)

    // Upload the file
    uploadPhoto(file)
  }

  const uploadPhoto = async (file: File) => {
    try {
      setIsUploadingPhoto(true)
      setUploadProgress(0)

      logger.logUserAction('upload_profile_photo', 'MemberProfile', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
      })

      // Upload using API service
      const result = await memberProfileService.uploadProfilePhoto(file)

      logger.info('Profile photo upload result', {
        result,
        hasProfilePictureUrl: !!result.profilePictureUrl,
        profilePictureUrl: result.profilePictureUrl,
      })

      // Update profile data with new photo URL
      if (profileData && result.profilePictureUrl) {
        const updatedProfileData = {
          ...profileData,
          profile: {
            ...profileData.profile,
            profilePictureUrl: result.profilePictureUrl,
          },
        }

        logger.info('Updating profile data with new photo', {
          oldProfilePictureUrl: profileData.profile.profilePictureUrl,
          newProfilePictureUrl: result.profilePictureUrl,
        })

        setProfileData(updatedProfileData)

        // Update cache buster to force image refresh
        setImageCacheBuster(Date.now())

        // Emit event for layout components to update
        window.dispatchEvent(new CustomEvent('profileUpdated', {
          detail: updatedProfileData
        }))
      } else {
        logger.warn('No profile picture URL in upload result or no profile data', {
          hasProfileData: !!profileData,
          hasProfilePictureUrl: !!result.profilePictureUrl,
          result,
        })
      }

      toast({
        title: "Success",
        description: "Profile photo updated successfully!",
      })

      // Clear preview image since upload is complete
      setPreviewImage(null)

    } catch (error) {
      logger.error('Profile photo upload failed', { error })

      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to upload photo. Please try again.",
        variant: "destructive",
      })

      // Clear preview image on error
      setPreviewImage(null)
    } finally {
      setIsUploadingPhoto(false)
      setUploadProgress(0)

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  // Dependent management functions
  const handleAddDependent = () => {
    setDependentFormMode('create')
    setEditingDependent(null)
    setIsDependentFormOpen(true)
    logger.logUserAction('open_add_dependent_form', 'MemberProfile')
  }

  const handleEditDependent = (dependent: Dependent) => {
    setDependentFormMode('edit')
    setEditingDependent(dependent)
    setIsDependentFormOpen(true)
    logger.logUserAction('open_edit_dependent_form', 'MemberProfile', { dependentId: dependent.id })
  }

  const handleDeleteDependent = (dependent: Dependent) => {
    setDependentToDelete(dependent)
    setIsDeleteDialogOpen(true)
    logger.logUserAction('open_delete_dependent_dialog', 'MemberProfile', { dependentId: dependent.id })
  }

  const handleDependentFormSubmit = async (data: CreateDependentData | UpdateDependentData) => {
    try {
      setIsDependentLoading(true)

      if (dependentFormMode === 'create') {
        logger.logUserAction('create_dependent', 'MemberProfile', { dependentData: { ...data, dateOfBirth: '[REDACTED]' } })
        const newDependent = await memberProfileService.createDependent(data as CreateDependentData)

        // Optimistically update the local state instead of full reload
        if (profileData) {
          const updatedProfileData = {
            ...profileData,
            dependents: [...profileData.dependents, newDependent],
            stats: {
              ...profileData.stats,
              totalDependents: profileData.stats.totalDependents + 1
            }
          }
          setProfileData(updatedProfileData)
        }

        toast({
          title: "Success",
          description: "Dependent added successfully!",
        })
      } else if (dependentFormMode === 'edit' && editingDependent) {
        logger.logUserAction('update_dependent', 'MemberProfile', {
          dependentId: editingDependent.id,
          updateFields: Object.keys(data)
        })
        const updatedDependent = await memberProfileService.updateDependent(editingDependent.id, data as UpdateDependentData)

        // Optimistically update the specific dependent in local state
        if (profileData) {
          const updatedDependents = profileData.dependents.map(dep =>
            dep.id === editingDependent.id ? updatedDependent : dep
          )
          const updatedProfileData = {
            ...profileData,
            dependents: updatedDependents
          }
          setProfileData(updatedProfileData)
        }

        toast({
          title: "Success",
          description: "Dependent updated successfully!",
        })
      }

      setIsDependentFormOpen(false)
      setEditingDependent(null)

    } catch (error) {
      logger.error('Dependent operation failed', {
        mode: dependentFormMode,
        dependentId: editingDependent?.id,
        error
      })

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${dependentFormMode} dependent. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsDependentLoading(false)
    }
  }

  const handleConfirmDeleteDependent = async () => {
    if (!dependentToDelete) return

    try {
      setIsDeletingDependent(true)
      logger.logUserAction('delete_dependent', 'MemberProfile', { dependentId: dependentToDelete.id })

      await memberProfileService.deleteDependent(dependentToDelete.id)

      // Optimistically update the local state instead of full reload
      if (profileData) {
        const updatedDependents = profileData.dependents.filter(dep => dep.id !== dependentToDelete.id)
        const updatedProfileData = {
          ...profileData,
          dependents: updatedDependents,
          stats: {
            ...profileData.stats,
            totalDependents: profileData.stats.totalDependents - 1
          }
        }
        setProfileData(updatedProfileData)
      }

      toast({
        title: "Success",
        description: "Dependent removed successfully!",
      })

      setIsDeleteDialogOpen(false)
      setDependentToDelete(null)

    } catch (error) {
      logger.error('Dependent deletion failed', {
        dependentId: dependentToDelete.id,
        error
      })

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove dependent. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDeletingDependent(false)
    }
  }

  if (isLoading) {
    return (
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium">Profile & Dependents</h2>
          <div className="h-10 w-32 bg-neutral-200 rounded animate-pulse"></div>
        </div>

        <Card className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-neutral-200">
            <div className="flex">
              <div className="py-4 px-6 border-b-2 border-primary">
                <div className="h-4 bg-neutral-200 rounded w-20 animate-pulse"></div>
              </div>
              <div className="py-4 px-6">
                <div className="h-4 bg-neutral-200 rounded w-24 animate-pulse"></div>
              </div>
            </div>
          </div>

          <div className="p-5">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Profile Photo Skeleton */}
              <div className="md:col-span-1">
                <div className="flex flex-col items-center p-4">
                  <div className="w-32 h-32 rounded-full bg-neutral-200 animate-pulse"></div>
                  <div className="mt-3 h-4 bg-neutral-200 rounded w-24 animate-pulse"></div>
                  <div className="mt-4 w-full space-y-4">
                    <div className="text-center">
                      <div className="h-5 bg-neutral-200 rounded w-32 mx-auto animate-pulse"></div>
                      <div className="h-4 bg-neutral-200 rounded w-24 mx-auto mt-1 animate-pulse"></div>
                    </div>
                    <div className="border-t border-neutral-200 pt-4">
                      <div className="h-3 bg-neutral-200 rounded w-16 mb-1 animate-pulse"></div>
                      <div className="h-4 bg-neutral-200 rounded w-28 animate-pulse"></div>
                    </div>
                    <div className="border-t border-neutral-200 pt-4">
                      <div className="h-3 bg-neutral-200 rounded w-24 mb-1 animate-pulse"></div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-neutral-200 rounded-full mr-2 animate-pulse"></div>
                        <div className="h-4 bg-neutral-200 rounded w-16 animate-pulse"></div>
                      </div>
                    </div>
                    {/* Profile Completeness Skeleton */}
                    <div className="border-t border-neutral-200 pt-4">
                      <div className="h-3 bg-neutral-200 rounded w-32 mb-1 animate-pulse"></div>
                      <div className="w-full bg-neutral-200 rounded-full h-2 animate-pulse"></div>
                      <div className="h-3 bg-neutral-200 rounded w-20 mt-1 animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Form Fields Skeleton */}
              <div className="md:col-span-2">
                <div className="space-y-6">
                  <div>
                    <div className="h-4 bg-neutral-200 rounded w-40 mb-3 animate-pulse"></div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="space-y-2">
                          <div className="h-3 bg-neutral-200 rounded w-1/4 animate-pulse"></div>
                          <div className="h-10 bg-neutral-200 rounded animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <div className="h-4 bg-neutral-200 rounded w-32 mb-3 animate-pulse"></div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="space-y-2">
                          <div className="h-3 bg-neutral-200 rounded w-1/3 animate-pulse"></div>
                          <div className="h-10 bg-neutral-200 rounded animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <div className="h-10 w-24 bg-neutral-200 rounded animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  if (!profileData) {
    return (
      <div className="mb-8">
        <h2 className="text-lg font-medium mb-4">Profile & Dependents</h2>
        <Card className="bg-white rounded-lg shadow-sm">
          <div className="p-8 text-center">
            <p className="text-neutral-500 mb-4">Failed to load profile data.</p>
            <Button onClick={loadProfileData} className="bg-primary text-white hover:bg-primary/90">
              Try Again
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="mb-8 fade-in">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium">Profile & Dependents</h2>
        <div className="flex items-center fade-in-delay-1">
          {activeTab === 'profile' ? (
            <Button
              onClick={handleEditToggle}
              variant={isEditing ? "outline" : "default"}
              className={isEditing ? "text-neutral-700" : "bg-primary text-white hover:bg-primary/90"}
              disabled={isSaving}
            >
              {isEditing ? (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Profile
                </>
              )}
            </Button>
          ) : (
            <Button
              onClick={handleAddDependent}
              className="bg-primary text-white hover:bg-primary/90"
            >
              <Users className="text-sm mr-1 h-4 w-4" />
              Add Dependent
            </Button>
          )}
        </div>
      </div>

      <Card className="bg-white rounded-lg shadow-sm card-transition fade-in-delay-2">
        <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab}>
          <div className="border-b border-neutral-200">
            <TabsList className="bg-transparent border-b">
              <TabsTrigger
                value="profile"
                className={`py-4 px-6 font-medium text-sm ${
                  activeTab === 'profile'
                    ? 'border-b-2 border-primary text-primary'
                    : 'text-neutral-500 hover:text-neutral-700'
                }`}
              >
                My Profile
              </TabsTrigger>
              <TabsTrigger
                value="dependents"
                className={`py-4 px-6 font-medium text-sm ${
                  activeTab === 'dependents'
                    ? 'border-b-2 border-primary text-primary'
                    : 'text-neutral-500 hover:text-neutral-700'
                }`}
              >
                Dependents
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Profile Tab Content */}
          <TabsContent value="profile" className="p-5">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1 fade-in-delay-1">
                <div className="flex flex-col items-center p-4 card-transition">
                  <div className="relative">
                    <AuthenticatedImage
                      src={previewImage || (profileData.profile.profilePictureUrl ? `/api/member/profile/photo/view?t=${imageCacheBuster}` : '')}
                      alt={`${profileData.profile.firstName} ${profileData.profile.lastName}`}
                      className="w-32 h-32 rounded-full object-cover"
                      fallback={
                        <div className="w-32 h-32 rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 text-2xl flex items-center justify-center">
                          {profileData.profile.firstName?.[0]}{profileData.profile.lastName?.[0]}
                        </div>
                      }
                    />

                    {/* Upload progress overlay */}
                    {isUploadingPhoto && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center fade-in">
                        <div className="text-white text-center">
                          <div className="relative">
                            <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                            <div className="absolute inset-0 w-8 h-8 border-2 border-transparent border-t-white rounded-full animate-pulse mx-auto"></div>
                          </div>
                          <div className="text-xs font-medium">Uploading...</div>
                          {uploadProgress > 0 && (
                            <div className="text-xs opacity-75 mt-1">{uploadProgress}%</div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="relative">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={handlePhotoClick}
                      disabled={isUploadingPhoto || isDeletingPhoto}
                      className="mt-3 text-xs text-primary hover:text-primary/80 hover:bg-primary/10"
                    >
                      {isUploadingPhoto ? (
                        <>
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Uploading...
                        </>
                      ) : isDeletingPhoto ? (
                        <>
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Removing...
                        </>
                      ) : (
                        <>
                          <Camera className="h-3 w-3 mr-1" />
                          {profileData?.profile.profilePictureUrl ? 'Edit Photo' : 'Add Photo'}
                        </>
                      )}
                    </Button>

                    {/* Photo management dropdown */}
                    {showPhotoMenu && profileData?.profile.profilePictureUrl && (
                      <div ref={photoMenuRef} className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                        <button
                          onClick={handleChangePhoto}
                          className="w-full px-3 py-2 text-left text-xs text-gray-700 hover:bg-gray-50 flex items-center"
                        >
                          <Camera className="h-3 w-3 mr-2" />
                          Change Photo
                        </button>
                        <button
                          onClick={handleRemovePhoto}
                          disabled={isDeletingPhoto}
                          className="w-full px-3 py-2 text-left text-xs text-red-600 hover:bg-red-50 flex items-center border-t border-gray-100"
                        >
                          {isDeletingPhoto ? (
                            <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                          ) : (
                            <svg className="h-3 w-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          )}
                          Remove Photo
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Hidden file input */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/png,image/webp"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <div className="mt-4 w-full">
                    <div className="text-center mb-2">
                      <h3 className="font-medium">{`${profileData.profile.firstName} ${profileData.profile.lastName}`}</h3>
                      <p className="text-sm text-neutral-500">{profileData.profile.employeeId || 'MEM001'}</p>
                    </div>
                    <div className="border-t border-neutral-200 pt-4 mt-4">
                      <p className="text-xs text-neutral-500 mb-1">Department</p>
                      <p className="text-sm font-medium">{profileData.profile.department || 'Engineering'}</p>
                    </div>
                    <div className="border-t border-neutral-200 pt-4 mt-4">
                      <p className="text-xs text-neutral-500 mb-1">Health Plan Status</p>
                      <div className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${
                          profileData.profile.healthPlanStatus === 'Active' ? 'bg-green-500' : 'bg-yellow-500'
                        }`}></span>
                        <p className={`text-sm font-medium ${
                          profileData.profile.healthPlanStatus === 'Active' ? 'text-green-500' : 'text-yellow-500'
                        }`}>
                          {profileData.profile.healthPlanStatus || 'Active'}
                        </p>
                      </div>
                    </div>
                    {/* Only show progress bar if completeness not achieved */}
                    {!profileData.stats.profileCompletenessAchieved && (
                      <div className="border-t border-neutral-200 pt-4 mt-4">
                        <p className="text-xs text-neutral-500 mb-1">Profile Completeness</p>
                        <div className="w-full bg-neutral-200 rounded-full h-2 overflow-hidden">
                          <div
                            className="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full transition-all duration-1000 ease-out animate-progress"
                            style={{ width: `${profileData.stats.profileCompleteness}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-neutral-500 mt-1">{profileData.stats.profileCompleteness}% complete</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="md:col-span-2 fade-in-delay-2">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div>
                      <h4 className="text-sm font-medium mb-3">Personal Information</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">First Name</FormLabel>
                              <FormControl>
                                <Input {...field} disabled={!isEditing} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">Last Name</FormLabel>
                              <FormControl>
                                <Input {...field} disabled={!isEditing} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">Email Address</FormLabel>
                              <FormControl>
                                <Input {...field} type="email" disabled />
                              </FormControl>
                              <FormMessage />
                              {/* Only show email restriction message in edit mode */}
                              {isEditing && (
                                <p className="text-xs text-neutral-400">Email cannot be changed</p>
                              )}
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="phone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">Phone Number</FormLabel>
                              <FormControl>
                                <Input {...field} type="tel" disabled={!isEditing} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="dateOfBirth"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">Date of Birth</FormLabel>
                              <FormControl>
                                {isEditing ? (
                                  <div className="relative">
                                    <Input
                                      {...field}
                                      type="date"
                                      disabled={!isEditing}
                                      className="pr-12 [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden"
                                      style={{ colorScheme: 'light' }}
                                    />
                                    <div
                                      className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-auto cursor-pointer"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        const input = e.currentTarget.parentElement?.querySelector('input[type="date"]') as HTMLInputElement;
                                        input?.showPicker?.();
                                      }}
                                    >
                                      <Calendar className="w-4 h-4 text-primary" />
                                    </div>
                                  </div>
                                ) : (
                                  <Input
                                    value={field.value ? formatDateForDisplay(field.value) : 'Not specified'}
                                    disabled={true}
                                    readOnly={true}
                                    className="cursor-default"
                                  />
                                )}
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="gender"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">Gender</FormLabel>
                              <FormControl>
                                {isEditing ? (
                                  <select
                                    {...field}
                                    disabled={!isEditing}
                                    className="w-full border border-neutral-300 rounded-md px-3 py-2 text-sm disabled:bg-neutral-50 disabled:text-neutral-500"
                                  >
                                    <option value="">Select gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                    <option value="prefer-not-to-say">Prefer not to say</option>
                                  </select>
                                ) : (
                                  <Input
                                    value={field.value ? (
                                      field.value === 'prefer-not-to-say' ? 'Prefer not to say' :
                                      field.value.charAt(0).toUpperCase() + field.value.slice(1)
                                    ) : 'Not specified'}
                                    disabled={true}
                                    readOnly={true}
                                    className="cursor-default"
                                  />
                                )}
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-3">Address Information</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="street"
                          render={({ field }) => (
                            <FormItem className="sm:col-span-2">
                              <FormLabel className="text-xs text-neutral-500">Street Address</FormLabel>
                              <FormControl>
                                <Input {...field} disabled={!isEditing} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="city"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">City</FormLabel>
                              <FormControl>
                                <Input {...field} disabled={!isEditing} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="state"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">State</FormLabel>
                              <FormControl>
                                <Input {...field} disabled={!isEditing} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="zipCode"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">Zip Code</FormLabel>
                              <FormControl>
                                <Input {...field} disabled={!isEditing} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="country"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs text-neutral-500">Country</FormLabel>
                              <FormControl>
                                <Input {...field} disabled={!isEditing} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {isEditing && (
                      <div className="flex justify-end space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleEditToggle}
                          disabled={isSaving}
                          className="bg-neutral-200 text-neutral-700 hover:bg-neutral-300"
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          disabled={isSaving}
                          className="bg-primary text-white hover:bg-primary/90"
                        >
                          {isSaving ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            <>
                              <Save className="h-4 w-4 mr-2" />
                              Save Changes
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </form>
                </Form>
              </div>
            </div>
          </TabsContent>

          {/* Dependents Tab Content */}
          <TabsContent value="dependents" className="p-5">

            {profileData.dependents.length === 0 ? (
              <Card className="fade-in">
                <CardContent className="p-8 text-center">
                  <Users className="text-4xl text-neutral-300 mx-auto h-16 w-16 animate-pulse" />
                  <h4 className="mt-4 text-lg font-medium">No Dependents Added</h4>
                  <p className="mt-2 text-neutral-500 text-sm">
                    Add your family members to manage their health benefits.
                  </p>
                  <Button
                    onClick={handleAddDependent}
                    className="mt-4 bg-primary text-white hover:bg-primary/90 card-transition"
                  >
                    Add First Dependent
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {profileData.dependents.map((dependent, index) => (
                  <Card key={dependent.id} className={`card-transition fade-in-delay-${(index % 3) + 1}`}>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">{`${dependent.firstName} ${dependent.lastName}`}</h4>
                          <div className="flex space-x-6 mt-1">
                            <div className="text-sm text-neutral-500">
                              <span className="text-xs font-medium block">Relationship</span>
                              {dependent.relationship}
                            </div>
                            <div className="text-sm text-neutral-500">
                              <span className="text-xs font-medium block">Date of Birth</span>
                              {formatDateForDisplay(dependent.dateOfBirth)}
                            </div>
                            {dependent.gender && (
                              <div className="text-sm text-neutral-500">
                                <span className="text-xs font-medium block">Gender</span>
                                {dependent.gender === 'prefer_not_to_say' ? 'Prefer not to say' :
                                 dependent.gender.charAt(0).toUpperCase() + dependent.gender.slice(1)}
                              </div>
                            )}
                            {dependent.memberId && (
                              <div className="text-sm text-neutral-500">
                                <span className="text-xs font-medium block">Member ID</span>
                                {dependent.memberId}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditDependent(dependent)}
                            className="text-neutral-500 hover:bg-neutral-100"
                            aria-label={`Edit ${dependent.firstName}`}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteDependent(dependent)}
                            className="text-neutral-500 hover:bg-neutral-100 hover:text-red-600"
                            aria-label={`Remove ${dependent.firstName}`}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </Card>

      {/* Dependent Form Dialog */}
      <DependentForm
        isOpen={isDependentFormOpen}
        onClose={() => {
          setIsDependentFormOpen(false)
          setEditingDependent(null)
        }}
        onSubmit={handleDependentFormSubmit}
        dependent={editingDependent}
        isLoading={isDependentLoading}
        mode={dependentFormMode}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => {
          setIsDeleteDialogOpen(false)
          setDependentToDelete(null)
        }}
        onConfirm={handleConfirmDeleteDependent}
        title="Remove Dependent"
        message={
          dependentToDelete
            ? `Are you sure you want to remove ${dependentToDelete.firstName} ${dependentToDelete.lastName} as a dependent? This action cannot be undone.`
            : 'Are you sure you want to remove this dependent?'
        }
        confirmText="Remove Dependent"
        cancelText="Cancel"
        isLoading={isDeletingDependent}
        variant="danger"
      />
    </div>
  )
}

export default MemberProfile
