import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { ApiResponse, ApiError, HttpStatus, ErrorCode } from '@aperion/shared';
import { ProfileService, MemberProfile, CreateDependentData, UpdateDependentData } from '../services/profileService';

/**
 * Get complete member profile with all data
 */
export const getMemberProfile = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';
  
  try {
    // For demo purposes, use a demo cognito user ID if no auth
    const cognitoUserId = req.user?.sub || 'demo-member-001';

    logger.info('Getting member profile', {
      requestId,
      cognitoUserId,
      isDemo: !req.user?.sub
    });

    // First, try to get existing profile
    let profile = await ProfileService.getMemberProfile(cognitoUserId);

    // If no profile exists, return 404
    if (!profile) {
      logger.info('Profile not found', { requestId, cognitoUserId });

      res.status(404).json({
        success: false,
        error: {
          code: 'PROFILE_NOT_FOUND',
          message: 'Member profile not found. Please complete your profile setup.',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    if (!profile) {
      throw new Error('Failed to create or retrieve member profile');
    }

    // Get dependents
    const dependents = await ProfileService.getMemberDependents(profile.id);

    // Prepare complete response
    const responseData = {
      profile: {
        id: profile.id,
        firstName: profile.firstName,
        lastName: profile.lastName,
        email: profile.email,
        phone: profile.phone,
        dateOfBirth: profile.dateOfBirth,
        gender: profile.gender,
        address: profile.address,
        profilePictureUrl: profile.profilePictureUrl,
        emergencyContacts: profile.emergencyContacts,
        healthPreferences: profile.healthPreferences,
        privacySettings: profile.privacySettings,
        department: profile.department,
        healthPlanStatus: profile.healthPlanStatus,
        status: profile.status,
        employeeId: profile.id.substring(0, 8).toUpperCase(), // Generate employee ID from member ID
        createdAt: profile.createdAt,
        updatedAt: profile.updatedAt,
      },
      dependents: dependents.map(dep => ({
        id: dep.id,
        firstName: dep.firstName,
        lastName: dep.lastName,
        relationship: dep.relationship,
        dateOfBirth: dep.dateOfBirth,
        memberId: dep.memberId_dependent || `DEP${dep.id.substring(0, 3).toUpperCase()}`,
      })),
      stats: {
        totalDependents: dependents.length,
        profileCompleteness: calculateProfileCompleteness(profile),
        profileCompletenessAchieved: false, // Default to false until migration is applied
        profileCompletenessAchievedAt: null, // Default to null until migration is applied
        lastUpdated: profile.updatedAt,
      }
    };

    const response: ApiResponse<typeof responseData> = {
      success: true,
      data: responseData,
    };

    logger.info('Member profile retrieved successfully', {
      requestId,
      cognitoUserId,
      memberId: profile.id,
      dependentCount: dependents.length,
      profileCompleteness: responseData.stats.profileCompleteness
    });

    res.status(HttpStatus.OK).json(response);

  } catch (error) {
    logger.error('Error getting member profile', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to retrieve member profile',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Update member profile
 */
export const updateMemberProfile = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';
  
  try {
    const cognitoUserId = req.user?.sub || 'demo-member-001';
    const updateData = req.body;

    logger.info('Updating member profile', {
      requestId,
      cognitoUserId,
      updateFields: Object.keys(updateData)
    });

    // Validate update data - expanded to include more profile fields
    const allowedFields = [
      'phone', 'address', 'emergencyContacts', 'healthPreferences',
      'privacySettings', 'profilePictureUrl', 'firstName', 'lastName',
      'dateOfBirth', 'gender'
    ];

    const filteredData: any = {};
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        filteredData[key] = value;
      }
    }

    if (Object.keys(filteredData).length === 0) {
      const apiError: ApiError = {
        code: ErrorCode.VALIDATION_ERROR,
        message: 'No valid fields provided for update',
        details: { allowedFields },
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    // Update profile
    const updatedProfile = await ProfileService.updateMemberProfile(cognitoUserId, filteredData);

    const response: ApiResponse<{ profile: MemberProfile }> = {
      success: true,
      data: { profile: updatedProfile },
    };

    logger.info('Member profile updated successfully', {
      requestId,
      cognitoUserId,
      updatedFields: Object.keys(filteredData)
    });

    res.status(HttpStatus.OK).json(response);

  } catch (error) {
    logger.error('Error updating member profile', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to update member profile',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Get member dependents only
 */
export const getMemberDependents = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';
  
  try {
    const cognitoUserId = req.user?.sub || 'demo-member-001';
    
    logger.info('Getting member dependents', { requestId, cognitoUserId });

    // Get member profile to get member ID
    const profile = await ProfileService.getMemberProfile(cognitoUserId);
    if (!profile) {
      const apiError: ApiError = {
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Member profile not found',
        details: {},
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    const dependents = await ProfileService.getMemberDependents(profile.id);

    const response: ApiResponse<{ dependents: any[] }> = {
      success: true,
      data: { 
        dependents: dependents.map(dep => ({
          id: dep.id,
          firstName: dep.firstName,
          lastName: dep.lastName,
          relationship: dep.relationship,
          dateOfBirth: dep.dateOfBirth,
          memberId: dep.memberId_dependent || `DEP${dep.id.substring(0, 3).toUpperCase()}`,
        }))
      },
    };

    logger.info('Member dependents retrieved successfully', {
      requestId,
      cognitoUserId,
      dependentCount: dependents.length
    });

    res.status(HttpStatus.OK).json(response);

  } catch (error) {
    logger.error('Error getting member dependents', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to retrieve member dependents',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

// Profile completeness calculation moved to ProfileService.calculateProfileCompleteness()
// Temporary function for backward compatibility until migration is complete
function calculateProfileCompleteness(profile: MemberProfile): number {
  // Only include fields that are displayed in the frontend profile form
  // Excludes emergency contacts as they are not shown in the profile form
  const requiredFields = [
    'firstName',      // Personal Information
    'lastName',       // Personal Information
    'email',          // Personal Information (read-only)
    'phone',          // Personal Information
    'dateOfBirth',    // Personal Information
    'gender',         // Personal Information
    'address',        // Address Information (all sub-fields)
    'profilePictureUrl' // Profile Photo
  ];

  let completedFields = 0;

  for (const field of requiredFields) {
    const value = profile[field as keyof MemberProfile];

    if (field === 'address') {
      // For address, check all required sub-fields that are in the frontend
      const address = value as any;
      if (address &&
          address.street && address.street.trim() !== '' &&
          address.city && address.city.trim() !== '' &&
          address.state && address.state.trim() !== '' &&
          address.zipCode && address.zipCode.trim() !== '' &&
          address.country && address.country.trim() !== '') {
        completedFields++;
      }
    } else {
      // For other fields, check if they have valid values
      if (value &&
          (typeof value === 'string' ? value.trim() !== '' :
           Array.isArray(value) ? value.length > 0 :
           typeof value === 'object' ? Object.keys(value).length > 0 : true)) {
        completedFields++;
      }
    }
  }

  return Math.round((completedFields / requiredFields.length) * 100);
}

/**
 * Create a new dependent
 */
export const createDependent = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const cognitoUserId = req.user?.sub || 'demo-member-001';
    const dependentData: CreateDependentData = req.body;

    logger.info('Creating dependent', {
      requestId,
      cognitoUserId,
      dependentData: { ...dependentData, dateOfBirth: '[REDACTED]' }
    });

    // Get member profile to get member ID
    const profile = await ProfileService.getMemberProfile(cognitoUserId);
    if (!profile) {
      const apiError: ApiError = {
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Member profile not found',
        details: {},
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    // Create the dependent
    const dependent = await ProfileService.createDependent(profile.id, dependentData);

    const response: ApiResponse<{ dependent: any }> = {
      success: true,
      data: {
        dependent: {
          id: dependent.id,
          firstName: dependent.firstName,
          lastName: dependent.lastName,
          relationship: dependent.relationship,
          dateOfBirth: dependent.dateOfBirth,
          gender: dependent.gender,
          memberId: dependent.memberId_dependent || `DEP${dependent.id.substring(0, 3).toUpperCase()}`,
        }
      },
    };

    logger.info('Dependent created successfully', {
      requestId,
      cognitoUserId,
      dependentId: dependent.id
    });

    res.status(HttpStatus.CREATED).json(response);

  } catch (error) {
    logger.error('Error creating dependent', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to create dependent',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Update an existing dependent
 */
export const updateDependent = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const cognitoUserId = req.user?.sub || 'demo-member-001';
    const dependentId = req.params.dependentId;
    const updateData: UpdateDependentData = req.body;

    logger.info('Updating dependent', {
      requestId,
      cognitoUserId,
      dependentId,
      updateFields: Object.keys(updateData)
    });

    // Get member profile to get member ID
    const profile = await ProfileService.getMemberProfile(cognitoUserId);
    if (!profile) {
      const apiError: ApiError = {
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Member profile not found',
        details: {},
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    // Update the dependent
    const dependent = await ProfileService.updateDependent(profile.id, dependentId, updateData);

    if (!dependent) {
      const apiError: ApiError = {
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Dependent not found',
        details: {},
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    const response: ApiResponse<{ dependent: any }> = {
      success: true,
      data: {
        dependent: {
          id: dependent.id,
          firstName: dependent.firstName,
          lastName: dependent.lastName,
          relationship: dependent.relationship,
          dateOfBirth: dependent.dateOfBirth,
          gender: dependent.gender,
          memberId: dependent.memberId_dependent || `DEP${dependent.id.substring(0, 3).toUpperCase()}`,
        }
      },
    };

    logger.info('Dependent updated successfully', {
      requestId,
      cognitoUserId,
      dependentId
    });

    res.status(HttpStatus.OK).json(response);

  } catch (error) {
    logger.error('Error updating dependent', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to update dependent',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};

/**
 * Delete a dependent
 */
export const deleteDependent = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';

  try {
    const cognitoUserId = req.user?.sub || 'demo-member-001';
    const dependentId = req.params.dependentId;

    logger.info('Deleting dependent', {
      requestId,
      cognitoUserId,
      dependentId
    });

    // Get member profile to get member ID
    const profile = await ProfileService.getMemberProfile(cognitoUserId);
    if (!profile) {
      const apiError: ApiError = {
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Member profile not found',
        details: {},
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    // Delete the dependent
    const deleted = await ProfileService.deleteDependent(profile.id, dependentId);

    if (!deleted) {
      const apiError: ApiError = {
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Dependent not found',
        details: {},
        timestamp: new Date().toISOString(),
        requestId,
        path: req.originalUrl,
        method: req.method,
      };

      res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        error: apiError,
      } as ApiResponse);
      return;
    }

    const response: ApiResponse<{ message: string }> = {
      success: true,
      data: { message: 'Dependent deleted successfully' },
    };

    logger.info('Dependent deleted successfully', {
      requestId,
      cognitoUserId,
      dependentId
    });

    res.status(HttpStatus.OK).json(response);

  } catch (error) {
    logger.error('Error deleting dependent', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    const apiError: ApiError = {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'Failed to delete dependent',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString(),
      requestId,
      path: req.originalUrl,
      method: req.method,
    };

    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: apiError,
    } as ApiResponse);
  }
};
