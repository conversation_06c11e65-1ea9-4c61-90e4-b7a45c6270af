#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';

interface FrontendApp {
  name: string;
  title: string;
  description: string;
  port: number;
  apiPath: string;
}

interface PackageJson {
  name: string;
  version: string;
  description: string;
  type: string;
  scripts: Record<string, string>;
  dependencies: Record<string, string>;
  devDependencies: Record<string, string>;
  keywords: string[];
  author: string;
  license: string;
}

const frontendApps: FrontendApp[] = [
  {
    name: 'employer-portal',
    title: 'Employer Portal',
    description: 'Employer Portal - React frontend for Employer Service',
    port: 4002,
    apiPath: '/api/employer'
  },
  {
    name: 'wellness-portal',
    title: 'Wellness Portal',
    description: 'Wellness Portal - React frontend for Wellness Central Service',
    port: 4003,
    apiPath: '/api/wellness'
  },
  {
    name: 'lms-portal',
    title: 'LMS Portal',
    description: 'LMS Portal - React frontend for ZenX LMS Service',
    port: 4004,
    apiPath: '/api/lms'
  },
  {
    name: 'admin-portal',
    title: 'Admin Portal',
    description: 'Admin Portal - React frontend for Command Center Service',
    port: 4005,
    apiPath: '/api/command-center'
  }
];

function createPackageJson(app: FrontendApp): PackageJson {
  return {
    name: `@aperion/${app.name}`,
    version: "1.0.0",
    description: app.description,
    type: "module",
    scripts: {
      dev: "vite",
      build: "tsc && vite build",
      preview: "vite preview",
      lint: "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
      "lint:fix": "eslint . --ext ts,tsx --fix",
      "type-check": "tsc --noEmit"
    },
    dependencies: {
      react: "^18.2.0",
      "react-dom": "^18.2.0",
      "react-router-dom": "^6.8.0",
      "@tanstack/react-query": "^4.24.0",
      "@tanstack/react-query-devtools": "^4.24.0",
      axios: "^1.3.0",
      clsx: "^1.2.1",
      "tailwind-merge": "^1.10.0",
      "class-variance-authority": "^0.4.0",
      "lucide-react": "^0.323.0",
      "@radix-ui/react-slot": "^1.0.1",
      "@radix-ui/react-dialog": "^1.0.3",
      "@radix-ui/react-dropdown-menu": "^2.0.4",
      "@radix-ui/react-toast": "^1.1.3",
      "@radix-ui/react-avatar": "^1.0.2",
      "@radix-ui/react-button": "^0.1.0",
      "@radix-ui/react-card": "^0.1.0",
      "@radix-ui/react-form": "^0.0.3",
      "@radix-ui/react-label": "^2.0.1",
      "@radix-ui/react-input": "^1.0.2",
      "@aperion/shared": "workspace:*"
    },
    devDependencies: {
      "@types/react": "^18.2.43",
      "@types/react-dom": "^18.2.17",
      "@typescript-eslint/eslint-plugin": "^6.14.0",
      "@typescript-eslint/parser": "^6.14.0",
      "@vitejs/plugin-react": "^4.2.1",
      autoprefixer: "^10.4.16",
      eslint: "^8.55.0",
      "eslint-plugin-react-hooks": "^4.6.0",
      "eslint-plugin-react-refresh": "^0.4.5",
      postcss: "^8.4.32",
      tailwindcss: "^3.3.6",
      typescript: "^5.2.2",
      vite: "^5.0.8"
    },
    keywords: ["aperion", "health", "frontend", "react", app.name],
    author: "Aperion Health Team",
    license: "UNLICENSED"
  };
}

function createViteConfig(app: FrontendApp): string {
  return `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: ${app.port},
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
})`;
}

function createIndexHtml(app: FrontendApp): string {
  return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Aperion Health - ${app.title}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`;
}

function createTsConfig(): string {
  return `{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}`;
}

function createTsConfigNode(): string {
  return `{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
}`;
}

function generateFrontendApp(app: FrontendApp): void {
  const appDir = path.join(__dirname, '..', 'apps', app.name);
  
  console.log(`🚀 Generating frontend app: ${app.name} on port ${app.port}`);
  
  // Create directory structure
  fs.mkdirSync(appDir, { recursive: true });
  fs.mkdirSync(path.join(appDir, 'src'), { recursive: true });
  fs.mkdirSync(path.join(appDir, 'src', 'components'), { recursive: true });
  fs.mkdirSync(path.join(appDir, 'src', 'pages'), { recursive: true });
  fs.mkdirSync(path.join(appDir, 'public'), { recursive: true });
  
  // Create package.json
  fs.writeFileSync(
    path.join(appDir, 'package.json'),
    JSON.stringify(createPackageJson(app), null, 2)
  );
  console.log(`   📄 Created package.json`);
  
  // Create vite.config.ts
  fs.writeFileSync(
    path.join(appDir, 'vite.config.ts'),
    createViteConfig(app)
  );
  console.log(`   📄 Created vite.config.ts`);
  
  // Create index.html
  fs.writeFileSync(
    path.join(appDir, 'index.html'),
    createIndexHtml(app)
  );
  console.log(`   📄 Created index.html`);
  
  // Create TypeScript configs
  fs.writeFileSync(
    path.join(appDir, 'tsconfig.json'),
    createTsConfig()
  );
  console.log(`   📄 Created tsconfig.json`);
  
  fs.writeFileSync(
    path.join(appDir, 'tsconfig.node.json'),
    createTsConfigNode()
  );
  console.log(`   📄 Created tsconfig.node.json`);
  
  console.log(`   ✅ Frontend app ${app.name} generated successfully\n`);
}

function generateAllFrontendApps(): void {
  console.log('🏗️  Generating all Aperion Health frontend applications...\n');
  
  frontendApps.forEach(app => {
    generateFrontendApp(app);
  });

  console.log('🎉 All frontend applications generated successfully!');
  console.log('\n📋 To start all applications:');
  console.log('   npm run dev');
}

function main(): void {
  const args = process.argv.slice(2);
  const command = args[0];
  const appName = args[1];

  if (command === 'generate' && appName) {
    const app = frontendApps.find(a => a.name === appName);
    if (!app) {
      console.error(`❌ Frontend app '${appName}' not found`);
      console.log('Available apps:', frontendApps.map(a => a.name).join(', '));
      process.exit(1);
    }
    generateFrontendApp(app);
  } else if (command === 'all') {
    generateAllFrontendApps();
  } else {
    console.log('Aperion Health Frontend Generator');
    console.log('');
    console.log('Usage:');
    console.log('  tsx scripts/generate-frontend.ts generate <app-name>  # Generate single app');
    console.log('  tsx scripts/generate-frontend.ts all                  # Generate all apps');
    console.log('');
    console.log('Available apps:');
    frontendApps.forEach(app => {
      console.log(`  ${app.name} - ${app.description} (port ${app.port})`);
    });
  }
}

if (require.main === module) {
  main();
}

export { generateFrontendApp, generateAllFrontendApps, frontendApps };
