#!/bin/bash

# Aperion Health - Troubleshooting Script
# Diagnoses and fixes common issues with the UserRole import fix

set -e

echo "🛠️  Aperion Health - Troubleshooting Guide"
echo "==========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_fix() {
    echo -e "${YELLOW}🔧 Fix: $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "packages/shared" ]; then
    print_error "Please run this script from the Aperion Health project root directory"
    exit 1
fi

echo "Diagnosing common issues..."
echo ""

# Function to generate debug report
generate_debug_report() {
    echo "📋 Generating debug report..."
    
    cat > debug-report.txt << EOF
=== Aperion Health Debug Report ===
Generated: $(date)

Node.js version: $(node --version)
NPM version: $(npm --version)
Operating System: $(uname -a)

=== Project Structure ===
$(ls -la)

=== Shared Package Structure ===
$(ls -la packages/shared/dist/ 2>/dev/null || echo "dist directory not found")

=== Package.json (shared) ===
$(cat packages/shared/package.json)

=== CJS Index Content ===
$(head -10 packages/shared/dist/cjs/index.js 2>/dev/null || echo "CJS index.js not found")

=== ESM Index Content ===
$(cat packages/shared/dist/esm/index.js 2>/dev/null || echo "ESM index.js not found")

=== Environment File ===
$(cat .env 2>/dev/null || echo ".env file not found")

=== Running Processes ===
$(ps aux | grep -E "(node|npm)" | grep -v grep || echo "No Node processes found")

=== Port Usage ===
$(lsof -i :3000,3001,3005,4000,4001 2>/dev/null || echo "No processes on expected ports")
EOF

    print_success "Debug report generated: debug-report.txt"
}

# Issue 1: Check if shared package is built
print_info "Issue 1: Checking shared package build"
if [ ! -d "packages/shared/dist" ]; then
    print_error "Shared package not built"
    print_fix "Run: cd packages/shared && npm run build"
    echo ""
elif [ ! -d "packages/shared/dist/cjs" ] || [ ! -d "packages/shared/dist/esm" ]; then
    print_error "Incomplete shared package build"
    print_fix "Run: cd packages/shared && npm run clean && npm run build"
    echo ""
else
    print_success "Shared package build exists"
fi
echo ""

# Issue 2: Check Node.js version
print_info "Issue 2: Checking Node.js compatibility"
NODE_VERSION=$(node --version)
if [[ "$NODE_VERSION" < "v16" ]]; then
    print_warning "Node.js version $NODE_VERSION may cause ES modules issues"
    print_fix "Upgrade to Node.js 16+ for better ES modules support"
else
    print_success "Node.js version $NODE_VERSION is compatible"
fi
echo ""

# Issue 3: Check for cached dependencies
print_info "Issue 3: Checking for dependency cache issues"
if [ -d "node_modules/.cache" ] || [ -d "apps/aperion-portal/node_modules/.cache" ]; then
    print_warning "Cache directories found - may cause stale module issues"
    print_fix "Clear cache with: rm -rf node_modules/.cache apps/aperion-portal/node_modules/.cache"
else
    print_success "No problematic cache directories found"
fi
echo ""

# Issue 4: Check package-lock files
print_info "Issue 4: Checking for package-lock conflicts"
LOCK_FILES=$(find . -name "package-lock.json" | wc -l)
if [ "$LOCK_FILES" -gt 1 ]; then
    print_warning "Multiple package-lock.json files found ($LOCK_FILES total)"
    print_fix "Clean install: rm -rf node_modules packages/*/node_modules services/*/node_modules apps/*/node_modules package-lock.json packages/*/package-lock.json services/*/package-lock.json apps/*/package-lock.json && npm install"
else
    print_success "Package-lock structure looks good"
fi
echo ""

# Issue 5: Check TypeScript configuration
print_info "Issue 5: Checking TypeScript configuration files"
REQUIRED_TS_CONFIGS=(
    "packages/shared/tsconfig.cjs.json"
    "packages/shared/tsconfig.esm.json"
    "packages/shared/tsconfig.types.json"
)

missing_configs=()
for config in "${REQUIRED_TS_CONFIGS[@]}"; do
    if [ ! -f "$config" ]; then
        missing_configs+=("$config")
    fi
done

if [ ${#missing_configs[@]} -gt 0 ]; then
    print_error "Missing TypeScript configuration files:"
    for config in "${missing_configs[@]}"; do
        echo "  - $config"
    done
    print_fix "Re-run setup script: ./setup-aperion-fix.sh"
else
    print_success "All required TypeScript configurations exist"
fi
echo ""

# Issue 6: Check environment variables
print_info "Issue 6: Checking environment configuration"
if [ ! -f ".env" ]; then
    print_warning ".env file not found"
    print_fix "Copy .env.example to .env and configure: cp .env.example .env"
elif ! grep -q "CORS_ORIGINS" .env; then
    print_warning "CORS_ORIGINS not configured in .env"
    print_fix "Add CORS_ORIGINS=http://localhost:4000,http://localhost:4001,http://localhost:3000 to .env"
else
    print_success "Environment configuration looks good"
fi
echo ""

# Issue 7: Check for port conflicts
print_info "Issue 7: Checking for port conflicts"
PORTS=(3000 3001 3005 4000 4001)
conflicts=()

for port in "${PORTS[@]}"; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        conflicts+=("$port")
    fi
done

if [ ${#conflicts[@]} -gt 0 ]; then
    print_warning "Ports in use: ${conflicts[*]}"
    print_fix "Stop conflicting services or use different ports"
else
    print_success "No port conflicts detected"
fi
echo ""

# Issue 8: Check workspace configuration
print_info "Issue 8: Checking workspace configuration"
if grep -q '"workspaces"' package.json; then
    print_success "Workspace configuration found"
    
    # Check if shared package is properly linked
    if npm ls @aperion/shared >/dev/null 2>&1; then
        print_success "Shared package is properly linked in workspace"
    else
        print_warning "Shared package may not be properly linked"
        print_fix "Re-install workspace: npm install"
    fi
else
    print_error "Workspace configuration missing in package.json"
    print_fix "Ensure package.json has workspaces configuration"
fi
echo ""

# Interactive troubleshooting menu
echo "🔧 INTERACTIVE TROUBLESHOOTING"
echo "==============================="
echo ""
echo "What issue are you experiencing?"
echo "1. UserRole import error in browser"
echo "2. Backend services won't start"
echo "3. Frontend shows blank page"
echo "4. Build errors"
echo "5. Generate debug report"
echo "6. Full reset and rebuild"
echo "0. Exit"
echo ""

read -p "Select option (0-6): " choice

case $choice in
    1)
        echo ""
        print_info "Fixing UserRole import error..."
        cd packages/shared
        npm run clean
        npm run build
        cd ../..
        print_success "Rebuilt shared package - restart frontend"
        ;;
    2)
        echo ""
        print_info "Fixing backend service issues..."
        cd packages/shared
        npm run build:cjs
        cd ../..
        print_success "Rebuilt CommonJS format - restart backend services"
        ;;
    3)
        echo ""
        print_info "Fixing frontend blank page..."
        cd apps/aperion-portal
        rm -rf node_modules/.cache
        cd ../..
        cd packages/shared
        npm run build:esm
        cd ../..
        print_success "Cleared cache and rebuilt ES modules - restart frontend"
        ;;
    4)
        echo ""
        print_info "Fixing build errors..."
        rm -rf packages/shared/dist
        cd packages/shared
        npm run build
        cd ../..
        print_success "Clean rebuild completed"
        ;;
    5)
        echo ""
        generate_debug_report
        ;;
    6)
        echo ""
        print_info "Performing full reset..."
        ./setup-aperion-fix.sh
        print_success "Full reset completed"
        ;;
    0)
        echo "Exiting troubleshooting"
        exit 0
        ;;
    *)
        print_error "Invalid option"
        exit 1
        ;;
esac

echo ""
print_success "Troubleshooting action completed!"
echo ""
echo "Next steps:"
echo "1. Restart affected services"
echo "2. Test the application"
echo "3. Run ./verify-fix.sh to confirm fix"
