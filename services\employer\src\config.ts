import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root .env
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

export const config = {
  serviceName: 'employer',
  port: parseInt(process.env.EMPLOYER_PORT || '3002', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  // Single Database Configuration
  database: {
    host: process.env.DB_HOST || '**************',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    name: process.env.DB_NAME || 'AperionHealth-Dev',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'Cloud@2025',
    schema: process.env.DB_SCHEMA || 'employer_service',
    // Add shared_data schema access for Command Center pattern
    sharedSchema: 'shared_data',
    ssl: process.env.DB_SSL === 'true',
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10', 10),
    idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000', 10),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000', 10),
  },

  // JWT
  jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret',
  serviceJwtSecret: process.env.SERVICE_JWT_SECRET || 'your-service-jwt-secret',

  // CORS
  corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],

  // Logging
  logLevel: process.env.LOG_LEVEL || 'info',
};
