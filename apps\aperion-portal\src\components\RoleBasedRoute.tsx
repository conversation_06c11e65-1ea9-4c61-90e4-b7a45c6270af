import { Navigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@aperion/shared'

interface RoleBasedRouteProps {
  children: React.ReactNode
  allowedRoles: UserRole[]
}

const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({ children, allowedRoles }) => {
  const { user } = useAuth()

  if (!user) {
    return <Navigate to="/auth/send-code" replace />
  }

  // Convert UserRole enum values to strings for comparison
  const allowedRoleStrings = allowedRoles.map(role => role.toString())

  if (!allowedRoleStrings.includes(user.role)) {
    // Redirect to user's default route if they don't have access
    const { getDefaultRoute } = useAuth()
    return <Navigate to={getDefaultRoute()} replace />
  }

  return <>{children}</>
}

export default RoleBasedRoute
