import { z } from 'zod';

/**
 * Email validation utility
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Phone number validation utility
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
};



/**
 * UUID validation utility
 */
export const validateUUID = (uuid: string): boolean => {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Activation code validation
 */
export const validateActivationCode = (code: string): boolean => {
  // 8-character alphanumeric code
  const codeRegex = /^[A-Z0-9]{8}$/;
  return codeRegex.test(code);
};

/**
 * URL validation utility
 */
export const validateURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Date validation utility
 */
export const validateDate = (date: string): boolean => {
  const parsedDate = new Date(date);
  return !isNaN(parsedDate.getTime());
};

/**
 * Age validation (must be 18 or older)
 */
export const validateAge = (dateOfBirth: string): boolean => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    return age - 1 >= 18;
  }

  return age >= 18;
};

/**
 * File type validation
 */
export const validateFileType = (
  filename: string,
  allowedTypes: string[]
): boolean => {
  const extension = filename.split('.').pop()?.toLowerCase();
  return extension ? allowedTypes.includes(extension) : false;
};

/**
 * File size validation (in bytes)
 */
export const validateFileSize = (size: number, maxSize: number): boolean => {
  return size <= maxSize;
};

/**
 * Sanitize string input
 */
export const sanitizeString = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .substring(0, 1000); // Limit length
};

/**
 * Validate and sanitize user input
 */
export const validateAndSanitizeInput = (
  input: any,
  schema: z.ZodSchema
): { isValid: boolean; data?: any; errors?: string[] } => {
  try {
    const validatedData = schema.parse(input);
    return {
      isValid: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
      };
    }
    return {
      isValid: false,
      errors: ['Validation failed'],
    };
  }
};

/**
 * Common validation schemas
 */
export const commonSchemas = {
  id: z.string().uuid('Invalid ID format'),
  email: z.string().email('Invalid email format'),
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain uppercase letter')
    .regex(/[a-z]/, 'Password must contain lowercase letter')
    .regex(/\d/, 'Password must contain number'),
  activationCode: z
    .string()
    .regex(/^[A-Z0-9]{8}$/, 'Invalid activation code format'),
  url: z.string().url('Invalid URL format'),
  date: z.string().refine((val) => {
    // Accept both yyyy-MM-dd and ISO 8601 formats
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;

    if (dateRegex.test(val) || isoRegex.test(val)) {
      const date = new Date(val);
      return !isNaN(date.getTime());
    }
    return false;
  }, 'Invalid date format - must be yyyy-MM-dd or ISO 8601'),
  positiveNumber: z.number().positive('Must be a positive number'),
  nonEmptyString: z.string().min(1, 'Field cannot be empty'),
};

/**
 * Validate pagination parameters
 */
export const validatePagination = (params: {
  page?: string;
  limit?: string;
}): { page: number; limit: number } => {
  const page = Math.max(1, parseInt(params.page || '1', 10) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(params.limit || '20', 10) || 20));

  return { page, limit };
};

/**
 * Validate sort parameters
 */
export const validateSort = (
  sortBy?: string,
  sortOrder?: string,
  allowedFields: string[] = []
): { sortBy?: string; sortOrder: 'asc' | 'desc' } => {
  const validSortBy =
    sortBy && allowedFields.includes(sortBy) ? sortBy : undefined;
  const validSortOrder = sortOrder === 'desc' ? 'desc' : 'asc';

  const result: { sortBy?: string; sortOrder: 'asc' | 'desc' } = {
    sortOrder: validSortOrder,
  };

  if (validSortBy) {
    result.sortBy = validSortBy;
  }

  return result;
};

/**
 * Validate search query
 */
export const validateSearchQuery = (query?: string): string | undefined => {
  if (!query || typeof query !== 'string') {
    return undefined;
  }

  const sanitized = sanitizeString(query);
  return sanitized.length >= 2 ? sanitized : undefined;
};

/**
 * Validate JSON input
 */
export const validateJSON = (input: string): { isValid: boolean; data?: any } => {
  try {
    const data = JSON.parse(input);
    return { isValid: true, data };
  } catch {
    return { isValid: false };
  }
};

/**
 * Validate array of IDs
 */
export const validateIdArray = (ids: any): string[] => {
  if (!Array.isArray(ids)) {
    return [];
  }

  return ids.filter(id => typeof id === 'string' && validateUUID(id));
};

/**
 * Validate time range
 */
export const validateTimeRange = (
  startTime: string,
  endTime: string
): boolean => {
  const start = new Date(startTime);
  const end = new Date(endTime);

  return (
    !isNaN(start.getTime()) &&
    !isNaN(end.getTime()) &&
    start < end
  );
};

/**
 * Validate business hours
 */
export const validateBusinessHours = (time: string): boolean => {
  const hour = new Date(`1970-01-01T${time}`).getHours();
  return hour >= 8 && hour <= 18; // 8 AM to 6 PM
};

/**
 * Validate future date
 */
export const validateFutureDate = (date: string): boolean => {
  const inputDate = new Date(date);
  const now = new Date();
  return inputDate > now;
};

/**
 * Normalize date format to yyyy-MM-dd for database storage
 */
export const normalizeDateFormat = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString; // Return original if invalid
    }
    // Return yyyy-MM-dd format
    return date.toISOString().split('T')[0];
  } catch {
    return dateString; // Return original if parsing fails
  }
};

/**
 * Validate date format (accepts both yyyy-MM-dd and ISO 8601)
 */
export const validateDateFormat = (dateString: string): boolean => {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;

  if (dateRegex.test(dateString) || isoRegex.test(dateString)) {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }
  return false;
};

/**
 * Password strength validation
 */
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  errors: string[];
  score: number;
} => {
  const errors: string[] = [];
  let score = 0;

  // Length check
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  } else if (password.length >= 12) {
    score += 2;
  } else {
    score += 1;
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  // Special character check
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  // Common patterns check
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i,
  ];

  if (commonPatterns.some(pattern => pattern.test(password))) {
    errors.push('Password contains common patterns and is not secure');
    score = Math.max(0, score - 2);
  }

  // Sequential characters check
  if (/(.)\1{2,}/.test(password)) {
    errors.push('Password should not contain repeated characters');
    score = Math.max(0, score - 1);
  }

  return {
    isValid: errors.length === 0 && score >= 4,
    errors,
    score: Math.min(5, score),
  };
};
