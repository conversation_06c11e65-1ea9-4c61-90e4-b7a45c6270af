import dotenv from 'dotenv';
import path from 'path';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

// Load environment variables from root .env
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

import { config } from './config';
import { httpLogger } from './utils/logger';
import { logger } from '../../utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';
import { authMiddleware } from './middleware/auth';
import { healthRouter } from './routes/health';
import { userRouter } from './routes/users';
import { otpRouter } from './routes/otp';
import { companiesRouter } from './routes/companies';
import { subscriptionsRouter } from './routes/subscriptions';
import { sessionsRouter } from './routes/sessions';
import { assignmentsRouter } from './routes/assignments';

const app = express();

// Security middleware
app.use(helmet());
app.use(
  cors({
    origin: config.corsOrigins,
    credentials: true,
  })
);

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Body parsing
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging with Pino
app.use(requestLogger); // Custom request ID and correlation ID middleware
app.use(httpLogger); // Custom HTTP logger for access.log

// Health check (no auth required)
app.use('/health', healthRouter);

// OTP routes (no auth required - part of login flow)
app.use('/auth', otpRouter);

// Protected routes with proper JWT verification
app.use('/api/command-center', authMiddleware);

// User management routes with proper JWT verification
app.use('/admin/users', (req, res, next) => {
  // Only skip auth for public confirmation endpoint
  if (req.path === '/account-confirmation') {
    console.log('Skipping auth for public confirmation route:', req.path);
    return next();
  }
  // Apply auth for all other routes
  console.log('Applying auth for route:', req.path);
  return authMiddleware(req, res, next);
}, userRouter);

// Command center management routes with proper JWT authentication
// All admin routes now require authentication - no dev bypasses
app.use('/admin/companies', authMiddleware, companiesRouter);
app.use('/admin/subscriptions', authMiddleware, subscriptionsRouter);
app.use('/admin/sessions', authMiddleware, sessionsRouter);
app.use('/admin/assignments', authMiddleware, assignmentsRouter);

// Error handling
app.use(errorHandler);

const server = app.listen(config.port, () => {
  console.log('Environment variables check:', {
    AWS_REGION: process.env.AWS_REGION,
    hasAccessKey: !!process.env.AWS_ACCESS_KEY_ID,
    hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY,
    CLIENT_ID: process.env.CLIENT_ID,
  });
  logger.info(`${config.serviceName} started on port ${config.port}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;
