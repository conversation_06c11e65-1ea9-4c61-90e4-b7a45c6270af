/**
 * Profile Photo Upload Controller
 * Handles profile photo uploads with S3 integration and database updates
 */

import { Request, Response } from 'express';
import { uploadProfilePhoto as sharedUploadProfilePhoto, generateSignedUrl } from '@aperion/shared/server';
import { ProfileService } from '../services/profileService';
import { logger } from '../utils/logger';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';

// Temporary S3 delete function until shared module import is fixed
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
});

const deleteFromS3 = async (key: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const bucketName = process.env.BUCKET_NAME || 'aperion-documents';

    const command = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: key,
    });

    await s3Client.send(command);
    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

/**
 * Upload profile photo endpoint
 * POST /api/member/profile/photo
 */
export const uploadProfilePhoto = async (req: Request, res: Response): Promise<void> => {
  const requestId = (req as any).requestId || 'unknown';

  try {
    const cognitoUserId = (req as any).user?.sub || 'demo-member-001';

    logger.info('Profile photo upload request', {
      requestId,
      cognitoUserId,
      hasFile: !!req.file,
    });

    // File should be available from middleware
    if (!req.file) {
      res.status(400).json({
        success: false,
        error: {
          code: 'NO_FILE',
          message: 'No file uploaded',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Get member profile to retrieve employer_id and member_id
    const memberProfile = await ProfileService.getMemberProfile(cognitoUserId);
    if (!memberProfile) {
      res.status(404).json({
        success: false,
        error: {
          code: 'MEMBER_NOT_FOUND',
          message: 'Member profile not found',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Use shared upload utility
    const uploadResult = await sharedUploadProfilePhoto(
      req.file,
      memberProfile.employerId,
      memberProfile.id
    );

    if (!uploadResult.success) {
      res.status(500).json({
        success: false,
        error: {
          code: 'UPLOAD_FAILED',
          message: uploadResult.error || 'Failed to upload photo',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Store S3 key in database instead of full URL for permanent solution
    await ProfileService.updateMemberProfile(cognitoUserId, {
      profilePictureUrl: uploadResult.key!, // Store S3 key instead of signed URL
    });

    logger.info('Profile photo upload completed successfully', {
      requestId,
      cognitoUserId,
      memberId: memberProfile.id,
    });

    // Return success response with secure proxy URL
    const proxyUrl = `/api/member/profile/photo/view`;

    res.status(200).json({
      success: true,
      data: {
        profilePictureUrl: proxyUrl, // Return secure proxy URL instead of signed URL
        uploadInfo: {
          originalSize: req.file.size,
          finalSize: uploadResult.processedImage?.size || req.file.size,
          s3Key: uploadResult.key,
        },
      },
    });

  } catch (error: any) {
    logger.error('Profile photo upload error', {
      requestId,
      error: error.message,
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to upload profile photo',
        timestamp: new Date().toISOString(),
        requestId,
      },
    });
  }
};

/**
 * Get current profile photo info
 * GET /api/member/profile/photo
 */
export const getProfilePhotoInfo = async (req: Request, res: Response): Promise<void> => {
  const requestId = (req as any).requestId || 'unknown';

  try {
    const cognitoUserId = (req as any).user?.sub || 'demo-member-001';

    logger.info('Get profile photo info request', {
      requestId,
      cognitoUserId,
    });

    const memberProfile = await ProfileService.getMemberProfile(cognitoUserId);
    if (!memberProfile) {
      res.status(404).json({
        success: false,
        error: {
          code: 'MEMBER_NOT_FOUND',
          message: 'Member profile not found',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        hasProfilePhoto: !!memberProfile.profilePictureUrl,
        profilePictureUrl: memberProfile.profilePictureUrl,
        memberInfo: {
          id: memberProfile.id,
          firstName: memberProfile.firstName,
          lastName: memberProfile.lastName,
          employerId: memberProfile.employerId,
        },
      },
    });

  } catch (error: any) {
    logger.error('Get profile photo info error', {
      requestId,
      error: error.message,
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to get profile photo information',
        timestamp: new Date().toISOString(),
        requestId,
      },
    });
  }
};

/**
 * Serve profile photo securely without exposing AWS credentials
 */
export const serveProfilePhoto = async (req: Request, res: Response): Promise<void> => {
  const requestId = req.requestId || 'unknown';
  const cognitoUserId = req.user?.sub;

  try {
    if (!cognitoUserId) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    logger.info('Serving profile photo', {
      requestId,
      cognitoUserId,
    });

    // Get member profile to retrieve S3 key
    const memberProfile = await ProfileService.getMemberProfile(cognitoUserId);
    if (!memberProfile) {
      res.status(404).json({
        success: false,
        error: {
          code: 'MEMBER_NOT_FOUND',
          message: 'Member profile not found',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Check if profile picture exists
    if (!memberProfile.profilePictureUrl) {
      res.status(404).json({
        success: false,
        error: {
          code: 'PHOTO_NOT_FOUND',
          message: 'No profile photo found',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Extract S3 key from stored URL (handle both S3 keys and full URLs)
    let s3Key = memberProfile.profilePictureUrl;
    if (s3Key.startsWith('http')) {
      // Extract key from full URL if needed
      const urlParts = s3Key.split('/');
      const keyIndex = urlParts.findIndex(part => part.includes('amazonaws.com')) + 1;
      if (keyIndex > 0 && keyIndex < urlParts.length) {
        s3Key = urlParts.slice(keyIndex).join('/').split('?')[0]; // Remove query params
      }
    }

    // Generate signed URL for internal use
    const signedUrl = await generateSignedUrl(s3Key);
    if (!signedUrl) {
      res.status(500).json({
        success: false,
        error: {
          code: 'PHOTO_ACCESS_ERROR',
          message: 'Unable to access profile photo',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Fetch the image from S3 and proxy it
    const fetch = (await import('node-fetch')).default;

    logger.info('Fetching image from S3', {
      requestId,
      cognitoUserId,
      s3Key,
      signedUrlLength: signedUrl.length,
    });

    const s3Response = await fetch(signedUrl);

    if (!s3Response.ok) {
      logger.error('Failed to fetch image from S3', {
        requestId,
        cognitoUserId,
        s3Key,
        status: s3Response.status,
      });

      res.status(404).json({
        success: false,
        error: {
          code: 'PHOTO_NOT_ACCESSIBLE',
          message: 'Profile photo not accessible',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Set appropriate headers
    const contentType = s3Response.headers.get('content-type') || 'image/jpeg';
    const contentLength = s3Response.headers.get('content-length');

    res.setHeader('Content-Type', contentType);
    if (contentLength) {
      res.setHeader('Content-Length', contentLength);
    }
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    res.setHeader('ETag', `"${s3Key}"`);

    // Stream the image data
    if (s3Response.body) {
      s3Response.body.pipe(res);
    } else {
      res.status(500).json({
        success: false,
        error: {
          code: 'PHOTO_STREAM_ERROR',
          message: 'Unable to stream profile photo',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
    }

    logger.info('Profile photo served successfully', {
      requestId,
      cognitoUserId,
      contentType,
      s3Key,
    });

  } catch (error) {
    logger.error('Profile photo serving failed', {
      requestId,
      cognitoUserId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to serve profile photo',
        timestamp: new Date().toISOString(),
        requestId,
      },
    });
  }
};

/**
 * Delete profile photo endpoint
 * DELETE /api/member/profile/photo
 */
export const deleteProfilePhoto = async (req: Request, res: Response): Promise<void> => {
  const requestId = (req as any).requestId || 'unknown';

  try {
    const cognitoUserId = (req as any).user?.sub || 'demo-member-001';

    logger.info('Profile photo delete request', {
      requestId,
      cognitoUserId,
    });

    // Get member profile to retrieve current photo info
    const memberProfile = await ProfileService.getMemberProfile(cognitoUserId);
    if (!memberProfile) {
      res.status(404).json({
        success: false,
        error: {
          code: 'MEMBER_NOT_FOUND',
          message: 'Member profile not found',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Check if profile photo exists
    if (!memberProfile.profilePictureUrl) {
      res.status(404).json({
        success: false,
        error: {
          code: 'PHOTO_NOT_FOUND',
          message: 'No profile photo to delete',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Extract S3 key from stored URL (handle both S3 keys and full URLs)
    let s3Key = memberProfile.profilePictureUrl;
    if (s3Key.startsWith('http')) {
      // Extract key from full URL if needed
      const urlParts = s3Key.split('/');
      const keyIndex = urlParts.findIndex(part => part.includes('amazonaws.com')) + 1;
      if (keyIndex > 0 && keyIndex < urlParts.length) {
        s3Key = urlParts.slice(keyIndex).join('/').split('?')[0]; // Remove query params
      }
    }

    // Delete from S3
    const deleteResult = await deleteFromS3(s3Key);
    if (!deleteResult.success) {
      logger.error('Failed to delete photo from S3', {
        requestId,
        cognitoUserId,
        s3Key,
        error: deleteResult.error,
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'S3_DELETE_FAILED',
          message: 'Failed to delete photo from storage',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
      return;
    }

    // Remove profile picture URL from database
    await ProfileService.updateMemberProfile(cognitoUserId, {
      profilePictureUrl: null,
    });

    logger.info('Profile photo deleted successfully', {
      requestId,
      cognitoUserId,
      memberId: memberProfile.id,
      deletedS3Key: s3Key,
    });

    res.status(200).json({
      success: true,
      data: {
        message: 'Profile photo deleted successfully',
        deletedS3Key: s3Key,
      },
    });

  } catch (error: any) {
    logger.error('Profile photo delete error', {
      requestId,
      error: error.message,
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to delete profile photo',
        timestamp: new Date().toISOString(),
        requestId,
      },
    });
  }
};
