-- =====================================================
-- COMMAND CENTER SCHEMA
-- =====================================================

-- System Administrators Table
CREATE TABLE command_center.system_administrators (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cognito_user_id VARCHAR(255) UNIQUE NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  role VARCHAR(50) NOT NULL CHECK (role IN ('super_admin', 'system_admin', 'support_admin', 'analytics_admin')),
  permissions JSONB DEFAULT '[]',
  department VARCHAR(100),
  phone VARCHAR(20),
  profile_picture_url VARCHAR(500),
  last_login_at TIMESTAMP,
  login_count INTEGER DEFAULT 0,
  failed_login_attempts INTEGER DEFAULT 0,
  account_locked_until TIMESTAMP,
  password_changed_at TIMESTAMP DEFAULT NOW(),
  two_factor_enabled BOOLEAN DEFAULT false,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'locked')),
  created_by UUID,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- User Onboarding Table
CREATE TABLE command_center.user_onboarding (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_type VARCHAR(50) NOT NULL CHECK (user_type IN ('employer', 'wellness_coach', 'lms_creator', 'system_admin')),
  email VARCHAR(255) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  organization_name VARCHAR(200),
  signup_link VARCHAR(500) UNIQUE,
  signup_token VARCHAR(255) UNIQUE,
  signup_link_expires_at TIMESTAMP DEFAULT (NOW() + INTERVAL '7 days'),
  signup_completed_at TIMESTAMP,
  created_by UUID REFERENCES command_center.system_administrators(id),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'completed', 'expired', 'cancelled')),
  metadata JSONB DEFAULT '{}',
  notes TEXT,
  reminder_sent_count INTEGER DEFAULT 0,
  last_reminder_sent_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- System Settings Table
CREATE TABLE command_center.system_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value JSONB NOT NULL,
  setting_type VARCHAR(50) NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json', 'array')),
  category VARCHAR(50) NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  is_encrypted BOOLEAN DEFAULT false,
  last_modified_by UUID REFERENCES command_center.system_administrators(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- System Analytics Table
CREATE TABLE command_center.system_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(15,4) NOT NULL,
  metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('counter', 'gauge', 'histogram', 'summary')),
  dimensions JSONB DEFAULT '{}',
  timestamp TIMESTAMP DEFAULT NOW(),
  service_name VARCHAR(50),
  created_at TIMESTAMP DEFAULT NOW()
);

-- System Notifications Table
CREATE TABLE command_center.system_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN ('system_alert', 'user_action', 'maintenance', 'security', 'performance')),
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  target_audience VARCHAR(50) DEFAULT 'admins' CHECK (target_audience IN ('all', 'admins', 'specific_users', 'service_users')),
  target_users JSONB DEFAULT '[]',
  read_by JSONB DEFAULT '[]',
  action_required BOOLEAN DEFAULT false,
  action_url VARCHAR(500),
  expires_at TIMESTAMP,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'read', 'dismissed', 'expired')),
  created_by UUID REFERENCES command_center.system_administrators(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Platform Usage Statistics Table
CREATE TABLE command_center.platform_usage_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date DATE NOT NULL,
  total_users INTEGER DEFAULT 0,
  active_users INTEGER DEFAULT 0,
  new_registrations INTEGER DEFAULT 0,
  total_employers INTEGER DEFAULT 0,
  total_members INTEGER DEFAULT 0,
  total_coaches INTEGER DEFAULT 0,
  total_sessions_completed INTEGER DEFAULT 0,
  total_learning_modules_completed INTEGER DEFAULT 0,
  total_wellness_programs_active INTEGER DEFAULT 0,
  revenue DECIMAL(12,2) DEFAULT 0,
  system_uptime_percentage DECIMAL(5,2) DEFAULT 100,
  avg_response_time_ms INTEGER DEFAULT 0,
  error_rate_percentage DECIMAL(5,2) DEFAULT 0,
  storage_used_gb DECIMAL(10,2) DEFAULT 0,
  bandwidth_used_gb DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- System Maintenance Logs Table
CREATE TABLE command_center.system_maintenance_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  maintenance_type VARCHAR(50) NOT NULL CHECK (maintenance_type IN ('scheduled', 'emergency', 'update', 'backup', 'security_patch')),
  title VARCHAR(200) NOT NULL,
  description TEXT,
  scheduled_start TIMESTAMP NOT NULL,
  scheduled_end TIMESTAMP NOT NULL,
  actual_start TIMESTAMP,
  actual_end TIMESTAMP,
  affected_services TEXT[] DEFAULT ARRAY[]::TEXT[],
  downtime_minutes INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled', 'failed')),
  performed_by UUID REFERENCES command_center.system_administrators(id),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR COMMAND CENTER
-- =====================================================

CREATE INDEX idx_admins_role ON command_center.system_administrators(role);
CREATE INDEX idx_admins_status ON command_center.system_administrators(status);
CREATE INDEX idx_admins_cognito_id ON command_center.system_administrators(cognito_user_id);
CREATE INDEX idx_admins_email ON command_center.system_administrators(email);

CREATE INDEX idx_onboarding_type ON command_center.user_onboarding(user_type);
CREATE INDEX idx_onboarding_status ON command_center.user_onboarding(status);
CREATE INDEX idx_onboarding_token ON command_center.user_onboarding(signup_token);
CREATE INDEX idx_onboarding_email ON command_center.user_onboarding(email);

CREATE INDEX idx_settings_key ON command_center.system_settings(setting_key);
CREATE INDEX idx_settings_category ON command_center.system_settings(category);
CREATE INDEX idx_settings_public ON command_center.system_settings(is_public);

CREATE INDEX idx_analytics_metric ON command_center.system_analytics(metric_name);
CREATE INDEX idx_analytics_timestamp ON command_center.system_analytics(timestamp);
CREATE INDEX idx_analytics_service ON command_center.system_analytics(service_name);
CREATE INDEX idx_analytics_type ON command_center.system_analytics(metric_type);

CREATE INDEX idx_notifications_type ON command_center.system_notifications(notification_type);
CREATE INDEX idx_notifications_severity ON command_center.system_notifications(severity);
CREATE INDEX idx_notifications_status ON command_center.system_notifications(status);
CREATE INDEX idx_notifications_target ON command_center.system_notifications(target_audience);

CREATE INDEX idx_usage_stats_date ON command_center.platform_usage_stats(date);

CREATE INDEX idx_maintenance_type ON command_center.system_maintenance_logs(maintenance_type);
CREATE INDEX idx_maintenance_status ON command_center.system_maintenance_logs(status);
CREATE INDEX idx_maintenance_scheduled ON command_center.system_maintenance_logs(scheduled_start, scheduled_end);

-- =====================================================
-- TRIGGERS FOR COMMAND CENTER
-- =====================================================

CREATE TRIGGER update_admins_updated_at 
  BEFORE UPDATE ON command_center.system_administrators 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settings_updated_at 
  BEFORE UPDATE ON command_center.system_settings 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_updated_at 
  BEFORE UPDATE ON command_center.system_maintenance_logs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
