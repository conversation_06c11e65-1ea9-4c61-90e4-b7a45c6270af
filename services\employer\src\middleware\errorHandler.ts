import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { ApiError, ErrorCode, HttpStatus } from '@aperion/shared';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, code: string = ErrorCode.INTERNAL_ERROR) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Error handler middleware
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
  let errorCode = ErrorCode.INTERNAL_ERROR;
  let message = 'An unexpected error occurred';

  // Handle custom AppError
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    errorCode = error.code as ErrorCode;
    message = error.message;
  }
  // Handle JWT errors
  else if (error.name === 'JsonWebTokenError') {
    statusCode = HttpStatus.UNAUTHORIZED;
    errorCode = ErrorCode.INVALID_TOKEN;
    message = 'Invalid token';
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = HttpStatus.UNAUTHORIZED;
    errorCode = ErrorCode.TOKEN_EXPIRED;
    message = 'Token has expired';
  }
  // Handle validation errors
  else if (error.name === 'ValidationError') {
    statusCode = HttpStatus.BAD_REQUEST;
    errorCode = ErrorCode.VALIDATION_ERROR;
    message = error.message;
  }

  // Create standardized error response
  const apiError: ApiError = {
    code: errorCode,
    message,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
    requestId: req.requestId || 'unknown',
  };

  // Log error details
  logger.error('Request error', {
    requestId: req.requestId,
    error: {
      name: error.name,
      message: error.message,
      statusCode,
      code: errorCode,
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userId: req.user?.sub,
    },
  });

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: apiError,
  });
};


