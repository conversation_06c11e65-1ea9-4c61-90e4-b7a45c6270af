import { Router } from 'express';
import { EmployeeController } from '../controllers/EmployeeController';

const router = Router();
const employeeController = new EmployeeController();

/**
 * @route POST /employees (via API Gateway: POST /api/employer/employees)
 * @desc Create a new employee
 * @access Employer only
 * @body {object} employeeData - Employee data
 * @body {string} employeeData.firstName - Employee's first name
 * @body {string} employeeData.lastName - Employee's last name
 * @body {string} employeeData.email - Employee's email address
 * @body {string} [employeeData.phoneNumber] - Employee's phone number
 * @body {string} employeeData.department - Employee's department
 * @body {string} employeeData.role - Employee's job title/role
 * @body {string} employeeData.healthPlan - Employee's health plan
 */
router.post('/', employeeController.createEmployeeCommandCenter);

/**
 * @route GET /employees (via API Gateway: GET /api/employer/employees)
 * @desc Get employees for the authenticated employer
 * @access Employer only
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 100)
 * @query {string} search - Search term for name or email
 * @query {string} department - Filter by department
 * @query {string} status - Filter by employment status
 * @query {string} sortBy - Sort field (default: created_at)
 * @query {string} sortOrder - Sort order: asc|desc (default: desc)
 */
router.get('/', employeeController.getEmployees);

/**
 * @route GET /employees/current (via API Gateway: GET /api/employer/employees/current)
 * @desc Get current (non-pending) employees for the authenticated employer
 * @access Employer only
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 100)
 * @query {string} search - Search term for name or email
 * @query {string} department - Filter by department
 * @query {string} status - Filter by employment status (excludes 'pending')
 * @query {string} sortBy - Sort field (default: created_at)
 * @query {string} sortOrder - Sort order: asc|desc (default: desc)
 */
router.get('/current', employeeController.getCurrentEmployees);

/**
 * @route GET /employees/pending (via API Gateway: GET /api/employer/employees/pending)
 * @desc Get pending employees for the authenticated employer
 * @access Employer only
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 100)
 * @query {string} search - Search term for name or email
 * @query {string} department - Filter by department
 * @query {string} sortBy - Sort field (default: created_at)
 * @query {string} sortOrder - Sort order: asc|desc (default: desc)
 */
router.get('/pending', employeeController.getPendingEmployees);

/**
 * @route DELETE /employees/pending/:id (via API Gateway: DELETE /api/employer/employees/pending/:id)
 * @desc Delete pending employee invitation
 * @access Employer only
 * @param {string} id - Employee ID to delete
 */
router.delete('/pending/:id', employeeController.deleteInvitation);

export { router as employeeRouter };
