/**
 * Test script to verify email integration functionality
 * This script tests the EmailService without actually sending emails
 */

import { EmailService } from '../services/EmailService';
import path from 'path';
import fs from 'fs/promises';

async function testEmailIntegration() {
  console.log('🧪 Testing Email Integration...\n');

  try {
    // Test 1: Check if email template exists
    console.log('1. Checking email template...');
    const templatePath = path.resolve(__dirname, '../../templates/employeeInvitationEmail.ejs');
    
    try {
      await fs.access(templatePath);
      console.log('✅ Email template found at:', templatePath);
    } catch (error) {
      console.log('❌ Email template not found at:', templatePath);
      return;
    }

    // Test 2: Check if template can be read
    console.log('\n2. Reading email template...');
    try {
      const template = await fs.readFile(templatePath, 'utf8');
      console.log('✅ Email template read successfully');
      console.log('📄 Template length:', template.length, 'characters');
    } catch (error) {
      console.log('❌ Failed to read email template:', error);
      return;
    }

    // Test 3: Test EmailService instantiation
    console.log('\n3. Testing EmailService instantiation...');
    try {
      new EmailService();
      console.log('✅ EmailService instantiated successfully');
    } catch (error) {
      console.log('❌ Failed to instantiate EmailService:', error);
      return;
    }

    // Test 4: Test environment variables
    console.log('\n4. Checking required environment variables...');
    const requiredEnvVars = [
      'FRONTEND_URL',
      'COMPANY_NAME',
      'ENCRYPTION_KEY',
      'MAILID',
      'AWS_REGION'
    ];

    let allEnvVarsPresent = true;
    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        console.log(`✅ ${envVar}: ${envVar === 'ENCRYPTION_KEY' ? '[HIDDEN]' : process.env[envVar]}`);
      } else {
        console.log(`❌ ${envVar}: Not set`);
        allEnvVarsPresent = false;
      }
    }

    if (!allEnvVarsPresent) {
      console.log('\n⚠️  Some required environment variables are missing');
    }

    // Test 5: Test email template rendering (without sending)
    console.log('\n5. Testing email template rendering...');
    try {
      const ejs = require('ejs');
      const template = await fs.readFile(templatePath, 'utf8');
      
      const testData = {
        firstName: 'John',
        lastName: 'Doe',
        confirmationLink: 'https://example.com/confirm?token=test123',
        companyName: process.env.COMPANY_NAME || 'Test Company'
      };

      const renderedHtml = ejs.render(template, testData);
      console.log('✅ Email template rendered successfully');
      console.log('📄 Rendered HTML length:', renderedHtml.length, 'characters');
      
      // Check if key elements are present in rendered HTML
      const hasWelcomeMessage = renderedHtml.includes('Welcome to');
      const hasConfirmationLink = renderedHtml.includes('confirm?token=test123');
      const hasEmployeeName = renderedHtml.includes('John Doe');
      
      console.log('📋 Template validation:');
      console.log(`   Welcome message: ${hasWelcomeMessage ? '✅' : '❌'}`);
      console.log(`   Confirmation link: ${hasConfirmationLink ? '✅' : '❌'}`);
      console.log(`   Employee name: ${hasEmployeeName ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log('❌ Failed to render email template:', error);
      return;
    }

    console.log('\n🎉 All email integration tests passed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Test the complete employee creation workflow');
    console.log('   2. Verify email confirmation endpoint');
    console.log('   3. Test with actual email sending (if SES is configured)');

  } catch (error) {
    console.error('❌ Email integration test failed:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEmailIntegration();
}

export { testEmailIntegration };
