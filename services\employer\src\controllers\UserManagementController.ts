import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import {
  CognitoIdentityProviderClient,
  SignUpCommand,
  AdminUpdateUserAttributesCommand,
  AdminConfirmSignUpCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import * as crypto from 'crypto';
import * as dotenv from 'dotenv';

dotenv.config();

// Initialize Cognito client
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

// Interface for employee registration request
interface EmployeeRegistrationRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  department: string;
  role: string;
  healthPlan: string;
}

// Interface for employee registration response
interface EmployeeRegistrationResponse {
  member_id: string;
  message: string;
}

/**
 * Generate a secure random password that meets Cognito requirements
 * Must contain: uppercase, lowercase, number, and symbol
 */
const generateSecurePassword = (): string => {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '**********';
  const symbols = '!@#$%^&*';

  // Ensure at least one character from each required category
  let password = '';
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];

  // Fill the rest with random characters from all categories
  const allChars = uppercase + lowercase + numbers + symbols;
  for (let i = 4; i < 12; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Shuffle the password to randomize the order
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

/**
 * Calculate secret hash for Cognito
 */
const calculateSecretHash = (username: string): string => {
  const clientSecret = process.env.CLIENT_SECRET;
  const clientId = process.env.CLIENT_ID;
  
  if (!clientSecret || !clientId) {
    throw new Error('CLIENT_SECRET and CLIENT_ID must be set in environment variables');
  }
  
  return crypto
    .createHmac('SHA256', clientSecret)
    .update(username + clientId)
    .digest('base64');
};

export class UserManagementController {
  /**
   * Register a new employee in Cognito
   * POST /api/user-management/employees
   */
  registerEmployee = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request body
      const {
        firstName,
        lastName,
        email,
        phoneNumber
        // Note: department, role, and healthPlan are received but not used in Cognito-only registration
      }: EmployeeRegistrationRequest = req.body;

      // Basic validation
      if (!firstName || !lastName || !email || !phoneNumber) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: 'firstName, lastName, email, and phoneNumber are required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_EMAIL_FORMAT',
            message: 'Invalid email format',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Validate phone number format (basic validation)
      const phoneRegex = /^\+?[1-9]\d{1,14}$/;
      if (!phoneRegex.test(phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PHONE_FORMAT',
            message: 'Invalid phone number format',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      logger.info('Registering new employee in Cognito:', {
        email,
        phoneNumber,
        firstName,
        lastName
      });

      // Use phoneNumber as username (following the established pattern)
      const userName = phoneNumber;
      const password = generateSecurePassword();
      const secretHash = calculateSecretHash(userName);

      // Create SignUp command with exactly 3 UserAttributes as specified
      const command = new SignUpCommand({
        ClientId: process.env.CLIENT_ID!,
        SecretHash: secretHash,
        Password: password,
        Username: userName,
        UserAttributes: [
          { Name: 'phone_number', Value: phoneNumber },
          { Name: 'email', Value: email },
          { Name: 'custom:roleId', Value: '3' }, // Employee role ID
        ],
      });

      // Execute Cognito SignUp
      const response = await cognitoClient.send(command);

      if (!response.UserSub) {
        throw new Error('Failed to get UserSub from Cognito response');
      }

      logger.info('Successfully registered employee in Cognito:', {
        email,
        phoneNumber,
        userSub: response.UserSub
      });

      // Update user attributes to mark email/phone as verified (following the pattern from other services)
      const updateAttributesCommand = new AdminUpdateUserAttributesCommand({
        UserPoolId: process.env.USERPOOLID!,
        Username: userName,
        UserAttributes: [
          { Name: 'email_verified', Value: 'true' },
          { Name: 'phone_number_verified', Value: 'true' },
        ],
      });

      await cognitoClient.send(updateAttributesCommand);

      logger.info('User attributes updated successfully:', {
        userSub: response.UserSub,
        username: userName
      });

      // Confirm the user account automatically (following the pattern from other services)
      const confirmCommand = new AdminConfirmSignUpCommand({
        UserPoolId: process.env.USERPOOLID!,
        Username: userName,
      });

      await cognitoClient.send(confirmCommand);

      logger.info('Employee account confirmed automatically:', {
        userSub: response.UserSub,
        username: userName,
        email,
        status: 'confirmed'
      });

      // Return success response with member_id as UserSub for referential integrity
      const successResponse: EmployeeRegistrationResponse = {
        member_id: response.UserSub,
        message: `Employee ${firstName} ${lastName} registered and confirmed successfully in Cognito`
      };

      res.status(201).json({
        success: true,
        data: successResponse,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      logger.error('Error registering employee:', {
        error: error.message,
        stack: error.stack,
        requestBody: req.body
      });

      // Handle specific Cognito errors
      let errorCode = 'REGISTRATION_FAILED';
      let errorMessage = 'Failed to register employee';
      let statusCode = 500;

      if (error.name === 'UsernameExistsException') {
        errorCode = 'USER_ALREADY_EXISTS';
        errorMessage = 'An employee with this phone number already exists';
        statusCode = 409;
      } else if (error.name === 'InvalidPasswordException') {
        errorCode = 'INVALID_PASSWORD';
        errorMessage = 'Generated password does not meet requirements';
        statusCode = 400;
      } else if (error.name === 'InvalidParameterException') {
        errorCode = 'INVALID_PARAMETERS';
        errorMessage = 'Invalid registration parameters provided';
        statusCode = 400;
      }

      res.status(statusCode).json({
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          timestamp: new Date().toISOString()
        }
      });
    }
  };
}
