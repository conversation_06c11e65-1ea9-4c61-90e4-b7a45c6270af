import { logger } from '../utils/logger';
import { DatabaseConnection, getServiceDatabaseConfig } from '@aperion/shared/server';

// Initialize secure database connection using shared utilities
const dbConnection = new DatabaseConnection(getServiceDatabaseConfig('member'));

// Create a pool-like interface for backward compatibility
const pool = {
  query: (text: string, params?: any[]) => dbConnection.query(text, params),
  connect: () => dbConnection.getClient(),
};

export interface MemberProfile {
  id: string;
  cognitoUserId: string;
  employerId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  profilePictureUrl?: string | null;
  emergencyContacts?: any[];
  healthPreferences?: any;
  privacySettings?: any;
  department?: string;
  healthPlanStatus?: string;
  status: string;
  profileCompletenessAchievedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Dependent {
  id: string;
  memberId: string;
  firstName: string;
  lastName: string;
  relationship: string;
  dateOfBirth: string;
  memberId_dependent?: string;
  gender?: string;
  status?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CreateDependentData {
  firstName: string;
  lastName: string;
  relationship: string;
  dateOfBirth: string;
  gender?: string;
}

export interface UpdateDependentData {
  firstName?: string;
  lastName?: string;
  relationship?: string;
  dateOfBirth?: string;
  gender?: string;
}

export class ProfileService {
  /**
   * Get complete member profile by cognito user ID
   */
  static async getMemberProfile(cognitoUserId: string): Promise<MemberProfile | null> {
    try {
      logger.info('Fetching member profile from database', { cognitoUserId });

      const query = `
        SELECT
          m.id,
          m.cognito_user_id,
          m.employer_id,
          m.first_name,
          m.last_name,
          m.email,
          m.phone,
          m.date_of_birth,
          m.gender,
          m.address,
          m.profile_picture_url,
          m.emergency_contacts,
          m.health_preferences,
          m.privacy_settings,
          m.status,
          m.created_at,
          m.updated_at
        FROM member_service.members m
        WHERE m.cognito_user_id = $1
      `;

      const result = await pool.query(query, [cognitoUserId]);

      if (!result.rows || result.rows.length === 0) {
        logger.warn('Member profile not found in database', { cognitoUserId });
        return null;
      }

      const row = result.rows[0];

      // Parse JSONB fields safely
      const address = row.address ? (typeof row.address === 'string' ? JSON.parse(row.address) : row.address) : null;
      const emergencyContacts = row.emergency_contacts ? (typeof row.emergency_contacts === 'string' ? JSON.parse(row.emergency_contacts) : row.emergency_contacts) : [];
      const healthPreferences = row.health_preferences ? (typeof row.health_preferences === 'string' ? JSON.parse(row.health_preferences) : row.health_preferences) : {};
      const privacySettings = row.privacy_settings ? (typeof row.privacy_settings === 'string' ? JSON.parse(row.privacy_settings) : row.privacy_settings) : {};

      // Normalize date format for consistent API response
      let normalizedDateOfBirth = row.date_of_birth;
      if (normalizedDateOfBirth) {
        try {
          // Ensure date is in yyyy-MM-dd format
          const date = new Date(normalizedDateOfBirth);
          if (!isNaN(date.getTime())) {
            normalizedDateOfBirth = date.toISOString().split('T')[0];
          }
        } catch (e) {
          logger.warn('Failed to normalize date format in profile retrieval', {
            cognitoUserId,
            originalDate: row.date_of_birth,
            error: e
          });
        }
      }

      // Keep the S3 key as stored in database - the frontend will use the proxy URL
      let profilePictureUrl = row.profile_picture_url;

      const profile: MemberProfile = {
        id: row.id,
        cognitoUserId: row.cognito_user_id,
        employerId: row.employer_id,
        firstName: row.first_name,
        lastName: row.last_name,
        email: row.email,
        phone: row.phone,
        dateOfBirth: normalizedDateOfBirth,
        gender: row.gender,
        address,
        profilePictureUrl,
        emergencyContacts,
        healthPreferences,
        privacySettings,
        department: 'Engineering', // TODO: Get from employer service
        healthPlanStatus: 'Active', // TODO: Get from health plan service
        status: row.status,
        profileCompletenessAchievedAt: null, // Default until migration is applied
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      };

      logger.info('Member profile retrieved successfully from database', {
        cognitoUserId,
        memberId: profile.id
      });

      return profile;
    } catch (error) {
      logger.error('Error fetching member profile from database', {
        cognitoUserId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get member dependents
   */
  static async getMemberDependents(memberId: string): Promise<Dependent[]> {
    try {
      logger.info('Fetching member dependents from database', { memberId });

      const query = `
        SELECT
          id,
          member_id,
          first_name,
          last_name,
          relationship,
          date_of_birth,
          member_id_dependent,
          gender,
          status,
          created_at,
          updated_at
        FROM member_service.member_dependents
        WHERE member_id = $1
        ORDER BY created_at ASC
      `;

      const result = await pool.query(query, [memberId]);

      const dependents: Dependent[] = result.rows?.map((row: any) => ({
        id: row.id,
        memberId: row.member_id,
        firstName: row.first_name,
        lastName: row.last_name,
        relationship: row.relationship,
        dateOfBirth: row.date_of_birth,
        memberId_dependent: row.member_id_dependent,
        gender: row.gender,
        status: row.status,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      })) || [];

      logger.info('Member dependents retrieved from database', {
        memberId,
        dependentCount: dependents.length
      });

      return dependents;
    } catch (error) {
      logger.error('Error fetching member dependents from database', {
        memberId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Create a new dependent for a member
   */
  static async createDependent(memberId: string, dependentData: CreateDependentData): Promise<Dependent> {
    try {
      logger.info('Creating new dependent', { memberId, dependentData: { ...dependentData, dateOfBirth: '[REDACTED]' } });

      // Generate a unique member ID for the dependent
      const memberIdDependent = `DEP${Date.now().toString().slice(-6)}`;

      const query = `
        INSERT INTO member_service.member_dependents (
          member_id,
          first_name,
          last_name,
          relationship,
          date_of_birth,
          member_id_dependent,
          gender,
          status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING
          id,
          member_id,
          first_name,
          last_name,
          relationship,
          date_of_birth,
          member_id_dependent,
          gender,
          status,
          created_at,
          updated_at
      `;

      const values = [
        memberId,
        dependentData.firstName,
        dependentData.lastName,
        dependentData.relationship,
        dependentData.dateOfBirth,
        memberIdDependent,
        dependentData.gender || null,
        'active'
      ];

      const result = await pool.query(query, values);
      const row = result.rows[0];

      const dependent: Dependent = {
        id: row.id,
        memberId: row.member_id,
        firstName: row.first_name,
        lastName: row.last_name,
        relationship: row.relationship,
        dateOfBirth: row.date_of_birth,
        memberId_dependent: row.member_id_dependent,
        gender: row.gender,
        status: row.status,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      };

      logger.info('Dependent created successfully', {
        memberId,
        dependentId: dependent.id,
        memberIdDependent: dependent.memberId_dependent
      });

      return dependent;
    } catch (error) {
      logger.error('Error creating dependent', {
        memberId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update an existing dependent
   */
  static async updateDependent(memberId: string, dependentId: string, updateData: UpdateDependentData): Promise<Dependent | null> {
    try {
      logger.info('Updating dependent', { memberId, dependentId, updateFields: Object.keys(updateData) });

      // Build dynamic update query
      const updateFields: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (updateData.firstName !== undefined) {
        updateFields.push(`first_name = $${paramIndex++}`);
        values.push(updateData.firstName);
      }
      if (updateData.lastName !== undefined) {
        updateFields.push(`last_name = $${paramIndex++}`);
        values.push(updateData.lastName);
      }
      if (updateData.relationship !== undefined) {
        updateFields.push(`relationship = $${paramIndex++}`);
        values.push(updateData.relationship);
      }
      if (updateData.dateOfBirth !== undefined) {
        updateFields.push(`date_of_birth = $${paramIndex++}`);
        values.push(updateData.dateOfBirth);
      }
      if (updateData.gender !== undefined) {
        updateFields.push(`gender = $${paramIndex++}`);
        values.push(updateData.gender);
      }

      if (updateFields.length === 0) {
        throw new Error('No valid fields provided for update');
      }

      // Add updated_at field
      updateFields.push(`updated_at = NOW()`);

      // Add WHERE conditions
      values.push(dependentId, memberId);

      const query = `
        UPDATE member_service.member_dependents
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex++} AND member_id = $${paramIndex++}
        RETURNING
          id,
          member_id,
          first_name,
          last_name,
          relationship,
          date_of_birth,
          member_id_dependent,
          gender,
          status,
          created_at,
          updated_at
      `;

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        logger.warn('Dependent not found for update', { memberId, dependentId });
        return null;
      }

      const row = result.rows[0];
      const dependent: Dependent = {
        id: row.id,
        memberId: row.member_id,
        firstName: row.first_name,
        lastName: row.last_name,
        relationship: row.relationship,
        dateOfBirth: row.date_of_birth,
        memberId_dependent: row.member_id_dependent,
        gender: row.gender,
        status: row.status,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      };

      logger.info('Dependent updated successfully', {
        memberId,
        dependentId,
        updatedFields: Object.keys(updateData)
      });

      return dependent;
    } catch (error) {
      logger.error('Error updating dependent', {
        memberId,
        dependentId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Delete a dependent
   */
  static async deleteDependent(memberId: string, dependentId: string): Promise<boolean> {
    try {
      logger.info('Deleting dependent', { memberId, dependentId });

      const query = `
        DELETE FROM member_service.member_dependents
        WHERE id = $1 AND member_id = $2
      `;

      const result = await pool.query(query, [dependentId, memberId]);

      if (result.rowCount === 0) {
        logger.warn('Dependent not found for deletion', { memberId, dependentId });
        return false;
      }

      logger.info('Dependent deleted successfully', { memberId, dependentId });
      return true;
    } catch (error) {
      logger.error('Error deleting dependent', {
        memberId,
        dependentId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }



  /**
   * Update member profile
   */
  static async updateMemberProfile(
    cognitoUserId: string,
    updateData: Partial<MemberProfile>
  ): Promise<MemberProfile> {
    try {
      logger.info('Updating member profile', { cognitoUserId, updateFields: Object.keys(updateData) });

      // Get current profile first
      const currentProfile = await this.getMemberProfile(cognitoUserId);
      if (!currentProfile) {
        throw new Error('Member not found');
      }

      // Build dynamic update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      // Handle basic fields
      if (updateData.firstName !== undefined) {
        updateFields.push(`first_name = $${paramIndex++}`);
        updateValues.push(updateData.firstName);
      }
      if (updateData.lastName !== undefined) {
        updateFields.push(`last_name = $${paramIndex++}`);
        updateValues.push(updateData.lastName);
      }
      if (updateData.phone !== undefined) {
        updateFields.push(`phone = $${paramIndex++}`);
        updateValues.push(updateData.phone);
      }
      if (updateData.dateOfBirth !== undefined) {
        updateFields.push(`date_of_birth = $${paramIndex++}`);

        // Normalize date format for PostgreSQL DATE column
        let normalizedDate = updateData.dateOfBirth;
        if (normalizedDate) {
          try {
            const date = new Date(normalizedDate);
            if (!isNaN(date.getTime())) {
              // Convert to yyyy-MM-dd format for PostgreSQL DATE column
              normalizedDate = date.toISOString().split('T')[0];
            }
          } catch (e) {
            logger.warn('Failed to normalize date format', {
              cognitoUserId,
              originalDate: updateData.dateOfBirth,
              error: e
            });
          }
        }

        updateValues.push(normalizedDate);
      }
      if (updateData.gender !== undefined) {
        updateFields.push(`gender = $${paramIndex++}`);
        updateValues.push(updateData.gender);
      }
      if (updateData.profilePictureUrl !== undefined) {
        updateFields.push(`profile_picture_url = $${paramIndex++}`);
        updateValues.push(updateData.profilePictureUrl);
      }

      // Handle JSON fields
      if (updateData.address !== undefined) {
        updateFields.push(`address = $${paramIndex++}`);
        updateValues.push(JSON.stringify(updateData.address));
      }
      if (updateData.emergencyContacts !== undefined) {
        updateFields.push(`emergency_contacts = $${paramIndex++}`);
        updateValues.push(JSON.stringify(updateData.emergencyContacts));
      }
      if (updateData.healthPreferences !== undefined) {
        updateFields.push(`health_preferences = $${paramIndex++}`);
        updateValues.push(JSON.stringify(updateData.healthPreferences));
      }
      if (updateData.privacySettings !== undefined) {
        updateFields.push(`privacy_settings = $${paramIndex++}`);
        updateValues.push(JSON.stringify(updateData.privacySettings));
      }

      // Always update the updated_at timestamp
      updateFields.push(`updated_at = $${paramIndex++}`);
      updateValues.push(new Date());

      // Add cognito_user_id for WHERE clause
      updateValues.push(cognitoUserId);

      if (updateFields.length === 1) { // Only updated_at was added
        logger.info('No fields to update, returning current profile', { cognitoUserId });
        return currentProfile;
      }

      const query = `
        UPDATE member_service.members
        SET ${updateFields.join(', ')}
        WHERE cognito_user_id = $${paramIndex}
        RETURNING *
      `;

      logger.info('Executing profile update query', {
        cognitoUserId,
        updateFields: updateFields.slice(0, -1), // Exclude updated_at from log
        query: query.replace(/\$\d+/g, '?') // Replace params for logging
      });

      // Execute the actual database update
      const result = await pool.query(query, updateValues);

      if (result.rows.length === 0) {
        throw new Error('Failed to update member profile - no rows affected');
      }

      const updatedRow = result.rows[0];

      // Parse JSON fields if they exist
      let address = null;
      let emergencyContacts = null;
      let healthPreferences = null;
      let privacySettings = null;

      try {
        address = updatedRow.address ? (typeof updatedRow.address === 'string' ? JSON.parse(updatedRow.address) : updatedRow.address) : null;
      } catch (e) {
        logger.warn('Failed to parse address JSON', { cognitoUserId, error: e });
      }

      try {
        emergencyContacts = updatedRow.emergency_contacts ? (typeof updatedRow.emergency_contacts === 'string' ? JSON.parse(updatedRow.emergency_contacts) : updatedRow.emergency_contacts) : null;
      } catch (e) {
        logger.warn('Failed to parse emergency contacts JSON', { cognitoUserId, error: e });
      }

      try {
        healthPreferences = updatedRow.health_preferences ? (typeof updatedRow.health_preferences === 'string' ? JSON.parse(updatedRow.health_preferences) : updatedRow.health_preferences) : null;
      } catch (e) {
        logger.warn('Failed to parse health preferences JSON', { cognitoUserId, error: e });
      }

      try {
        privacySettings = updatedRow.privacy_settings ? (typeof updatedRow.privacy_settings === 'string' ? JSON.parse(updatedRow.privacy_settings) : updatedRow.privacy_settings) : null;
      } catch (e) {
        logger.warn('Failed to parse privacy settings JSON', { cognitoUserId, error: e });
      }

      // Normalize date format for consistent API response
      let normalizedUpdatedDateOfBirth = updatedRow.date_of_birth;
      if (normalizedUpdatedDateOfBirth) {
        try {
          // Ensure date is in yyyy-MM-dd format
          const date = new Date(normalizedUpdatedDateOfBirth);
          if (!isNaN(date.getTime())) {
            normalizedUpdatedDateOfBirth = date.toISOString().split('T')[0];
          }
        } catch (e) {
          logger.warn('Failed to normalize date format in profile update response', {
            cognitoUserId,
            originalDate: updatedRow.date_of_birth,
            error: e
          });
        }
      }

      // Convert database row to MemberProfile format
      const updatedProfile: MemberProfile = {
        id: updatedRow.id,
        cognitoUserId: updatedRow.cognito_user_id,
        employerId: updatedRow.employer_id,
        firstName: updatedRow.first_name,
        lastName: updatedRow.last_name,
        email: updatedRow.email,
        phone: updatedRow.phone,
        dateOfBirth: normalizedUpdatedDateOfBirth,
        gender: updatedRow.gender,
        address,
        profilePictureUrl: updatedRow.profile_picture_url,
        emergencyContacts,
        healthPreferences,
        privacySettings,
        department: 'Engineering', // TODO: Get from employer service
        healthPlanStatus: 'Active', // TODO: Get from health plan service
        status: updatedRow.status,
        profileCompletenessAchievedAt: null, // Default until migration is applied
        createdAt: updatedRow.created_at,
        updatedAt: updatedRow.updated_at,
      };

      // Check if profile completeness reached 100% for the first time
      // Note: This will be implemented when database migration is applied
      // await ProfileService.checkAndUpdateCompletenessAchievement(updatedProfile);

      logger.info('Member profile updated successfully in database', {
        cognitoUserId,
        memberId: updatedProfile.id,
        updatedFields: Object.keys(updateData)
      });

      return updatedProfile;

    } catch (error) {
      logger.error('Error updating member profile', {
        cognitoUserId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Check if profile completeness reached 100% for the first time and update achievement timestamp
   */
  static async checkAndUpdateCompletenessAchievement(profile: MemberProfile): Promise<void> {
    try {
      // Skip if already achieved
      if (profile.profileCompletenessAchievedAt) {
        return;
      }

      // Calculate current completeness
      const completeness = ProfileService.calculateProfileCompleteness(profile);

      if (completeness >= 100) {
        logger.info('Profile completeness reached 100% for the first time', {
          cognitoUserId: profile.cognitoUserId,
          memberId: profile.id,
          completeness
        });

        // Update the achievement timestamp
        const query = `
          UPDATE member_service.members
          SET profile_completeness_achieved_at = NOW()
          WHERE cognito_user_id = $1 AND profile_completeness_achieved_at IS NULL
        `;

        await pool.query(query, [profile.cognitoUserId]);

        logger.info('Profile completeness achievement timestamp updated', {
          cognitoUserId: profile.cognitoUserId,
          memberId: profile.id
        });
      }
    } catch (error) {
      logger.error('Error checking profile completeness achievement', {
        cognitoUserId: profile.cognitoUserId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Don't throw error as this is not critical for the main operation
    }
  }

  /**
   * Calculate profile completeness percentage (static method for reuse)
   * Only includes fields that are shown in the frontend profile form
   */
  static calculateProfileCompleteness(profile: MemberProfile): number {
    // Only include fields that are displayed in the frontend form
    // Excludes emergency contacts as they are not shown in the profile form
    const requiredFields = [
      'firstName',      // Personal Information
      'lastName',       // Personal Information
      'email',          // Personal Information (read-only)
      'phone',          // Personal Information
      'dateOfBirth',    // Personal Information
      'gender',         // Personal Information
      'address',        // Address Information (all sub-fields)
      'profilePictureUrl' // Profile Photo
    ];

    let completedFields = 0;

    for (const field of requiredFields) {
      const value = profile[field as keyof MemberProfile];

      if (field === 'address') {
        // For address, check all required sub-fields that are in the frontend
        const address = value as any;
        if (address &&
            address.street && address.street.trim() !== '' &&
            address.city && address.city.trim() !== '' &&
            address.state && address.state.trim() !== '' &&
            address.zipCode && address.zipCode.trim() !== '' &&
            address.country && address.country.trim() !== '') {
          completedFields++;
        }
      } else {
        // For other fields, check if they have valid values
        if (value &&
            (typeof value === 'string' ? value.trim() !== '' :
             Array.isArray(value) ? value.length > 0 :
             typeof value === 'object' ? Object.keys(value).length > 0 : true)) {
          completedFields++;
        }
      }
    }

    return Math.round((completedFields / requiredFields.length) * 100);
  }
}

export default ProfileService;
