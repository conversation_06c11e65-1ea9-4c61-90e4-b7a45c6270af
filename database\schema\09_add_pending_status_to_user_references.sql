-- =====================================================
-- ADD PENDING STATUS TO USER REFERENCES TABLE
-- Migration to add 'pending' status to shared_data.user_references status constraint
-- =====================================================

-- Drop the existing check constraint
ALTER TABLE shared_data.user_references 
DROP CONSTRAINT IF EXISTS user_references_status_check;

-- Add the new check constraint with 'pending' status included
ALTER TABLE shared_data.user_references 
ADD CONSTRAINT user_references_status_check 
CHECK (status IN ('active', 'inactive', 'suspended', 'deleted', 'pending'));

-- Add comment for documentation
COMMENT ON CONSTRAINT user_references_status_check ON shared_data.user_references 
IS 'Allowed status values: active, inactive, suspended, deleted, pending. Pending status is used for newly created employees who have not completed registration.';
