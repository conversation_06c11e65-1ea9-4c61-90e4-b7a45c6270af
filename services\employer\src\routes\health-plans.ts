import { Router } from 'express';
import { EmployeeController } from '../controllers/EmployeeController';

const router = Router();
const employeeController = new EmployeeController();

/**
 * @route GET /health-plans (via API Gateway: GET /api/employer/health-plans)
 * @desc Get available health plans
 * @access Employer only
 */
router.get('/', employeeController.getHealthPlans);

export { router as healthPlanRouter };
