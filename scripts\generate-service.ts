#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';

interface ServiceConfig {
  name: string;
  port: number;
}

interface PackageJson {
  name: string;
  version: string;
  description: string;
  main: string;
  scripts: Record<string, string>;
  dependencies: Record<string, string>;
  devDependencies: Record<string, string>;
  keywords: string[];
  author: string;
  license: string;
}

interface TsConfig {
  extends: string;
  compilerOptions: {
    outDir: string;
    rootDir?: string;
  };
  include: string[];
  exclude: string[];
}

function generateService(serviceName: string, port: number): void {
  const serviceDir = path.join('services', serviceName);
  
  console.log(`🚀 Generating service: ${serviceName} on port ${port}`);
  
  // Create service directory structure
  const directories = [
    'src',
    'src/routes',
    'src/middleware',
    'src/models',
    'src/services',
    'src/repositories',
    'src/utils',
    'src/types',
    'tests',
    'tests/unit',
    'tests/integration'
  ];

  directories.forEach(dir => {
    const fullPath = path.join(serviceDir, dir);
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`   📁 Created directory: ${dir}`);
  });

  // Generate package.json
  const packageJson: PackageJson = {
    name: `@aperion/${serviceName}`,
    version: "1.0.0",
    description: `${serviceName.charAt(0).toUpperCase() + serviceName.slice(1)} service for Aperion Health`,
    main: "dist/index.js",
    scripts: {
      dev: "nodemon src/index.ts",
      build: "tsc",
      start: "node dist/index.js",
      test: "jest",
      "test:watch": "jest --watch",
      "test:coverage": "jest --coverage",
      lint: "eslint src --ext .ts",
      "lint:fix": "eslint src --ext .ts --fix",
      migrate: "tsx scripts/migrate.ts",
      seed: "tsx scripts/seed.ts",
      clean: "rimraf dist"
    },
    dependencies: {
      express: "^4.18.2",
      cors: "^2.8.5",
      helmet: "^7.1.0",
      "express-rate-limit": "^7.1.5",
      "express-validator": "^7.0.1",
      dotenv: "^16.3.1",
      winston: "^3.11.0",
      pg: "^8.11.3",
      "drizzle-orm": "^0.29.1",
      zod: "^3.22.4",
      jsonwebtoken: "^9.0.2",
      bcrypt: "^5.1.1",
      uuid: "^9.0.1",
      "@aperion/shared": "workspace:*",
      "@aperion/database": "workspace:*"
    },
    devDependencies: {
      "@types/express": "^4.17.21",
      "@types/cors": "^2.8.17",
      "@types/node": "^20.10.5",
      "@types/pg": "^8.10.9",
      "@types/jsonwebtoken": "^9.0.5",
      "@types/bcrypt": "^5.0.2",
      "@types/uuid": "^9.0.7",
      typescript: "^5.3.3",
      nodemon: "^3.0.2",
      rimraf: "^5.0.5",
      tsx: "^4.6.2"
    },
    keywords: ["aperion", "health", "microservice", serviceName],
    author: "Aperion Health Team",
    license: "UNLICENSED"
  };

  fs.writeFileSync(
    path.join(serviceDir, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  );
  console.log(`   📄 Created package.json`);

  // Generate TypeScript config
  const tsConfig: TsConfig = {
    extends: "../../tsconfig.json",
    compilerOptions: {
      outDir: "./dist",
      rootDir: "./src"
    },
    include: ["src/**/*"],
    exclude: ["dist", "node_modules", "**/*.test.ts"]
  };

  fs.writeFileSync(
    path.join(serviceDir, 'tsconfig.json'),
    JSON.stringify(tsConfig, null, 2)
  );
  console.log(`   📄 Created tsconfig.json`);

  // Note: Environment variables are configured in the root .env file
  console.log(`   📝 Environment variables should be configured in root .env file`);
}

function getSchemaName(serviceName: string): string {
  const schemaMap: Record<string, string> = {
    'member': 'member_service',
    'employer': 'employer_service',
    'wellness-central': 'wellness_service',
    'zenx-lms': 'lms_service',
    'command-center': 'command_center'
  };
  return schemaMap[serviceName] || `${serviceName}_service`;
}

// Generate all services
const services: ServiceConfig[] = [
  { name: 'member', port: 3001 },
  { name: 'employer', port: 3002 },
  { name: 'wellness-central', port: 3003 },
  { name: 'zenx-lms', port: 3004 },
  { name: 'command-center', port: 3005 }
];

function generateAllServices(): void {
  console.log('🏗️  Generating all Aperion Health services...\n');
  
  services.forEach(service => {
    generateService(service.name, service.port);
    console.log(''); // Add spacing between services
  });

  console.log('🎉 All services generated successfully!');
  console.log('\n📋 To start all services:');
  console.log('   npm run dev');
}

// Main execution
function main(): void {
  const args = process.argv.slice(2);
  const command = args[0];
  const serviceName = args[1];
  const port = args[2] ? parseInt(args[2], 10) : undefined;

  if (command === 'generate' && serviceName) {
    if (!port) {
      console.error('❌ Port number is required');
      console.log('Usage: tsx scripts/generate-service.ts generate <service-name> <port>');
      process.exit(1);
    }
    generateService(serviceName, port);
  } else if (command === 'all') {
    generateAllServices();
  } else {
    console.log('Aperion Health Service Generator');
    console.log('');
    console.log('Usage:');
    console.log('  tsx scripts/generate-service.ts generate <service-name> <port>  # Generate single service');
    console.log('  tsx scripts/generate-service.ts all                            # Generate all services');
    console.log('');
    console.log('Examples:');
    console.log('  tsx scripts/generate-service.ts generate member 3001');
    console.log('  tsx scripts/generate-service.ts all');
  }
}

if (require.main === module) {
  main();
}

export { generateService, generateAllServices };
