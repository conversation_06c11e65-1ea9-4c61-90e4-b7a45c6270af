# Aperion Health - Microservices Platform

A comprehensive healthcare platform built with microservices architecture, featuring member management, employer services, wellness coaching, learning management, and administrative tools.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │────│  Load Balancer  │────│   Auth Service  │
│   (Port 3000)   │    │                 │    │   (Cognito)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ├─── Member Service (Port 3001)
         ├─── Employer Service (Port 3002)
         ├─── Wellness Central Service (Port 3003)
         ├─── ZenX LMS Service (Port 3004)
         └─── Command Center Service (Port 3005)
```

## 🚀 Quick Start

### Prerequisites

- **Node.js** v18.0.0 or higher
- **PostgreSQL** v14.0 or higher
- **npm** v8.0.0 or higher
- **Git** latest version

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aperion-health
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up databases**
   ```bash
   npm run db:setup
   ```

5. **Generate services**
   ```bash
   npm run generate:service
   ```

6. **Start development environment**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
aperion-health/
├── packages/
│   ├── api-gateway/          # API Gateway (Port 3000)
│   ├── shared/               # Shared utilities and types
│   ├── auth/                 # Authentication service
│   └── database/             # Database schemas and migrations
├── services/
│   ├── member/               # Member Service (Port 3001)
│   ├── employer/             # Employer Service (Port 3002)
│   ├── wellness-central/     # Wellness Central Service (Port 3003)
│   ├── zenx-lms/            # ZenX LMS Service (Port 3004)
│   └── command-center/       # Command Center Service (Port 3005)
├── apps/
│   └── aperion-portal/       # Unified Frontend Portal (Port 4000)
│       ├── src/
│       │   ├── pages/
│       │   │   ├── auth/     # Authentication pages
│       │   │   ├── member/   # Member-specific pages
│       │   │   ├── employer/ # Employer-specific pages
│       │   │   ├── wellness/ # Wellness coach pages
│       │   │   ├── lms/      # LMS content creator pages
│       │   │   └── admin/    # Admin pages
│       │   ├── layouts/      # Role-based layouts
│       │   ├── components/   # Shared UI components
│       │   └── contexts/     # React contexts (Auth, etc.)
├── docs/                     # Documentation
├── scripts/                  # Development scripts
└── database/                 # Database setup and migrations
```

## 🎯 Services Overview

### 1. Member Service (Port 3001)
- **Purpose**: Individual member profile and health data management
- **Key Features**: Profile management, health metrics, wellness goals
- **Users**: Members (employees)

### 2. Employer Service (Port 3002)
- **Purpose**: Company and employee management
- **Key Features**: Employee onboarding, activation codes, company settings
- **Users**: Employers, HR Managers

### 3. Wellness Central (Port 3003)
- **Purpose**: Wellness coaching and health programs
- **Key Features**: Coach assignments, session scheduling, wellness programs
- **Users**: Wellness Coaches, Wellness Coordinators

### 4. ZenX LMS (Port 3004)
- **Purpose**: Learning management and content delivery
- **Key Features**: Course creation, learning paths, assessments
- **Users**: Learners, Content Creators, LMS Admins

### 5. Command Center (Port 3005)
- **Purpose**: System administration and analytics
- **Key Features**: User management, system monitoring, analytics
- **Users**: System Admins, Operations Managers

## 🎨 Frontend Architecture

### Single Portal with Route-Based Segregation
- **Unified Application**: One React application (`aperion-portal`) serving all user roles
- **Role-Based Routing**: Different routes and layouts based on user role
- **Shared Components**: Reusable UI components across all sections
- **Centralized Authentication**: Single sign-on experience for all users

### Route Structure
```
Authentication Routes:
├── /auth/step1                 # Login page
├── /auth/signup                # Registration
└── /auth/activate              # Account activation

Role-Based Routes:
├── /member/*                   # Member dashboard & profile
├── /employer/*                 # Employee management & onboarding
├── /wellness/*                 # Member coaching & tracking
├── /lms/*                      # Learning modules & content
└── /admin/*                    # User management & system admin
```

### Benefits of Single Portal
- **Unified User Experience**: Consistent UI/UX across all services
- **Shared Authentication State**: Single login for all features
- **Simplified Deployment**: One frontend application to deploy
- **Better SEO**: Unified domain and routing structure
- **Easier Maintenance**: Single codebase for frontend

## 🔐 Authentication & Authorization

### Passkey Authentication
- **Primary Method**: WebAuthn/FIDO2 passkeys via AWS Cognito
- **Fallback**: Traditional email/password authentication
- **Multi-Factor**: Optional MFA for enhanced security

### User Roles
- **Member**: Individual employees with health profile access
- **Employer**: Company administrators managing employees
- **HR Manager**: Limited employee management access
- **Wellness Coach**: Health coaching and member support
- **Wellness Coordinator**: Senior wellness management
- **Learner**: Access to learning content and courses
- **Content Creator**: LMS content creation and management
- **LMS Admin**: Full LMS administration
- **System Admin**: Platform-wide administration
- **Operations Manager**: Analytics and operations oversight

## 🗄️ Database Architecture

### Single Database with Schema Separation
- **Database**: `AperionHealth-Dev` (PostgreSQL)
- **Host**: `**************:5432`
- **Schemas**:
  - `shared_data` - Cross-service references and shared data
  - `member_service` - Member service data
  - `employer_service` - Employer service data
  - `wellness_service` - Wellness service data
  - `lms_service` - LMS service data
  - `command_center` - Command center data

### Benefits of Single Database Approach
- **Simplified Operations**: Single database instance to manage
- **ACID Transactions**: Cross-service transactions when needed
- **Cost Efficiency**: Reduced infrastructure costs
- **Better Performance**: Optimized connection pooling
- **Easier Backup/Recovery**: Unified backup strategy

## 🔄 Inter-Service Communication

### Synchronous Communication
- Direct HTTP calls via API Gateway
- Service-to-service authentication with JWT
- Circuit breaker pattern for resilience

### Asynchronous Communication
- Event-driven architecture
- Message queues for reliability
- Eventual consistency model

## 📋 Development Commands

### Project Management
```bash
npm run dev                    # Start all services in development
npm run build                  # Build all services
npm run test                   # Run all tests
npm run lint                   # Lint all code
npm run format                 # Format all code
```

### Database Management
```bash
npm run db:setup               # Set up databases
npm run db:migrate             # Run migrations
npm run db:seed                # Seed test data
```

### Service Management
```bash
npm run generate:service       # Generate all services
npm run validate:env           # Validate environment variables
```

### Individual Services
```bash
npm run dev:gateway            # Start API Gateway only
npm run dev:services           # Start all services only
```

## 🧪 Testing Strategy

### Test Types
- **Unit Tests**: Individual function testing (70%)
- **Integration Tests**: Service communication testing (20%)
- **End-to-End Tests**: Complete workflow testing (10%)

### Running Tests
```bash
npm run test                   # All tests
npm run test:unit              # Unit tests only
npm run test:integration       # Integration tests only
npm run test:e2e               # End-to-end tests only
npm run test:coverage          # Coverage report
```

## 🚀 Deployment

### Development
```bash
npm run dev                    # Local development
```

### Production
```bash
npm run build                  # Build all services
npm run start:all              # Start all services
```

### Docker
```bash
npm run docker:build          # Build Docker images
npm run docker:up              # Start with Docker Compose
npm run docker:down            # Stop Docker services
```

## 📊 Monitoring & Health Checks

### Application URLs
- **Frontend Portal**: `http://localhost:4000` (Aperion Portal)
- **API Gateway**: `http://localhost:3000` (Main API entry point)

### Health Check Endpoints
- API Gateway: `http://localhost:3000/health`
- Member Service: `http://localhost:3001/health`
- Employer Service: `http://localhost:3002/health`
- Wellness Central: `http://localhost:3003/health`
- ZenX LMS: `http://localhost:3004/health`
- Command Center: `http://localhost:3005/health`

### Metrics
- Request/response times
- Error rates
- Service availability
- Database performance

## 🔧 Configuration

### Environment Variables
See `.env.example` for all configuration options.

Key variables:
- `JWT_SECRET` - JWT signing secret
- `SERVICE_JWT_SECRET` - Service-to-service JWT secret
- `COGNITO_USER_POOL_ID` - AWS Cognito User Pool ID
- `DB_HOST` - Database host
- Service-specific ports and database credentials

## 📚 Documentation

Detailed documentation is available in the `docs/` directory:

- [Development Setup](docs/development-setup.md)
- [API Gateway](docs/api-gateway.md)
- [Authentication Flow](docs/authentication-flow.md)
- [Database Schema](docs/database-schema.md)
- [Inter-Service Communication](docs/inter-service-communication.md)
- [Testing Strategy](docs/testing-strategy.md)
- Individual service documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation in `docs/`
- Review the troubleshooting section
- Create an issue in the repository

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000-3005 are available
2. **Database connection**: Verify PostgreSQL is running
3. **Environment variables**: Run `npm run validate:env`
4. **Service startup**: Check individual service logs

### Debug Commands
```bash
npm run validate:env           # Check environment configuration
npm run db:setup               # Verify database setup
node scripts/setup-databases.js --create-script  # Generate SQL script
```
