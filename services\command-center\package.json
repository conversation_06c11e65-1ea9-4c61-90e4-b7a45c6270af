{"name": "@aperion/command-center", "version": "1.0.0", "description": "Command-center service for Aperion Health", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@aperion/shared": "^1.0.0", "@aws-sdk/client-cognito-identity-provider": "^3.823.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "drizzle-orm": "^0.28.6", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-validator": "^3.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.7", "@types/uuid": "^9.0.7", "drizzle-kit": "^0.19.13", "nodemon": "^3.0.2", "rimraf": "^5.0.5", "ts-node": "^10.9.1", "typescript": "^5.1.0"}}