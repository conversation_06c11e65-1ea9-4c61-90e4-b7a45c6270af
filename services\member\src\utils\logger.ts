import { LoggerFactory, LoggingUtils, LogContext, ContextualLogger } from '@aperion/shared/server';

// Logger configuration for member service
const loggerConfig = {
  serviceName: 'member' as const,
  logLevel: (process.env.LOG_LEVEL as any) || 'info',
  enableConsole: true,
  enableFileLogging: true,
  enablePrettyPrint: process.env.NODE_ENV !== 'production',
  enableLogRotation: true,
  maxFileSize: '10M',
  maxFiles: 7,
  enableDebugFile: process.env.NODE_ENV !== 'production',
};

// Create application logger (for non-HTTP application events)
export const logger = LoggerFactory.createLogger(loggerConfig) as any;

// Create HTTP logger middleware (uses dedicated access logger)
export const httpLogger = LoggerFactory.createHttpLogger(logger, loggerConfig);

// Utility functions for request/correlation IDs
export const generateRequestId = LoggerFactory.generateRequestId;
export const generateCorrelationId = LoggerFactory.generateCorrelationId;

/**
 * Create a contextual logger for specific operations
 * This ensures consistent fields across all log entries for an operation
 */
export function createOperationLogger(
  operationName: string,
  context: Partial<LogContext> = {}
): ContextualLogger {
  const operationContext: LogContext = {
    operation: operationName,
    operationId: `op_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    component: 'member-service',
    ...context,
  };

  return (logger as any).withContext(operationContext);
}

/**
 * Create a request-scoped logger with consistent request context
 */
export function createRequestLogger(
  requestId: string,
  correlationId?: string,
  additionalContext: Partial<LogContext> = {}
): ContextualLogger {
  const requestContext: LogContext = {
    requestId,
    correlationId: correlationId || generateCorrelationId(),
    component: 'member-service',
    ...additionalContext,
  };

  return (logger as any).withContext(requestContext);
}

/**
 * Create a user-scoped logger with consistent user context
 */
export function createUserLogger(
  userId: string,
  additionalContext: Partial<LogContext> = {}
): ContextualLogger {
  const userContext: LogContext = {
    userId,
    component: 'member-service',
    ...additionalContext,
  };

  return (logger as any).withContext(userContext);
}

/**
 * Create a database operation logger with enhanced DB context
 */
export function createDatabaseLogger(
  operation: string,
  table?: string,
  schema?: string,
  additionalContext: Partial<LogContext> = {}
): ContextualLogger {
  const dbContext = LoggingUtils.createDatabaseContext(operation, table, schema);
  const enhancedContext: LogContext = {
    ...dbContext,
    component: 'database',
    ...additionalContext,
  };

  return (logger as any).withContext(enhancedContext);
}

/**
 * Create enhanced error logger with comprehensive error context
 */
export function createErrorLogger(
  error: Error,
  context: Partial<LogContext> = {}
): { logger: ContextualLogger; errorContext: any } {
  const errorContext = LoggingUtils.enhanceError(error, {
    component: 'member-service',
    ...context,
  });

  return {
    logger: (logger as any).withContext(errorContext),
    errorContext,
  };
}

/**
 * Create performance tracking logger
 */
export function createPerformanceLogger(
  operation: string,
  context: Partial<LogContext> = {}
): { logger: ContextualLogger; startTracking: () => any; endTracking: (startContext: any) => any } {
  const performanceContext = LoggingUtils.createPerformanceContext();
  const enhancedContext: LogContext = {
    operation,
    component: 'member-service',
    ...context,
  };

  return {
    logger: (logger as any).withContext(enhancedContext),
    startTracking: () => performanceContext,
    endTracking: (startContext: any) => LoggingUtils.calculatePerformanceDelta(startContext),
  };
}

/**
 * Create business operation logger with enhanced context
 */
export function createBusinessLogger(
  operation: string,
  step?: string,
  additionalContext: Partial<LogContext> = {}
): ContextualLogger {
  const businessContext = LoggingUtils.createBusinessContext(operation, step, 'member-service');
  const enhancedContext: LogContext = {
    ...businessContext,
    ...additionalContext,
  };

  return (logger as any).withContext(enhancedContext);
}

/**
 * Create security audit logger
 */
export function createSecurityLogger(
  action: string,
  resource?: string,
  resourceId?: string,
  additionalContext: Partial<LogContext> = {}
): ContextualLogger {
  const securityContext = LoggingUtils.createSecurityContext(action, resource, resourceId);
  const enhancedContext: LogContext = {
    ...securityContext,
    component: 'member-service',
    ...additionalContext,
  };

  return (logger as any).withContext(enhancedContext);
}

// Legacy compatibility - create a stream object for any existing Morgan usage
export const loggerStream = {
  write: (message: string) => {
    logger.info(message.trim());
  },
};
