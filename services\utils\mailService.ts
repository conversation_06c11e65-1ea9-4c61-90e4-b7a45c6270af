import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import * as dotenv from 'dotenv';

dotenv.config();

const sesClient = new SESClient({
  region: process.env.AWS_REGION!,
});

/**
 * Send an HTML email using AWS SES
 * @param {string} htmlContent - Raw HTML string to send as email body
 * @param {string} recipientEmail - Recipient's email address
 * @param {string} subject - Email subject line
 * @returns {Promise<boolean>} - true if sent successfully, else false
 */
export async function sendMail(htmlContent: string, recipientEmail: string, subject: string): Promise<boolean> {
  try {
    if (!recipientEmail) {
      console.error('Recipient email is required');
      return false;
    }

    if (!process.env.MAILID) {
      console.error('MAILID environment variable is not set');
      return false;
    }

    if (!htmlContent || htmlContent.trim() === '') {
      console.error('Email content is empty');
      return false;
    }

    const params = {
      Destination: {
        ToAddresses: [recipientEmail],
      },
      Message: {
        Body: {
          Html: {
            Charset: 'UTF-8',
            Data: htmlContent,
          },
        },
        Subject: {
          Charset: 'UTF-8',
          Data: subject,
        },
      },
      Source: process.env.MAILID,
    };

    const command = new SendEmailCommand(params);
    await sesClient.send(command);

    return true;
  } catch (error: any) {
    if (error.name === 'MessageRejected') {
      console.error('Email was rejected. Check if the sender email is verified in SES.');
    } else if (error.name === 'ValidationError') {
      console.error('Validation error. Check email parameters:', error.message);
    } else {
      console.error('Failed to send email:', error);
    }
    return false;
  }
}
