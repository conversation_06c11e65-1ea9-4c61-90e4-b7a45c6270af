import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { Bookmark } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export interface RecommendationCardProps {
  id: string;
  type: string;
  title: string;
  description: string;
  imageUrl: string;
  category: string;
  readTime?: string;
  watchTime?: string;
  wellnessScoreImpact?: number;
  fromProvider?: boolean;
  providerId?: string;
  providerName?: string;
  isSaved?: boolean;
  isSeasonal?: boolean;
  seasonalContext?: string;
  evidenceLevel?: 'high' | 'medium' | 'low';
  onClick: () => void;
  onSave?: () => void;
}

export function RecommendationCard({
  id,
  type,
  title,
  description,
  imageUrl,
  category,
  readTime,
  watchTime,
  wellnessScoreImpact,
  fromProvider,
  providerId,
  providerName,
  isSaved = false,
  isSeasonal,
  seasonalContext,
  evidenceLevel,
  onClick,
  onSave
}: RecommendationCardProps) {
  // Define visual properties based on recommendation type
  const typeColor: Record<string, string> = {
    article: 'bg-[#8b5cf6]/10 text-[#7c3aed] border-[#7c3aed]/30',
    video: 'bg-[#ef4444]/10 text-[#ef4444] border-[#ef4444]/30', 
    blog: 'bg-[#3b82f6]/10 text-[#3b82f6] border-[#3b82f6]/30',
    news: 'bg-[#10b981]/10 text-[#10b981] border-[#10b981]/30'
  };

  // Evidence level colors
  const evidenceColors: Record<string, string> = {
    high: 'bg-emerald-500/10 text-emerald-500 border-emerald-500/30',
    medium: 'bg-amber-500/10 text-amber-500 border-amber-500/30',
    low: 'bg-gray-500/10 text-gray-500 border-gray-500/30'
  };
  
  const handleSaveClick = (e: React.MouseEvent) => {
    if (onSave) {
      e.stopPropagation();
      onSave();
    }
  };

  return (
    <TooltipProvider>
      <motion.div
        whileHover={{ y: -5, transition: { duration: 0.2 } }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card 
          className="overflow-hidden cursor-pointer border border-[#f1f5f9] bg-white hover:border-[#e2e8f0] transition-colors h-full hover:shadow-md"
          onClick={onClick}
        >
          <div className="relative pb-[56.25%] overflow-hidden">
            {/* Image with overlay gradient for better readability */}
            <img 
              src={imageUrl} 
              alt={title} 
              className="absolute inset-0 h-full w-full object-cover transition-transform duration-500 hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-60"></div>
            
            {/* Type badge */}
            <div className="absolute top-2 right-2 flex gap-2">
              <Badge variant="outline" className={`${typeColor[type]} border-0 font-medium`}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Badge>
            </div>
            
            {/* Provider badge */}
            {fromProvider && (
              <div className="absolute top-2 left-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge variant="outline" className="bg-blue-600 text-white border-0 font-medium flex items-center">
                      <i className="material-icons text-xs mr-1">verified</i>
                      Provider
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Recommended by your healthcare provider{providerName ? `: ${providerName}` : ''}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
            
            {/* Seasonal badge */}
            {isSeasonal && (
              <div className="absolute bottom-2 left-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge variant="outline" className="bg-amber-500 text-white border-0 font-medium flex items-center">
                      <i className="material-icons text-xs mr-1">event</i>
                      Seasonal
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">{seasonalContext || 'Timely recommendation for this season'}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
          </div>
          
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <Badge variant="outline" className="bg-[#f8fafc] text-[#64748b] border-[#e2e8f0] font-normal px-2 py-0 text-xs">
                {category}
              </Badge>
              
              <div className="flex items-center gap-2">
                {/* Evidence level */}
                {evidenceLevel && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className={`${evidenceColors[evidenceLevel]} text-xs px-1.5 py-0 font-normal`}>
                        <i className="material-icons text-[10px] mr-0.5">science</i>
                        {evidenceLevel}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Evidence level: {evidenceLevel.charAt(0).toUpperCase() + evidenceLevel.slice(1)}</p>
                    </TooltipContent>
                  </Tooltip>
                )}
                
                {/* Time indicator */}
                {(readTime || watchTime) && (
                  <span className="text-xs text-[#64748b]">
                    {readTime ? `${readTime} min read` : `${watchTime} min watch`}
                  </span>
                )}
              </div>
            </div>
            
            <h3 className="font-semibold text-[#1e293b] mb-1 line-clamp-2">{title}</h3>
            <p className="text-sm text-[#64748b] line-clamp-2 mb-3">{description}</p>
            
            <div className="flex justify-between items-center mt-auto pt-2 border-t border-[#f1f5f9]">
              {/* Wellness score impact */}
              {wellnessScoreImpact && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center text-xs text-emerald-600">
                      <i className="material-icons text-sm mr-1">trending_up</i>
                      +{wellnessScoreImpact} points
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Potential wellness score impact</p>
                  </TooltipContent>
                </Tooltip>
              )}
              
              {/* Save button */}
              {onSave && (
                <button 
                  onClick={handleSaveClick}
                  className={`flex items-center text-xs ${isSaved ? 'text-[#7c3aed]' : 'text-[#64748b]'} hover:text-[#7c3aed] transition-colors`}
                >
                  <Bookmark size={16} className={`${isSaved ? 'fill-[#7c3aed]' : ''}`} />
                  <span className="ml-1">{isSaved ? 'Saved' : 'Save'}</span>
                </button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </TooltipProvider>
  );
}
