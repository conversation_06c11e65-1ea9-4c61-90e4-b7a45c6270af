import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useToast } from '@/hooks/use-toast'
import { memberProfileService } from '@/lib/api'
import { AuthenticatedImage } from '@/components/ui/authenticated-image'
import {
  Home,
  User,
  Users,
  Heart,
  Activity,
  Lightbulb,
  Award,
  HelpCircle,
  LogOut,
  Menu,
  X,
  ChevronLeft,
  Bell,
  Shield,
  Search
} from 'lucide-react'

const MemberLayout = () => {
  const { user, logout } = useAuth()
  const { toast } = useToast()
  const location = useLocation()
  const navigate = useNavigate()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [collapsed, setCollapsed] = useState(false)
  const [unreadNotifications] = useState(2)

  // Profile data state
  const [profileData, setProfileData] = useState<any>(null)
  const [isLoadingProfile, setIsLoadingProfile] = useState(true)
  const [imageCacheBuster, setImageCacheBuster] = useState(Date.now())

  // Primary navigation items (Dashboard & Personal)
  const primaryNavigation = [
    {
      path: '/member/dashboard',
      label: 'Dashboard',
      icon: Home,
      description: 'Overview of your wellness journey'
    },
    {
      path: '/member/profile',
      label: 'My Profile',
      icon: User,
      description: 'Personal information and settings'
    },
    {
      path: '/member/dependents',
      label: 'Dependents',
      icon: Users,
      description: 'Manage family members'
    },
  ]

  // Health navigation items
  const healthNavigation = [
    {
      path: '/member/health-plan',
      label: 'Health Plan',
      icon: Heart,
      description: 'View your plan details and coverage'
    },
    {
      path: '/member/wellness-goals',
      label: 'Wellness Goals',
      icon: Activity,
      description: 'Set and track your wellness goals'
    },
    {
      path: '/member/recommendations',
      label: 'Recommendations',
      icon: Lightbulb,
      description: 'Personalized wellness suggestions',
      badge: unreadNotifications > 0 ? unreadNotifications.toString() : undefined
    },
  ]

  // Support navigation items
  const supportNavigation = [
    {
      path: '/member/rewards',
      label: 'Rewards',
      icon: Award,
      description: 'Earn and redeem wellness rewards'
    },
    {
      path: '/member/support',
      label: 'Help & Support',
      icon: HelpCircle,
      description: 'Get assistance and resources'
    },
  ]

  // Load profile data
  const loadProfileData = async () => {
    try {
      setIsLoadingProfile(true)
      const data = await memberProfileService.getProfile()
      setProfileData(data)
    } catch (error) {
      console.error('Failed to load profile data:', error)
      // Continue with auth context user data as fallback
    } finally {
      setIsLoadingProfile(false)
    }
  }

  // Load profile data on mount and when user changes
  useEffect(() => {
    if (user) {
      loadProfileData()
    }
  }, [user])

  // Listen for profile updates from other components
  useEffect(() => {
    const handleProfileUpdate = (event: CustomEvent) => {
      if (event.detail) {
        setProfileData(event.detail)
        setImageCacheBuster(Date.now())
      }
    }

    window.addEventListener('profileUpdated', handleProfileUpdate as EventListener)
    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate as EventListener)
    }
  }, [])

  // Get current user data (profile data takes precedence over auth context)
  const getCurrentUserData = () => {
    if (profileData?.profile) {
      return {
        firstName: profileData.profile.firstName,
        lastName: profileData.profile.lastName,
        email: profileData.profile.email,
        profilePictureUrl: profileData.profile.profilePictureUrl,
        employeeId: profileData.profile.employeeId || 'MEM001',
        role: 'Member'
      }
    }

    // Fallback to auth context user data
    return {
      firstName: user?.firstName || 'Member',
      lastName: user?.lastName || 'User',
      email: user?.email || '<EMAIL>',
      profilePictureUrl: (user as any)?.profileImage || null,
      employeeId: (user as any)?.employeeId || 'MEM001',
      role: 'Member'
    }
  }

  const currentUser = getCurrentUserData()

  const toggleCollapse = () => {
    setCollapsed(!collapsed)
  }

  const handleLogout = async () => {
    toast({
      title: "Logout Successful",
      description: "You have been successfully logged out.",
    });
    try {
      await logout()
      navigate("/auth/send-code")
    } catch (error) {
      console.error('Logout failed:', error)
      navigate("/auth/send-code")
    }
  }

  const getPageTitle = () => {
    switch(location.pathname) {
      case '/member/dashboard': return 'Dashboard'
      case '/member/profile': return 'My Profile'
      case '/member/dependents': return 'Dependents'
      case '/member/health-plan': return 'Health Plan'
      case '/member/wellness-goals': return 'Wellness Goals'
      case '/member/rewards': return 'Rewards'
      case '/member/support': return 'Help & Support'
      default: return 'Dashboard'
    }
  }

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar for desktop */}
      <div className={`hidden md:flex md:flex-col md:fixed md:inset-y-0 ${collapsed ? 'md:w-20' : 'md:w-72'} transition-all duration-300 ease-in-out z-30`}>
        <div className="flex flex-col h-full border-r border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm">
        <div className="flex items-center h-16 px-6 border-b border-gray-200 dark:border-gray-800 bg-gradient-to-r from-blue-600 to-indigo-600 flex-shrink-0">
          {collapsed ? (
            // When collapsed: show hamburger menu centered
            <div className="flex items-center justify-center w-full">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleCollapse}
                className="text-white hover:bg-white/20 h-8 w-8"
              >
                <Menu className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            // When expanded: show logo and close button
            <>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-md bg-white flex items-center justify-center">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
                <AnimatePresence mode="wait">
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="text-white text-lg font-semibold"
                  >
                    Aperion Health
                  </motion.span>
                </AnimatePresence>
              </div>
              <div className="flex ml-auto">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleCollapse}
                  className="text-white hover:bg-white/20 h-8 w-8"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </div>
            </>
          )}
        </div>

        {/* Scrollable content area */}
        <div className="flex-1 overflow-y-auto min-h-0 scrollbar-hide">
          {/* User profile section */}
          <div className={`border-b border-gray-200 dark:border-gray-800 p-4`}>
            {user && !collapsed && (
              <div className="flex items-center space-x-3">
                <Avatar className="h-10 w-10">
                  {currentUser.profilePictureUrl ? (
                    <AuthenticatedImage
                      src={`/api/member/profile/photo/view?t=${imageCacheBuster}`}
                      alt={`${currentUser.firstName} ${currentUser.lastName}`}
                      className="w-full h-full object-cover rounded-full"
                      fallback={
                        <AvatarFallback className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                          {currentUser.firstName?.[0]}{currentUser.lastName?.[0]}
                        </AvatarFallback>
                      }
                    />
                  ) : (
                    <AvatarFallback className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                      {currentUser.firstName?.[0]}{currentUser.lastName?.[0]}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div className="flex-1 min-w-0">
                  {isLoadingProfile ? (
                    <div className="space-y-1">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4"></div>
                    </div>
                  ) : (
                    <>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {`${currentUser.firstName} ${currentUser.lastName}`}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        Member ID: {currentUser.employeeId}
                      </p>
                    </>
                  )}
                </div>
                {unreadNotifications > 0 && (
                  <div className="relative">
                    <Bell className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-blue-600 text-white">
                      {unreadNotifications}
                    </Badge>
                  </div>
                )}
              </div>
            )}

            {user && collapsed && (
              <div className="flex flex-col items-center justify-center">
                <Avatar className="h-10 w-10">
                  {currentUser.profilePictureUrl ? (
                    <AuthenticatedImage
                      src={`/api/member/profile/photo/view?t=${imageCacheBuster}`}
                      alt={`${currentUser.firstName} ${currentUser.lastName}`}
                      className="w-full h-full object-cover rounded-full"
                      fallback={
                        <AvatarFallback className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                          {currentUser.firstName?.[0]}{currentUser.lastName?.[0]}
                        </AvatarFallback>
                      }
                    />
                  ) : (
                    <AvatarFallback className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                      {currentUser.firstName?.[0]}{currentUser.lastName?.[0]}
                    </AvatarFallback>
                  )}
                </Avatar>
                {unreadNotifications > 0 && (
                  <Badge className="mt-2 bg-blue-600 text-white">{unreadNotifications}</Badge>
                )}
              </div>
            )}
          </div>

          {/* Navigation sections */}
          <div className={collapsed ? "px-2 py-4" : "px-4 py-4"}>
            {/* Dashboard & Personal section */}
            <div className={collapsed ? "mb-2" : "mb-6"}>
              <AnimatePresence mode="wait">
                {!collapsed && (
                  <motion.h3
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Dashboard & Personal
                  </motion.h3>
                )}
              </AnimatePresence>
              <nav className="space-y-1">
                {primaryNavigation.map((item) => {
                  const isActive = location.pathname === item.path
                  const IconComponent = item.icon

                  return (
                    <Button
                      key={item.path}
                      variant={isActive ? "secondary" : "ghost"}
                      className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} py-6 ${
                        isActive
                          ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-200'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                      }`}
                      onClick={() => navigate(item.path)}
                      title={collapsed ? item.label : ''}
                    >
                      <IconComponent className={`${collapsed ? '' : 'mr-3'} h-5 w-5 ${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`} aria-hidden="true" />
                      <AnimatePresence mode="wait">
                        {!collapsed && (
                          <motion.span
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="text-sm font-medium"
                          >
                            {item.label}
                          </motion.span>
                        )}
                      </AnimatePresence>
                    </Button>
                  )
                })}
              </nav>
            </div>

            {/* Health & Wellness section */}
            <div className={collapsed ? "mb-2" : "mb-6"}>
              <AnimatePresence mode="wait">
                {!collapsed && (
                  <motion.h3
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Health & Wellness
                  </motion.h3>
                )}
              </AnimatePresence>
              <nav className="space-y-1">
                {healthNavigation.map((item) => {
                  const isActive = location.pathname === item.path
                  const IconComponent = item.icon

                  return (
                    <Button
                      key={item.path}
                      variant={isActive ? "secondary" : "ghost"}
                      className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} py-6 ${
                        isActive
                          ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-200'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                      }`}
                      onClick={() => navigate(item.path)}
                      title={collapsed ? item.label : ''}
                    >
                      <IconComponent className={`${collapsed ? '' : 'mr-3'} h-5 w-5 ${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`} aria-hidden="true" />
                      <AnimatePresence mode="wait">
                        {!collapsed && (
                          <motion.div
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="flex items-center justify-between flex-1"
                          >
                            <span className="text-sm font-medium">{item.label}</span>
                            {item.badge && (
                              <Badge className="ml-auto bg-blue-600 text-white hover:bg-blue-700">
                                {item.badge}
                              </Badge>
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </Button>
                  )
                })}
              </nav>
            </div>

            {/* Support & Resources section */}
            <div>
              <AnimatePresence mode="wait">
                {!collapsed && (
                  <motion.h3
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Support & Resources
                  </motion.h3>
                )}
              </AnimatePresence>
              <nav className="space-y-1">
                {supportNavigation.map((item) => {
                  const isActive = location.pathname === item.path
                  const IconComponent = item.icon

                  return (
                    <Button
                      key={item.path}
                      variant={isActive ? "secondary" : "ghost"}
                      className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} py-6 ${
                        isActive
                          ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-200'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                      }`}
                      onClick={() => navigate(item.path)}
                      title={collapsed ? item.label : ''}
                    >
                      <IconComponent className={`${collapsed ? '' : 'mr-3'} h-5 w-5 ${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`} aria-hidden="true" />
                      <AnimatePresence mode="wait">
                        {!collapsed && (
                          <motion.span
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="text-sm font-medium"
                          >
                            {item.label}
                          </motion.span>
                        )}
                      </AnimatePresence>
                    </Button>
                  )
                })}
              </nav>
            </div>
          </div>

        </div>

        {/* Footer with actions - positioned at bottom */}
        <div className="flex-shrink-0 p-3 border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 space-y-1">
          <Button
            variant="ghost"
            className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60 py-2.5`}
            onClick={() => navigate('/')}
            title={collapsed ? 'Back to Home' : ''}
          >
            <Home className={`${collapsed ? '' : 'mr-3'} h-4 w-4`} />
            <AnimatePresence mode="wait">
              {!collapsed && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="text-sm font-medium"
                >
                  Back to Home
                </motion.span>
              )}
            </AnimatePresence>
          </Button>

          <Button
            variant="ghost"
            className={`w-full ${collapsed ? 'justify-center px-2' : 'justify-start px-3'} text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60 py-2.5`}
            onClick={handleLogout}
            title={collapsed ? 'Logout' : ''}
          >
            <LogOut className={`${collapsed ? '' : 'mr-3'} h-4 w-4`} />
            <AnimatePresence mode="wait">
              {!collapsed && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="text-sm font-medium"
                >
                  Logout
                </motion.span>
              )}
            </AnimatePresence>
          </Button>
        </div>
      </div>
      </div>

      {/* Mobile sidebar */}
      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-75" onClick={() => setSidebarOpen(false)}></div>
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white dark:bg-gray-900 shadow-xl">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <Button
              variant="ghost"
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white text-white"
              onClick={() => setSidebarOpen(false)}
            >
              <span className="sr-only">Close sidebar</span>
              <X className="h-6 w-6" aria-hidden="true" />
            </Button>
          </div>

          {/* Mobile sidebar header with logo */}
          <div className="flex items-center h-16 px-4 border-b border-gray-200 dark:border-gray-800 bg-gradient-to-r from-blue-600 to-indigo-600">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-md bg-white flex items-center justify-center">
                <Shield className="h-5 w-5 text-blue-600" />
              </div>
              <span className="text-white text-lg font-semibold">Aperion Health</span>
            </div>
          </div>

          {/* Member profile for mobile */}
          <div className="flex items-center p-4 border-b border-gray-200 dark:border-gray-800">
            <Avatar className="h-10 w-10 mr-3">
              {currentUser.profilePictureUrl ? (
                <AuthenticatedImage
                  src={`/api/member/profile/photo/view?t=${imageCacheBuster}`}
                  alt={`${currentUser.firstName} ${currentUser.lastName}`}
                  className="w-full h-full object-cover rounded-full"
                  fallback={
                    <AvatarFallback className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                      {currentUser.firstName?.[0]}{currentUser.lastName?.[0]}
                    </AvatarFallback>
                  }
                />
              ) : (
                <AvatarFallback className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                  {currentUser.firstName?.[0]}{currentUser.lastName?.[0]}
                </AvatarFallback>
              )}
            </Avatar>
            <div className="flex-1 min-w-0">
              {isLoadingProfile ? (
                <div className="space-y-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3"></div>
                </div>
              ) : (
                <>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {`${currentUser.firstName} ${currentUser.lastName}`}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{currentUser.role}</p>
                </>
              )}
            </div>
          </div>

          {/* Mobile navigation */}
          <div className="flex-1 h-0 overflow-y-auto scrollbar-hide">
            <div className="px-2 py-4 space-y-6">
              <div>
                <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Dashboard & Personal
                </h3>
                <nav className="mt-2 space-y-1">
                  {primaryNavigation.map((item) => {
                    const isActive = location.pathname === item.path;
                    return (
                      <Button
                        key={item.path}
                        variant={isActive ? "secondary" : "ghost"}
                        className={`w-full justify-start px-3 py-6 ${
                          isActive
                            ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-200'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                        }`}
                        onClick={() => {
                          navigate(item.path);
                          setSidebarOpen(false);
                        }}
                      >
                        <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`} aria-hidden="true" />
                        <span className="text-sm font-medium">{item.label}</span>
                      </Button>
                    );
                  })}
                </nav>
              </div>

              <div>
                <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Health & Wellness
                </h3>
                <nav className="mt-2 space-y-1">
                  {healthNavigation.map((item) => {
                    const isActive = location.pathname === item.path;
                    return (
                      <Button
                        key={item.path}
                        variant="ghost"
                        className={`w-full justify-start px-3 py-6 ${
                          isActive
                            ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-200'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                        }`}
                        onClick={() => {
                          navigate(item.path);
                          setSidebarOpen(false);
                        }}
                      >
                        <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`} aria-hidden="true" />
                        <span className="text-sm font-medium">{item.label}</span>
                        {item.badge && (
                          <Badge className="ml-auto bg-blue-600 text-white hover:bg-blue-700">
                            {item.badge}
                          </Badge>
                        )}
                      </Button>
                    );
                  })}
                </nav>
              </div>

              <div>
                <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Support & Resources
                </h3>
                <nav className="mt-2 space-y-1">
                  {supportNavigation.map((item) => {
                    const isActive = location.pathname === item.path;
                    return (
                      <Button
                        key={item.path}
                        variant="ghost"
                        className={`w-full justify-start px-3 py-6 ${
                          isActive
                            ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-200'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60'
                        }`}
                        onClick={() => {
                          navigate(item.path);
                          setSidebarOpen(false);
                        }}
                      >
                        <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`} aria-hidden="true" />
                        <span className="text-sm font-medium">{item.label}</span>
                      </Button>
                    );
                  })}
                </nav>
              </div>
            </div>
          </div>

          {/* Mobile footer with actions */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-800 space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-700"
              onClick={() => {
                navigate('/');
                setSidebarOpen(false);
              }}
            >
              <Home className="mr-3 h-4 w-4" />
              <span className="text-sm">Back to Home</span>
            </Button>

            <Button
              variant="ghost"
              className="w-full justify-start text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
              onClick={() => {
                handleLogout();
                setSidebarOpen(false);
              }}
            >
              <LogOut className="mr-3 h-4 w-4" />
              <span className="text-sm">Logout</span>
            </Button>
          </div>
        </div>
        <div className="flex-shrink-0 w-14"></div>
      </div>

      {/* Main content wrapper */}
      <div className={`flex flex-col ${collapsed ? 'md:ml-20' : 'md:ml-72'} md:pt-16 transition-all duration-300 ease-in-out min-h-screen`}>
        {/* Mobile header */}
        <div className="md:hidden sticky top-0 z-20 flex items-center justify-between bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 h-16 px-4">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(true)}
              className="text-gray-700 dark:text-gray-300"
            >
              <span className="sr-only">Open sidebar</span>
              <Menu className="h-6 w-6" />
            </Button>
          </div>
          <div className="flex items-center">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-md p-1 mr-2">
              <Shield className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">{getPageTitle()}</h1>
          </div>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-700 dark:text-gray-300 relative"
            >
              <Bell className="h-5 w-5" />
              {unreadNotifications > 0 && (
                <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center text-white">
                  {unreadNotifications}
                </span>
              )}
            </Button>
          </div>
        </div>
        {/* Desktop header */}
        <div className="hidden md:flex fixed top-0 right-0 left-0 z-10 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 h-16 items-center justify-between px-6" style={{left: collapsed ? '5rem' : '18rem'}}>
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">{getPageTitle()}</h1>
            <Badge variant="outline" className="ml-4 px-2 py-1 text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800">
              Member Portal
            </Badge>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-700 dark:text-gray-300 relative"
            >
              <Bell className="h-5 w-5" />
              {unreadNotifications > 0 && (
                <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center text-white">
                  {unreadNotifications}
                </span>
              )}
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className="text-gray-700 dark:text-gray-300"
            >
              <Search className="h-5 w-5" />
            </Button>

            <Avatar className="h-8 w-8">
              {currentUser.profilePictureUrl ? (
                <AuthenticatedImage
                  src={`/api/member/profile/photo/view?t=${imageCacheBuster}`}
                  alt={`${currentUser.firstName} ${currentUser.lastName}`}
                  className="w-full h-full object-cover rounded-full"
                  fallback={
                    <AvatarFallback className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                      {currentUser.firstName?.[0]}{currentUser.lastName?.[0]}
                    </AvatarFallback>
                  }
                />
              ) : (
                <AvatarFallback className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                  {currentUser.firstName?.[0]}{currentUser.lastName?.[0]}
                </AvatarFallback>
              )}
            </Avatar>
          </div>
        </div>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 custom-scrollbar p-4 sm:p-6 lg:p-8 pt-16 md:pt-0">
          <div className="max-w-7xl mx-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  )
}

export default MemberLayout