{"name": "@aperion/auth", "version": "1.0.0", "description": "Authentication package for Aperion Health platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@aperion/shared": "^1.0.0", "aws-sdk": "^2.1498.0", "@aws-sdk/client-cognito-identity-provider": "^3.454.0", "amazon-cognito-identity-js": "^6.3.6", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.5", "@types/bcrypt": "^5.0.2", "@types/uuid": "^9.0.7", "typescript": "^5.1.0", "rimraf": "^5.0.5"}}