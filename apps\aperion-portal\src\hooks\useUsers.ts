import { useState, useEffect, useCallback } from 'react';
import { userService, User, UserQueryParams, UserStatistics, FilterApiResponse } from '../services/userService';

export interface UseUsersResult {
  users: User[];
  loading: boolean;
  error: string | null;
  total: number;
  totalPages: number;
  currentPage: number;
  hasNext: boolean;
  hasPrev: boolean;
  statistics: UserStatistics | null;
  companies: string[];
  filterInfo: any; // Filter metadata from the new endpoint

  // Actions
  fetchUsers: (params?: UserQueryParams) => Promise<void>;
  fetchStatistics: () => Promise<void>;
  fetchCompanies: () => Promise<void>;
  createUser: (userData: Partial<User>) => Promise<boolean>;
  updateUser: (id: string, userData: Partial<User>) => Promise<boolean>;
  deleteUser: (id: string) => Promise<boolean>;
  suspendUser: (id: string) => Promise<boolean>;
  activateUser: (id: string) => Promise<boolean>;

  // Pagination
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}

export const useUsers = (initialParams?: UserQueryParams): UseUsersResult => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statistics, setStatistics] = useState<UserStatistics | null>(null);
  const [companies, setCompanies] = useState<string[]>([]);
  const [filterInfo, setFilterInfo] = useState<any>(null);
  
  // Pagination state
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(initialParams?.page || 1);
  const [limit, setLimit] = useState(initialParams?.limit || 20);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrev, setHasPrev] = useState(false);
  
  // Current query parameters
  const [queryParams, setQueryParams] = useState<UserQueryParams>(initialParams || {});

  const fetchUsers = useCallback(async (params?: UserQueryParams) => {
    setLoading(true);
    setError(null);

    try {
      const finalParams = { ...queryParams, ...params, page: currentPage, limit };
      const response = await userService.getUsers(finalParams);

      if (response.success && response.data) {
        setUsers(response.data);
        setFilterInfo(response.filterInfo || null); // Store filter metadata

        if (response.meta) {
          setTotal(response.meta.total);
          setTotalPages(response.meta.totalPages);
          setHasNext(response.meta.hasNext);
          setHasPrev(response.meta.hasPrev);
        }
      } else {
        setError(response.error?.message || 'Failed to fetch users');
        setFilterInfo(null);
      }
    } catch (err) {
      setError('An unexpected error occurred');
      setFilterInfo(null);
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  }, [queryParams, currentPage, limit]);

  const fetchStatistics = useCallback(async () => {
    try {
      console.log('Fetching user statistics...');
      const response = await userService.getUserStatistics();
      console.log('Statistics response:', response);

      if (response.success && response.data) {
        console.log('Statistics data received:', response.data);
        setStatistics(response.data);
      } else {
        console.error('Failed to fetch statistics:', response.error?.message);
        console.error('Full statistics error response:', response);
        // Set statistics to null to indicate failure
        setStatistics(null);
      }
    } catch (err) {
      console.error('Error fetching statistics:', err);
      setStatistics(null);
    }
  }, []);

  const fetchCompanies = useCallback(async () => {
    console.log('🔍 Starting to fetch companies...');
    try {
      console.log('📡 Making API call to userService.getCompanies()');
      const response = await userService.getCompanies();
      console.log('📊 Companies API response:', response);

      if (response.success && response.data) {
        setCompanies(response.data);
        console.log('✅ Successfully fetched companies:', response.data);
      } else {
        const errorMessage = response.error?.message || 'Failed to fetch companies';
        console.error('❌ Failed to fetch companies:', errorMessage);
        console.error('Full error response:', response.error);
        setError(`Company filter error: ${errorMessage}`);
        // Set companies to empty array to indicate failure
        setCompanies([]);
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while fetching companies';
      console.error('💥 Exception while fetching companies:', err);
      setError(errorMessage);
      setCompanies([]);
    }
  }, []);

  const createUser = useCallback(async (userData: Partial<User>): Promise<boolean> => {
    try {
      const response = await userService.createUser(userData);
      if (response.success) {
        await fetchUsers(); // Refresh the list
        return true;
      } else {
        const errorMessage = response.error?.message || 'Failed to create user';
        setError(errorMessage);
        // Error toast will be handled by the calling component
        return false;
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while creating user';
      setError(errorMessage);
      console.error('Error creating user:', err);
      // Error toast will be handled by the calling component
      return false;
    }
  }, [fetchUsers]);

  const updateUser = useCallback(async (id: string, userData: Partial<User>): Promise<boolean> => {
    try {
      const response = await userService.updateUser(id, userData);
      if (response.success) {
        await fetchUsers(); // Refresh the list
        return true;
      } else {
        const errorMessage = response.error?.message || 'Failed to update user';
        setError(errorMessage);
        // Error toast will be handled by the calling component
        return false;
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while updating user';
      setError(errorMessage);
      console.error('Error updating user:', err);
      // Error toast will be handled by the calling component
      return false;
    }
  }, [fetchUsers]);

  const deleteUser = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await userService.deleteUser(id);
      if (response.success) {
        await fetchUsers(); // Refresh the list
        return true;
      } else {
        const errorMessage = response.error?.message || 'Failed to delete user';
        setError(errorMessage);
        // Error toast will be handled by the calling component
        return false;
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while deleting user';
      setError(errorMessage);
      console.error('Error deleting user:', err);
      // Error toast will be handled by the calling component
      return false;
    }
  }, [fetchUsers]);

  const suspendUser = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await userService.suspendUser(id);
      if (response.success) {
        await fetchUsers(); // Refresh the list
        return true;
      } else {
        const errorMessage = response.error?.message || 'Failed to suspend user';
        setError(errorMessage);
        // Error toast will be handled by the calling component
        return false;
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while suspending user';
      setError(errorMessage);
      console.error('Error suspending user:', err);
      // Error toast will be handled by the calling component
      return false;
    }
  }, [fetchUsers]);

  const activateUser = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await userService.activateUser(id);
      if (response.success) {
        await fetchUsers(); // Refresh the list
        return true;
      } else {
        const errorMessage = response.error?.message || 'Failed to activate user';
        setError(errorMessage);
        // Error toast will be handled by the calling component
        return false;
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while activating user';
      setError(errorMessage);
      console.error('Error activating user:', err);
      // Error toast will be handled by the calling component
      return false;
    }
  }, [fetchUsers]);

  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const setLimitCallback = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setCurrentPage(1); // Reset to first page when changing limit
  }, []);

  // Update query parameters and refetch
  const updateQueryParams = useCallback((newParams: UserQueryParams) => {
    setQueryParams(prev => ({ ...prev, ...newParams }));
    setCurrentPage(1); // Reset to first page when changing filters
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchUsers();
    fetchStatistics();
    fetchCompanies();
  }, []);

  // Refetch when page or limit changes
  useEffect(() => {
    if (currentPage > 0) {
      fetchUsers();
    }
  }, [currentPage, limit]);

  // Refetch when query parameters change (role, search, company filters)
  useEffect(() => {
    if (Object.keys(queryParams).length > 0) {
      console.log('Query parameters changed, fetching users:', queryParams);
      fetchUsers();
    }
  }, [queryParams, fetchUsers]);

  return {
    users,
    loading,
    error,
    total,
    totalPages,
    currentPage,
    hasNext,
    hasPrev,
    statistics,
    companies,
    filterInfo, // Add filter metadata

    // Actions
    fetchUsers,
    fetchStatistics,
    fetchCompanies,
    createUser,
    updateUser,
    deleteUser,
    suspendUser,
    activateUser,

    // Pagination
    setPage,
    setLimit: setLimitCallback,

    // Additional helper for updating query params
    updateQueryParams,
  } as UseUsersResult & { updateQueryParams: (params: UserQueryParams) => void };
};
