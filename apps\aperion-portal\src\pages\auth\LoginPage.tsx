import { useState } from 'react'
import { Link, Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'

const LoginPage = () => {
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [otpSent, setOtpSent] = useState(false)
  const [otp, setOtp] = useState('')
  
  const { user, login, getDefaultRoute } = useAuth()
  const { toast } = useToast()
  const location = useLocation()
  
  const from = location.state?.from?.pathname || getDefaultRoute()

  if (user) {
    return <Navigate to={from} replace />
  }

  const handleSendOTP = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // TODO: Implement OTP sending logic
      console.log('Sending OTP to:', email)
      setOtpSent(true)
      toast({
        title: "OTP Sent!",
        description: "Please check your email for the verification code.",
      })
    } catch (error) {
      toast({
        title: "Failed to send OTP",
        description: error instanceof Error ? error.message : "An error occurred while sending OTP.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // TODO: Implement OTP verification and login logic
      console.log('Verifying OTP:', otp, 'for email:', email)
      // await login(email, otp) // Update login method to accept OTP
      toast({
        title: "Welcome back!",
        description: "You have successfully logged in.",
      })
    } catch (error) {
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "Invalid verification code.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl text-center">Welcome to Aperion Health</CardTitle>
          <CardDescription className="text-center">
            {!otpSent
              ? "Enter your email to receive a verification code"
              : "Enter the verification code sent to your email"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Signing in..." : "Sign in"}
            </Button>
          </form>
          
          <div className="mt-6 space-y-2">
            <div className="text-center text-sm">
              <span className="text-muted-foreground">Have an activation code? </span>
              <Link to="/auth/activate" className="text-primary hover:underline">
                Activate account
              </Link>
            </div>
          </div>


        </CardContent>
      </Card>
    </div>
  )
}

export default LoginPage
