@echo off
REM Aperion Health - UserRole Import Fix Setup Script (Windows)
REM This script fixes the UserRole import/export issue by setting up dual-format builds

echo 🚀 Aperion Health - UserRole Import Fix Setup
echo ==============================================
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Please run this script from the Aperion Health project root directory
    pause
    exit /b 1
)

if not exist "packages\shared" (
    echo ❌ Please run this script from the Aperion Health project root directory
    pause
    exit /b 1
)

echo 📋 Step 1: Checking Node.js version
node --version
echo ✅ Node.js version checked
echo.

echo 📋 Step 2: Cleaning existing installations
echo Removing node_modules and package-lock files...
if exist "node_modules" rmdir /s /q "node_modules"
if exist "packages\shared\node_modules" rmdir /s /q "packages\shared\node_modules"
if exist "services\command-center\node_modules" rmdir /s /q "services\command-center\node_modules"
if exist "services\member\node_modules" rmdir /s /q "services\member\node_modules"
if exist "apps\aperion-portal\node_modules" rmdir /s /q "apps\aperion-portal\node_modules"
if exist "package-lock.json" del "package-lock.json"
if exist "packages\shared\package-lock.json" del "packages\shared\package-lock.json"
echo ✅ Cleaned existing installations
echo.

echo 📋 Step 3: Installing dependencies
echo Running npm install...
npm install
echo ✅ Dependencies installed
echo.

echo 📋 Step 4: Setting up shared package dual-format build
cd packages\shared

echo Cleaning existing shared package build...
if exist "dist" rmdir /s /q "dist"

echo Building shared package with dual-format support...
npm run build

REM Verify build
if exist "dist\cjs" (
    if exist "dist\esm" (
        if exist "dist\types" (
            echo ✅ Shared package dual-format build completed
        ) else (
            echo ❌ Types build failed
            pause
            exit /b 1
        )
    ) else (
        echo ❌ ESM build failed
        pause
        exit /b 1
    )
) else (
    echo ❌ CJS build failed
    pause
    exit /b 1
)

cd ..\..
echo.

echo 📋 Step 5: Verifying build outputs
echo Checking CommonJS format...
if exist "packages\shared\dist\cjs\index.js" (
    echo ✅ CommonJS build verified
) else (
    echo ❌ CommonJS build missing
    pause
    exit /b 1
)

echo.
echo Checking ES modules format...
if exist "packages\shared\dist\esm\index.js" (
    echo ✅ ES modules build verified
) else (
    echo ❌ ES modules build missing
    pause
    exit /b 1
)

echo.
echo Checking TypeScript declarations...
if exist "packages\shared\dist\types\index.d.ts" (
    echo ✅ TypeScript declarations verified
) else (
    echo ❌ TypeScript declarations missing
    pause
    exit /b 1
)
echo.

echo 📋 Step 6: Checking environment configuration
if exist ".env" (
    findstr /C:"4001" .env >nul
    if %errorlevel% equ 0 (
        echo ✅ CORS configuration includes port 4001
    ) else (
        echo ⚠️  Adding port 4001 to CORS configuration
        powershell -Command "(Get-Content .env) -replace 'CORS_ORIGINS=([^,]*)', 'CORS_ORIGINS=$1,http://localhost:4001' | Set-Content .env"
        echo ✅ Updated CORS configuration
    )
) else (
    echo ⚠️  .env file not found - you may need to create one
)
echo.

echo 🎉 Setup completed successfully!
echo.
echo Next steps:
echo 1. Run: start-services.bat (to start all services)
echo 2. Or manually start services in separate terminals:
echo    - API Gateway: cd packages\api-gateway ^&^& npm run dev
echo    - Command Center: cd services\command-center ^&^& npm run dev
echo    - Member Service: cd services\member ^&^& npm run dev
echo    - Frontend: cd apps\aperion-portal ^&^& npm run dev
echo.
echo The UserRole import issue should now be resolved!
pause
