import { S3Client, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Upload } from '@aws-sdk/lib-storage';
import sharp from 'sharp';
import { LoggerFactory } from './pinoLogger.js';

// Create a simple logger for the shared utilities
const logger = LoggerFactory.createLogger({ serviceName: 'api-gateway' });

// AWS S3 Configuration
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const s3Client = new S3Client({
  region: AWS_REGION,
});

const BUCKET_NAME = process.env.BUCKET_NAME || 'aperion-documents';

export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
}

export interface ProcessedImage {
  buffer: Buffer;
  size: number;
  width?: number;
  height?: number;
}

/**
 * Process image: resize, optimize, and convert to JPEG
 */
export async function processImage(
  imageBuffer: Buffer,
  maxWidth: number = 1024,
  maxHeight: number = 1024,
  quality: number = 85
): Promise<ProcessedImage> {
  try {
    const processed = await sharp(imageBuffer)
      .resize(maxWidth, maxHeight, {
        fit: 'inside',
        withoutEnlargement: true,
      })
      .jpeg({ quality })
      .toBuffer({ resolveWithObject: true });

    return {
      buffer: processed.data,
      size: processed.data.length,
      width: processed.info.width,
      height: processed.info.height,
    };
  } catch (error: any) {
    logger.error('Image processing failed', { error: error.message });
    throw new Error(`Image processing failed: ${error.message}`);
  }
}

/**
 * Upload file to S3
 */
export async function uploadToS3(
  buffer: Buffer,
  key: string,
  contentType: string = 'image/jpeg'
): Promise<UploadResult> {
  try {
    const upload = new Upload({
      client: s3Client,
      params: {
        Bucket: BUCKET_NAME,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        // Removed ACL since bucket doesn't allow ACLs
      },
    });

    await upload.done();

    // Generate a signed URL for accessing the uploaded file (valid for 7 days)
    const getObjectCommand = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    const signedUrl = await getSignedUrl(s3Client, getObjectCommand, {
      expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds
    });

    logger.info('File uploaded to S3 successfully with signed URL', {
      key,
      signedUrl: signedUrl.substring(0, 100) + '...', // Log truncated URL for security
      size: buffer.length,
    });

    return {
      success: true,
      url: signedUrl,
      key,
    };
  } catch (error: any) {
    logger.error('S3 upload failed', {
      error: error.message,
      key,
      bucket: BUCKET_NAME,
    });

    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Validate image file
 */
export function validateImage(
  file: Express.Multer.File,
  _minWidth: number = 50,
  _minHeight: number = 50
): { valid: boolean; error?: string } {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.mimetype)) {
    return {
      valid: false,
      error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.',
    };
  }

  // Check file size (5MB limit)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size exceeds limit of ${maxSize / (1024 * 1024)}MB`,
    };
  }

  return { valid: true };
}

/**
 * Generate a signed URL for an existing S3 object
 */
export async function generateSignedUrl(key: string): Promise<string | null> {
  try {
    const getObjectCommand = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    const signedUrl = await getSignedUrl(s3Client, getObjectCommand, {
      expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds
    });

    logger.info('Generated signed URL for existing S3 object', {
      key,
      signedUrl: signedUrl.substring(0, 100) + '...', // Log truncated URL for security
    });

    return signedUrl;
  } catch (error: any) {
    logger.error('Failed to generate signed URL', {
      error: error.message,
      key,
      bucket: BUCKET_NAME,
    });
    return null;
  }
}

/**
 * Upload profile photo - complete workflow
 */
export async function uploadProfilePhoto(
  file: Express.Multer.File,
  employerId: string,
  userId: string
): Promise<{
  success: boolean;
  url?: string;
  key?: string;
  processedImage?: ProcessedImage;
  error?: string;
}> {
  try {
    // Validate image
    const validation = validateImage(file);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error || 'Validation failed',
      };
    }

    // Process image
    const processedImage = await processImage(file.buffer);

    // Generate S3 key
    const key = `${employerId}/${userId}/profile.jpg`;

    // Upload to S3
    const uploadResult = await uploadToS3(processedImage.buffer, key);

    if (!uploadResult.success) {
      return {
        success: false,
        error: uploadResult.error || 'Upload failed',
      };
    }

    return {
      success: true,
      url: uploadResult.url!,
      key: uploadResult.key!,
      processedImage,
    };
  } catch (error: any) {
    logger.error('Profile photo upload failed', {
      error: error.message,
      employerId,
      userId,
    });

    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Delete file from S3
 */
export async function deleteFromS3(key: string): Promise<{ success: boolean; error?: string }> {
  try {
    const deleteCommand = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    await s3Client.send(deleteCommand);

    logger.info('File deleted from S3 successfully', {
      key,
      bucket: BUCKET_NAME,
    });

    return {
      success: true,
    };
  } catch (error: any) {
    logger.error('S3 delete failed', {
      error: error.message,
      key,
      bucket: BUCKET_NAME,
    });

    return {
      success: false,
      error: error.message,
    };
  }
}
